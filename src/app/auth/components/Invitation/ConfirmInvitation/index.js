import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import queryString from "query-string";
import { connect } from "react-redux";

import { toast } from "@component/ToastProvider";

import { confirmInvatation } from "@src/app/services/User";

import { LINK } from "@link";

import * as authRedux from "@src/ducks/auth.duck";
import * as workspaceRedux from "@src/ducks/workspace.duck";

function ConfirmInvitation({ user, ...props }) {
  const { t } = useTranslation();
  const location = useLocation();
  const { organizationId, accessToken } = queryString.parseUrl(location.search)?.query;
  const navigate = useNavigate();

  useEffect(() => {
    onConfirm();
  }, []);

  async function onConfirm() {
    const apiResponse = await confirmInvatation(accessToken, { organizationId });

    let toastMessage = "";
    const { email: userEmail } = user || {};
    const { code, email: apiEmail, message } = apiResponse || {};
    const isSuccess = apiResponse?.code === 200;

    if (userEmail === apiEmail) {
      props.setUser({ ...user, ...apiResponse });
      props.getAvailableWorkspaces();
      toastMessage = isSuccess ? t("COMPLETE_CONFIRMATION_INVITATION") : message;
    } else {
      if (userEmail) props.logout(); //đang đăng nhập tài khoản khác thì logout

      toastMessage = isSuccess
        ? t("EMAIL_COMPLETE_CONFIRMATION_INVITATION").format(apiEmail)
        : code === 404
          ? message
          : t("INVITATION_EMAIL_EXPIRED").format(apiEmail);
    }

    isSuccess ? toast.success(toastMessage) : toast.error(toastMessage);
    navigate(LINK.HOMEPAGE);
  }

  return;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = { ...workspaceRedux.actions, ...authRedux.actions };

export default (connect(mapStateToProps, mapDispatchToProps)(ConfirmInvitation));

