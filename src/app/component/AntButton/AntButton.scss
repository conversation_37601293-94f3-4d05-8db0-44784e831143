@use "sass:map";

$colors: "white", "blue", "navy", "yellow", "green", "purple", "pink", "red";
$colorTypes: "light", "deep", "ghost";

$btnSize: (
        sm: 32px,
        compact: 28px,
        xsmall: 24px,
        mini: 20px,
        tiny: 16px
);
$btnSizeSpace: (
        compact: 8px,
        xsmall: 4px,
        mini: 4px,
        tiny: 4px
);


@mixin generate-white-button-styles($class-name, $base-color) {
  &.#{$class-name} {
    &:not(:disabled):not(.ant-btn-disabled) {
      color: var(#{$base-color});
      border-color: #FFF;
      background: #FFF;

      .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
        stroke: var(#{$base-color});
      }

      .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
        fill: var(#{$base-color});
      }

      &:hover {
        background: var(#{$base-color}-light-1);
        border-color: var(#{$base-color}-light-1);
      }

      &:active {
        color: #FFF;
        background: var(#{$base-color});
        border-color: var(#{$base-color});

        .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
          stroke: var(--white);
        }

        .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
          fill: var(--white);
        }
      }
    }
  }
}

:root {
  --black: #000;
  --white: #FFF;

  --blue: #2196F3;
  --blue-light-1: #F2FAFF;
  --blue-light-2: #E1F4FF;
  --blue-dark: #0E6DB9;

  --navy: #09196B;
  --navy-light-1: #EAEDFF;
  --navy-light-2: #BCC7FF;
  --navy-dark: #031B9A;

  --cobalt: #3A18CE;
  --cobalt-light-1: #E4D9FF;
  --cobalt-light-2: #C1A8FF;
  --cobalt-dark: #280FA2;

  --yellow: #FFB800;
  --yellow-light-1: #FFF1CD;
  --yellow-light-2: #FFD361;
  --yellow-dark: #E0A91A;

  --green: #3FDB90;
  --green-light-1: #DDFFEF;
  --green-light-2: #C4FDE2;
  --green-dark: #2ABD76;

  --purple: #9B6EFB;
  --purple-light-1: #F4EFFF;
  --purple-light-2: #E9DFFF;
  --purple-dark: #794FD2;

  --pink: #FF4E83;
  --pink-light-1: #FFE5ED;
  --pink-light-2: #FFD2E0;
  --pink-dark: #D93264;

  --red: #E70C0C;
  --red-light-1: #FFDCDC;
  --red-light-2: #FAC9C9;
  --red-dark: #C52222;


  @each $color in $colors {
    // light color
    --btn-light-#{$color}-color: var(--#{$color});
    --btn-light-#{$color}-bg-color: var(--#{$color}-light-1);
    --btn-light-#{$color}-border-color: var(--#{$color}-light-1);

    --btn-light-#{$color}-hover-color: var(--white);
    --btn-light-#{$color}-hover-bg-color: var(--#{$color}-light-2);
    --btn-light-#{$color}-hover-border-color: var(--#{$color}-light-2);

    --btn-light-#{$color}-active-color: var(--white);
    --btn-light-#{$color}-active-bg-color: var(--#{$color});
    --btn-light-#{$color}-active-border-color: var(--#{$color});

    // deep color
    --btn-deep-#{$color}-color: var(--white);
    --btn-deep-#{$color}-bg-color: var(--#{$color});
    --btn-deep-#{$color}-border-color: var(--#{$color});

    --btn-deep-#{$color}-hover-color: var(--white);
    --btn-deep-#{$color}-hover-bg-color: var(--#{$color}-dark);
    --btn-deep-#{$color}-hover-border-color: var(--#{$color}-dark);

    --btn-deep-#{$color}-active-color: var(--white);
    --btn-deep-#{$color}-active-bg-color: var(--#{$color}-dark);
    --btn-deep-#{$color}-active-border-color: var(--#{$color}-dark);

    // ghost color
    --btn-ghost-#{$color}-color: var(--#{$color});
    --btn-ghost-#{$color}-bg-color: transparent;
    --btn-ghost-#{$color}-border-color: var(--#{$color});

    --btn-ghost-#{$color}-hover-color: var(--#{$color});
    --btn-ghost-#{$color}-hover-bg-color: var(--#{$color}-light-1);
    --btn-ghost-#{$color}-hover-border-color: var(--#{$color});

    --btn-ghost-#{$color}-active-color: var(--white);
    --btn-ghost-#{$color}-active-bg-color: var(--#{$color});
    --btn-ghost-#{$color}-active-border-color: var(--#{$color});
  }
}

.line-clamp {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}


.ant-btn {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  max-width: 100%;

  .ant-btn-icon {
    display: flex;
  }

  &:disabled {
    .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
      stroke: var(--white);
    }

    .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
      fill: var(--white);
    }
  }

  &.ant-btn-lg {
    font-weight: 600;
  }

  // set button size
  @each $btn-size, $size-value in $btnSize {
    &.ant-btn-#{$btn-size} {
      height: $size-value;
      padding: 0 8px;

      &:not(.ant-btn-circle) {
        border-radius: 4px;
      }

      &.ant-btn-icon-only {
        width: $size-value;
        min-width: $size-value;
        padding: 0;
      }

      &.ant-btn-full-icon .ant-btn-icon svg {
        width: $size-value;
        height: $size-value;
      }
    }
  }

  .ant-btn-icon svg, .ant-btn-icon img {
    width: 16px;
    height: 16px;

    path {
      transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    }
  }

  .ant-btn-label {
    white-space: break-spaces;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: left;
    overflow-wrap: anywhere;
  }

  @each $type in $colorTypes {
    @each $color in $colors {
      &.ant-btn-#{$type}-#{$color} {
        &:not(:disabled):not(.ant-btn-disabled) {
          color: var(--btn-#{$type}-#{$color}-color);
          border-color: var(--btn-#{$type}-#{$color}-border-color);
          background: var(--btn-#{$type}-#{$color}-bg-color);

          .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
            stroke: var(--btn-#{$type}-#{$color}-color);
          }

          .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
            fill: var(--btn-#{$type}-#{$color}-color);
          }

          &:hover {
            color: var(--btn-#{$type}-#{$color}-hover-color);
            background-color: var(--btn-#{$type}-#{$color}-hover-bg-color);
            border-color: var(--btn-#{$type}-#{$color}-hover-border-color);

            .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
              stroke: var(--btn-#{$type}-#{$color}-hover-color);
            }

            .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
              fill: var(--btn-#{$type}-#{$color}-hover-color);
            }
          }

          &:active {
            color: var(--btn-#{$type}-#{$color}-active-color);
            background-color: var(--btn-#{$type}-#{$color}-active-bg-color);
            border-color: var(--btn-#{$type}-#{$color}-active-border-color);

            .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
              stroke: var(--btn-#{$type}-#{$color}-active-color);
            }

            .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
              fill: var(--btn-#{$type}-#{$color}-active-color);
            }
          }
        }
      }
    }
  }


  &.ant-btn-white {
    &:not(:disabled):not(.ant-btn-disabled) {
      color: var(--primary-colours-blue-navy);
      border-color: #FFF;
      background: #FFF;

      .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
        stroke: var(--primary-colours-blue-navy);
      }

      .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
        fill: var(--primary-colours-blue-navy);
      }

      &.ant-btn-bordered, &.ant-btn-bordered:hover:active {
        border: 1px solid var(--background-light-background-grey) !important;
      }

      &:hover {
        background: var(--navy-light-1);
        border-color: var(--navy-light-1);
      }

      &:active, &.ant-dropdown-open {
        color: #FFF;
        background: var(--navy);
        border-color: var(--navy);

        .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
          stroke: var(--white);
        }

        .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
          fill: var(--white);
        }
      }
    }
  }

  /*&.ant-btn-ghost-blue {
    &:not(:disabled):not(.ant-btn-disabled) {
      color: var(--blue);
      border-color: var(--blue);
      background: transparent;

      .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
        stroke: var(--blue);
      }

      .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
        fill: var(--blue);
      }

      &:hover {
        background: var(--blue-light-2);
      }

      &:active, &.ant-dropdown-open {
        color: #FFF;
        background: var(--blue);

        .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
          stroke: var(--white);
        }

        .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
          fill: var(--white);
        }
      }
    }
  }*/

  &.ant-btn-ghost-white {
    &:not(:disabled):not(.ant-btn-disabled) {
      color: var(--black);
      border-color: transparent;
      background: transparent;

      .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
        stroke: var(--black);
      }

      .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
        fill: var(--black);
      }

      &:hover {
        color: var(--navy);
        border-color: var(--navy);

        .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
          stroke: var(--navy);
        }

        .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
          fill: var(--navy);
        }
      }

      &:active, &.ant-dropdown-open {
        color: #FFF;
        background: var(--navy);
        border-color: var(--navy);

        .ant-btn-icon svg:not(.svg-fill) path, svg:not(.svg-fill) path {
          stroke: var(--white);
        }

        .ant-btn-icon svg.svg-fill path, svg.svg-fill path {
          fill: var(--white);
        }
      }
    }
  }


  @include generate-white-button-styles("ant-btn-white-blue", "--blue");
  @include generate-white-button-styles("ant-btn-white-navy", "--navy");
  @include generate-white-button-styles("ant-btn-white-red", "--red");
  @include generate-white-button-styles("ant-btn-white-cobalt", "--cobalt");
}


