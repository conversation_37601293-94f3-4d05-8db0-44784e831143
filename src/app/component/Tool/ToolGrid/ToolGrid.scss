.tool-item {
  display: flex;
  padding: 24px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 16px;
  background: var(--background-light-background-2);
  gap: 15px;
  box-shadow: var(--shadow-level-2);
  position: relative;

  &:hover {
    background: var(--primary-colours-blue-light-1);
  }

  &:not(:hover) {
    .tool-item__favorite {
      &:not(.favorited) {
        opacity: 0;
      }
    }
  }

  .tool-item__favorite {
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
    z-index: 1;
  }

  .tool-item__content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    width: 100%;

    .tool-item__category {
      color: var(--typo-colours-support-blue-light);
    }

    .tool-item__title {
      color: var(--typo-colours-primary-black);
      font-weight: 600;
      display: flex;
      gap: 16px;
      align-items: flex-start;

      .tool-item__icon {
        width: 24px;
        height: 24px;
      }
    }

    .tool-item__description {
      color: var(--typo-colours-support-blue-light);
      font-size: 13px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      height: 49px;
    }
  }

  .tool-item__footer {
    display: flex;
    flex-direction: column;
    margin-top: auto;
    justify-content: center;
    width: 100%;
    gap: 16px;

    .tool-item__access-tool {
      margin-top: auto;
      height: 48px;
    }

    .tool-item__video-preview {
      color: var(--primary-colours-blue);
      cursor: pointer;
      margin: auto;
    }
  }
}