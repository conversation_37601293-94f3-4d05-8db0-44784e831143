import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Avatar, Button, Dropdown, Input } from "antd";
import { connect } from "react-redux";

import AntAvatar from "../AntAvatar";
import CustomModal from "../CustomModal";
import Loading from "@component/Loading";

import { getAllUserAccess, updateUserAccess } from "@services/Share";
import { getAllGeneralAccess, updateGeneralAccess } from "@services/GeneralAccess";
import { getProjectPermission } from "@services/Project";
import { getUserFolderPermission } from "@services/Folder";
import { findOneUser } from '@services/User';
import { generatePermissionFromAccess } from "@src/common/functionCommons";

import { Validate } from "@src/constants/validate";
import { GENERAL_ACCESS_TYPE, PERMISSION_OPTIONS, PERMISSION, WORKSPACE_TYPE } from "@constant";
import { toast } from "@component/ToastProvider";

import CHEC<PERSON> from "@src/asset/icon/button/check.svg";
import ANY_ONE from "@src/asset/icon/share/anyone.svg";
import HOME from "@src/asset/icon/share/home.svg";
import LOCK from "@src/asset/icon/share/lock.svg";
import SHARE_LINK from "@src/assets/icons/share-link.svg";
import ChevronDown from "@component/SvgIcons/ChevronDown";
import XIcon from "@component/SvgIcons/X-Icon";

import "./Share.scss";

const Share = ({ user, availableWorkspaces, ...props }) => {
  const { disabledEdit = false, isShowModal, handleCancel, handleAfterShare } = props;
  const { queryAccess, name, owner, workspaceId } = props;
  const { t, i18n } = useTranslation();

  const [userAccessData, setUserAccessData] = useState([]);
  const [userAccessDisplay, setUserAccessDisplay] = useState([]);
  const [permission, setPermission] = useState();
  const [isFirst, setFirst] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [isShowSuggestions, setShowSuggestions] = useState(false);
  const [isCopied, setCopied] = useState(false);

  const [generalAccessData, setGeneralAccessData] = useState({
    typeAccess: GENERAL_ACCESS_TYPE.RESTRICTED,
    key: GENERAL_ACCESS_TYPE.RESTRICTED,
  });

  const [emailList, setEmailList] = useState([]);
  const [inputValue, setInputValue] = useState("");

  useEffect(() => {
    if (isShowModal) {
      getAllData();
    }
  }, [isShowModal]);

  useEffect(() => {
    if (isCopied) {
      setTimeout(() => {
        setCopied(false);
      }, 1000);
    }
  }, [isCopied]);

  const organizationWorkspace = availableWorkspaces?.find(workspace => workspace?.type === WORKSPACE_TYPE.ORGANIZATIONAL);
  const isInOrganization = organizationWorkspace?._id === workspaceId;

  const haveInvalidEmail = emailList.some(email => !email?.valid);

  const { isViewer, viewerNotification } = useMemo(() => {
    if (permission && [PERMISSION.VIEWER, PERMISSION.NO_PERMISSION].includes(permission)) {
      if (queryAccess?.folderId) {
        return {
          isViewer: true,
          viewerNotification: t("NOT_HAVE_PERMISSION_SHARE_FOLDER"),
        };
      } else {
        return {
          isViewer: true,
          viewerNotification: t("NOT_HAVE_PERMISSION_SHARE_PROJECT"),
        };
      }
    }
    return { isViewer: false };
  }, [permission]);

  const permissionShares = disabledEdit ? ["VIEWER"] : ["EDITOR", "VIEWER"];

  //-------------Call API--------------------------------------------
  const getAllData = async () => {
    setFirst(true);
    const allReques = [getAllUserAccess(queryAccess), getAllGeneralAccess(queryAccess)];
    if (queryAccess?.folderId) {
      allReques.push(getUserFolderPermission(queryAccess?.folderId));
    } else {
      allReques.push(getProjectPermission(queryAccess?.projectId));
    }
    const [userAccess, generalAccess, permissionResponse] = await Promise.all(allReques);
    if (!permissionResponse || permissionResponse?.code === 404) {
      handleCancel();
      return;
    }
    setPermission(permissionResponse.permission);
    if (userAccess) {
      const allUserAccess = [{
        key: owner?._id,
        userId: owner,
      }, ...userAccess]
      setUserAccessData(allUserAccess);
      setUserAccessDisplay(JSON.parse(JSON.stringify(allUserAccess)));
    }
    if (generalAccess.length) {
      setGeneralAccessData(generalAccess[0]);
    }
    setFirst(false);
  }

  //Get the user access data that has been changed
  const getChangeUserAccessData = (accessData = [], newUsersAccess = []) => {
    const updatedData = accessData.flatMap(shareAccess => {
      if (shareAccess.isChange) {
        return { userId: shareAccess.userId._id, permission: shareAccess.permission };
      } else if (shareAccess.isDeleted) {
        return shareAccess;
      } else {
        return [];
      }
    });
    const createdData = newUsersAccess.filter(item => !item.isDeleted);
    return {
      ...queryAccess,
      data: [...updatedData, ...createdData],
    };
  };

  //handle submit
  const onOk = async () => {
    setLoading(true);
    const userAccessData = getChangeUserAccessData(userAccessDisplay, emailList);
    const isOrganization = generalAccessData.typeAccess === GENERAL_ACCESS_TYPE.ORGANIZATIONAL;
    const generalAccessDataRequest = {
      ...queryAccess,
      ...generalAccessData?.permission ? { permission: generalAccessData?.permission } : {},
      ...isOrganization ? { organizationId: user?.organizationId?._id } : {},
    }
    const params = { workspaceId: workspaceId }

    const allRequest = [
      updateGeneralAccess(generalAccessDataRequest, generalAccessData?.typeAccess, params),
      ...(userAccessData?.data.length ? [updateUserAccess(userAccessData, params)] : [])
    ];
    const dataResponse = await Promise.all(allRequest);
    if (dataResponse.length === allRequest.length) {
      toast.success("SHARE_SUCCESS");
      if (handleAfterShare) {
        const newPermission = generatePermissionFromAccess(userAccessDisplay, generalAccessData, user);
        await handleAfterShare(newPermission);
      }
      setEmailList([]);
      setInputValue("");
      handleCancel();
    }
    setLoading(false);
  };

  // handle cancel share
  const onCancel = () => {
    setEmailList([]);
    setInputValue("");
    handleCancel();
  }

  const handleChangePermissionUser = (event, index) => {
    const updatedUserAccessDisplay = JSON.parse(JSON.stringify(userAccessDisplay));

    if (event.key === PERMISSION_OPTIONS.REMOVE_ACCESS) {
      updatedUserAccessDisplay[index].isDeleted = true;
    } else {
      updatedUserAccessDisplay[index].permission = event.key;
      updatedUserAccessDisplay[index].isChange = event.key !== userAccessData[index].permission;
    }

    setUserAccessDisplay(updatedUserAccessDisplay);
  };

  const handleChangePermissionGeneralAccess = (event) => {
    setGeneralAccessData(prevData => {
      return { ...prevData, permission: event.key, };
    });
  };

  const generateDescription = (type, permission) => {
    const { RESTRICTED, ANYONE_WITH_LINK } = GENERAL_ACCESS_TYPE;
    const { EDITOR } = PERMISSION_OPTIONS;
    switch (type) {
      case RESTRICTED:
        return t('ONLY_PEOPLE_WITH_ACCESS_LINK');
      case ANYONE_WITH_LINK:
        return permission === EDITOR ? t('ANYONE_WITH_LINK_EDIT') : t('ANYONE_WITH_LINK_VIEW');
      default:
        return permission === EDITOR ? t('ORGANIZATIONAL_EDIT') : t('ORGANIZATIONAL_VIEW');
    }
  };

  const handleChangeGeneralAccess = (e) => {
    if (e.key !== generalAccessData?.typeAccess) {
      if (e.key === GENERAL_ACCESS_TYPE.RESTRICTED) {
        setGeneralAccessData({ typeAccess: e.key, key: e.key });
      } else {
        setGeneralAccessData({ typeAccess: e.key, permission: PERMISSION_OPTIONS.VIEWER, key: e.key });
      }
    }
  };

  //----Handling functions for adding new emails ------------------
  const checkEmailExists = value => {
    return emailList.some(email => email?.email === value);
  };

  const handleAddEmail = async () => {
    const trimValue = inputValue.trim();
    if (!trimValue) {
      return;
    }

    if (checkEmailExists(trimValue)) {
      setInputValue("");
      return;
    }

    let isValidEmail = !Validate.EMAIL(trimValue);

    if (!isValidEmail) {
      toast.error("EMAIL_ADDRESS_NOT_VALID");
    } else {
      const users = await findOneUser({ email: trimValue });
      if (!users) {
        isValidEmail = false;
        toast.error("EMAIL_ADDRESS_DOES_NOT_EXIST");
      }
    }

    const newEmail = {
      email: trimValue,
      permission: PERMISSION_OPTIONS.VIEWER,
      valid: isValidEmail,
    };

    setEmailList((prevEmailList) => [...prevEmailList, newEmail]);
    setInputValue("");
    setShowSuggestions(false);
  };

  const handleDeleteEmail = (index) => {
    setEmailList(emailList.filter((_, i) => i !== index));
  };

  const onChangeInput = e => {
    const value = e.target.value;
    setInputValue(value);
    const isValid = !Validate.EMAIL_REQUIRED(value);
    setShowSuggestions(isValid);
  };

  const handleChangeEmailPermission = (e, index) => {
    const updatedEmailList = [...emailList];
    updatedEmailList[index] = { ...updatedEmailList[index], permission: e.key };
    setEmailList(updatedEmailList);
  };

  const renderNewEmailList = useMemo(() => {
    return emailList
      .filter(item => !item?.isDeleted)
      .map((email, index) => {
        const emailItemClassName = email?.valid ? "valid-email" : "invalid-email";

        const permissionItems = permissionShares.map(permission => {
          return {
            label: t(permission),
            key: permission,
          };
        });

        return (
          <div className="add-email__email-item" key={index}>
            <div className={`email-item__email ${emailItemClassName}`}>
              <span>{email?.email}</span>
              <div className="email-item__remove_icon" onClick={() => handleDeleteEmail(index)}>
                <XIcon />
              </div>
            </div>
            <Dropdown
              className="modal-share__action"
              disabled={!email?.valid}
              menu={{
                className: "action__menu",
                items: permissionItems,
                onClick: e => handleChangeEmailPermission(e, index),
              }}
              trigger={["click"]}
              placement="bottomRight"
            >
              <Button>
                {t(email?.permission)}
                <ChevronDown />
              </Button>
            </Dropdown>
          </div>
        );
      });
  }, [emailList, i18n.language, permissionShares]);
  //----------------------------------------------

  const renderGeneralAccessList = useMemo(() => {

    const isRestricted = generalAccessData?.typeAccess === GENERAL_ACCESS_TYPE.RESTRICTED;
    const isAnyOne = generalAccessData?.typeAccess === GENERAL_ACCESS_TYPE.ANYONE_WITH_LINK;
    const isOrganization = !isRestricted && !isAnyOne;

    const avatar = isRestricted ? LOCK : isAnyOne ? ANY_ONE : HOME;
    const title = isOrganization ? user?.organizationId?.name : t(generalAccessData?.typeAccess);
    const description = generateDescription(generalAccessData?.typeAccess, generalAccessData?.permission);

    const permissionItems = permissionShares.map(permission => ({
      label: t(permission),
      key: permission,
    }));

    const dropdownMenu = {
      className: "action__menu",
      items: permissionItems,
      onClick: (e) => handleChangePermissionGeneralAccess(e),
    };
    return (
      <div className="modal-share__access-item">
        <div className="gap-2 flex">
          <Avatar className="avatar-general-access" size="large" style={{
            backgroundImage: 'linear-gradient(180deg, #6EBCFB 0%, #2196F3 100%)',
            backgroundSize: 'cover',
            border: 0
          }}>
            <img src={avatar} alt="" />
          </Avatar>
          <div className="access-item__info general-access">
            <div className="item__info__title">{title}</div>
            <div className="access-item__info__desctiption">{description}</div>
          </div>
        </div>
        {generalAccessData?.permission && (
          <Dropdown className="modal-share__action" menu={dropdownMenu} trigger={["click"]} placement="bottomRight">
            <Button>
              {t(generalAccessData.permission)}
              <ChevronDown />
            </Button>
          </Dropdown>
        )}
      </div>
    );
  }, [generalAccessData, user, i18n.language, permissionShares]);

  const generalAccessItemsDisplay = useMemo(() => {
    let listItem = [
      { label: t(GENERAL_ACCESS_TYPE.RESTRICTED), key: GENERAL_ACCESS_TYPE.RESTRICTED },
      { label: t(GENERAL_ACCESS_TYPE.ANYONE_WITH_LINK), key: GENERAL_ACCESS_TYPE.ANYONE_WITH_LINK }
    ];
    if (user.organizationId && isInOrganization) {
      listItem = [
        listItem[0],
        {
          label: user?.organizationId?.name,
          key: GENERAL_ACCESS_TYPE.ORGANIZATIONAL
        },
        listItem[1]
      ];
    }
    return listItem;
  }, [user, i18n.language]);

  const renderPeopelAccessList = useMemo(() => {
    return userAccessDisplay.map((userAccess, index) => {
      if (!userAccess?.userId || userAccess.isDeleted) {
        return null;
      }
      const isCurrentUser = userAccess?.userId?._id === user?._id;
      const permissionItems = Object.values(PERMISSION_OPTIONS)
        .filter((permission) => disabledEdit && permission !== PERMISSION_OPTIONS.EDITOR)
        .map((permission => {
          return {
            label: t(permission),
            key: permission,
            danger: permission === PERMISSION_OPTIONS.REMOVE_ACCESS
          };
        }));

      return (
        <div className="modal-share__access-item" key={userAccess?.key}>
          <div className="gap-2 flex items-center">
            <AntAvatar name={userAccess?.userId?.fullName} shape="circle" />
            <div className="access-item__info">
              <div>
                {`${userAccess?.userId?.fullName} ${isCurrentUser ? `(${t("YOU")})` : ""}`}
              </div>
              <div className="access-item__info__email">
                {userAccess?.userId?.email}
              </div>
            </div>
          </div>
          {!userAccess?.permission ? (
            <div className="access-item__owner">{t("OWNER")}</div>
          ) : (isViewer ? (
            <div className="access-item__owner">{t(userAccess?.permission)}</div>
          ) :
            (
              <Dropdown
                className="modal-share__action"
                menu={{
                  className: "action__menu",
                  items: permissionItems,
                  onClick: (e) => handleChangePermissionUser(e, index),
                }}
                trigger={["click"]}
                placement="bottomRight"
              >
                <Button>
                  {t(userAccess?.permission)}
                  <ChevronDown />
                </Button>
              </Dropdown>
            ))}
        </div>
      );
    });
  }, [userAccessDisplay, user, i18n.language, disabledEdit]);

  const handleCopyLink = () => {
    if (!isCopied) {
      navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      toast.success("LINK_COPIED");
    }
  };

  return <CustomModal
    width={400}
    isShowModal={isShowModal}
    handleOk={onOk}
    handleCancel={onCancel}
    className="modal-share"
    title={`${t("SHARE")} "${name}"`}
    loadingOkButton={isLoading}
    disableOkButton={isFirst || isViewer || inputValue || haveInvalidEmail}
  >
    {isFirst ? <Loading active className="modal-share__loading" /> :
      (isViewer ? <div className="modal-share__no-permission">{viewerNotification}</div>
        : <>
          <div className="modal-share__add-email">
            {renderNewEmailList}
            <div className="add-email__input-wrapper">
              <Input
                placeholder={t("SHARE_PLACEHOLDER")}
                onPressEnter={handleAddEmail}
                onChange={onChangeInput}
                value={inputValue}
              />
              {isShowSuggestions && <div className="add-email__suggestion" onClick={handleAddEmail}>{inputValue}</div>}
            </div>
          </div>

          <div className="modal-share__access">
            <div>{t("GENERAL_ACCESS")}</div>
            {!isViewer && <Dropdown
              className="modal-share__access__action"
              menu={{
                className: "action__menu",
                items: [...generalAccessItemsDisplay],
                selectedKeys: [generalAccessData?.typeAccess],
                onClick: handleChangeGeneralAccess,
              }}
              trigger={["click"]}
            >
              <Button>
                <ChevronDown />
              </Button>
            </Dropdown>}
          </div>

          {renderGeneralAccessList}

          {
            !!userAccessDisplay.length && <>
              <div className="modal-share__access">{t("PEOPLE_WITH_ACCESS")}</div>
              {renderPeopelAccessList}
            </>
          }

          <div className="modal-share__copy-link">
            <div>{t("SHARE_LINK")}</div>
            <Button
              className="copy-link-btn"
              onClick={handleCopyLink}
            >
              <img src={SHARE_LINK} alt="" />
              {isCopied ? t("COPIED") : t("COPY_LINK")}
            </Button>
          </div>
        </>)
    }

  </CustomModal>;
};


function mapStateToProps(store) {
  const { user } = store.auth;
  const { availableWorkspaces } = store.workspace;
  return { user, availableWorkspaces };
}

export default connect(mapStateToProps)(Share);
