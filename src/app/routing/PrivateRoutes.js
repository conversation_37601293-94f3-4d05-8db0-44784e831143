import { useMemo } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import { connect } from "react-redux";

import Layout from "@app/layout";
import StudentLayout from "@app/layout/StudentLayout";
import Error404 from "@app/pages/Error/404";
import AdminPageRouter from "./AdminPageRouter";

import { ROUTERS } from "@src/constants/routers";
import { LINK } from "@link";
import { USER_TYPE } from "@constant";

const PrivateRoutes = ({ user }) => {
  
  const isStudent = useMemo(() => user.type === USER_TYPE.STUDENT, [user]);
  
  const routerData = useMemo(() => {
      let routers = [];
      
      let menuData = [...ROUTERS];
      while (menuData.length) {
        const menuItem = menuData.shift();
        if (menuItem.element) {
          if(!menuItem?.forStudent && isStudent) continue;
          routers.push(<Route key={menuItem.path} path={menuItem.path} element={menuItem.element} />);
        }
        if (Array.isArray(menuItem.subMenu)) menuData = [...menuData, ...menuItem.subMenu];
        if (Array.isArray(menuItem.child)) menuData = [...menuData, ...menuItem.child];
      }
      return routers;
      
    }, [],
  );
  
  const defaultRoute = useMemo(() => isStudent ? LINK.WRITING : LINK.WELCOME, [isStudent]);
  
  if (!routerData) return null;
  
  return (
    <Routes>
      <Route element={isStudent ? <StudentLayout /> : <Layout />}>
        {/* navigate after login */}
        <Route path="auth/*" element={<Navigate to={defaultRoute} />} />
        <Route path={`${LINK.ADMIN_PAGE}/*`} element={<AdminPageRouter />} />
        {routerData}
        <Route path={LINK.ERROR_404} element={<Error404 />} />
        {/* Page Not Found */}
        <Route path="*" element={<Navigate to={LINK.ERROR_404} replace />} />
      </Route>
    </Routes>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default (connect(mapStateToProps, {})(PrivateRoutes));