import axios from "axios";
import { API } from "@api";
import {
  createBase,
  deleteBase,
  getAllBase,
  updateBase,
  getAllManager,
  getDetailBase,
  getAllPaginationBase,
} from "@services/Base";

export function getOptions(query, loading) {
  return getAllBase(API.OPTION, query);
}

export function getAllOptions(query) {
  return getAllBase(API.OPTION, query);
}

export function getPaginationOptions(paging, query, populateOpts = [], loading) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.OPTION, paging, query, ["name"]);
}

export function createOption(data, toastError = false) {
  return createBase(API.OPTION, data, [], false, toastError);
}

export function updateOption(data, toastError = false) {
  return updateBase(API.OPTION_ID, data, [], false, toastError);
}

export function deleteOption(id, toastError = false) {
  return deleteBase(API.OPTION_ID, id, false, toastError);
}

export function getDataset(query) {
  return getAllBase(API.DATASET, query);
}

export function getOptionById(id) {
  return getDetailBase(API.OPTION_ID, id);
}