import axios from "axios";
import { API } from "@api";

export function createForm(data) {
  return axios
    .post(`${API.CREATE_GOOGLE_FORM}`, (data), { loading: true, hideNoti: true })
    .then(response => response?.data || { success: false })
    .catch(() => null);
}

export function oauthGoogleForm(data) {
  return axios.post(`${API.AUTH_GOOGLE_FORM}`, data)
              .then(response => {
                if (response?.status === 200) return response?.data;
                return null;
              })
              .catch((err) => {
                // renderMessageError(err);
                return null;
              });
}