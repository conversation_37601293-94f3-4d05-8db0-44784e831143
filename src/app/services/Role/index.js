import { API } from '@api';
import { createBase, deleteBase, getAllBase, getAllPaginationBase, getDetailBase, updateBase } from '@services/Base';

export function getPaginationRoles(paging, query, loading) {
  return getAllPaginationBase(API.ROLES, paging, query, ['name', 'code'], null, loading);
}

export function getAllRoles(query, loading) {
  return getAllBase(API.ROLES, query, null, loading);
}

export function deleteRole(id) {
  return deleteBase(API.ROLES_ID, id);
}

export function createRole(data) {
  return createBase(API.ROLES, data);
}

export function updateRole(id, data) {
  return updateBase(API.ROLES_ID.format(id), data);
}

export function getRole(id) {
  return getDetailBase(API.ROLES_ID, id, ['permissionIds']);
}
