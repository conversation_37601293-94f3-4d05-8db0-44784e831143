import { API } from "@api";

import {
  createBase,
  deleteBase,
  getAllBase,
  updateBase,
  getAllManager,
  getDetailBase,
  getAllPaginationBase,
} from "@services/Base";
import axios from "axios";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";

export function getInvitationUser(paging, query) {
  return getAllBase(API.INVITATION, paging, query, ["fullName"]);
}

export function invitationMember(data) {
  return createBase(API.INVITATION, data, [], false, true);
}

export function resendInvitationMember(data) {
  return createBase(API.RESEND_INVITATION, data, [], false, true);
}

export function deleteMember(id) {
  return deleteBase(API.INVITATION_ID, id, false, true);
}

export function updateInvitationMember(data) {
  return updateBase(API.INVITATION_ID, data, [], false, true);
}