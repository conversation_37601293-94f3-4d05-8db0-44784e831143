import { getAllPaginationBase, updateBase } from "@services/Base";
import { API } from "@api";
import axios from "axios";
import { cloneObj } from "@src/common/functionCommons";

export const moveToWhiteList = (_id) => {
  return updateBase(API.WAIT_LIST_TO_WHITELIST, { _id });
}

export const getWaitListPagination = async (query = {}) => {
  const queryObj = cloneObj(query);
  queryObj.page ||= 1;
  queryObj.limit ||= 0;
  return axios
    .get(API.WAIT_LIST_MANAGER, { params: queryObj })
    .then((response) => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
}