import { API } from "@api";
import { createBase, deleteBase, getAllBase, getBase, updateBase, getParamsBase } from "../Base";
import axios from "axios";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";

export function getAllTemplate(query) {
  return getAllBase(API.TEMPLATE, query);
}

export function getWelcomeTemplate() {
  return getBase(API.TEMPLATE_WELCOME);
}

export function getTemplateAvailable(query) {
  return getBase(API.TEMPLATE_AVAILABLE, query);
}

export function updateTemplate(data) {
  return updateBase(API.TEMPLATE_ID, data, []);
}

export function updateTemplateNewContent(data) {
  return updateBase(API.TEMPLATE_NEW_CONTENT, data, []);
}

export function deleteTemplate(id) {
  return deleteBase(API.TEMPLATE_ID, id);
}

export function createProjectFromTemplate(data) {
  return createBase(API.CREATE_PROJECT_FROM_TEMPLATE, data);
}

export function createTemplate(data) {
  return createBase(API.TEMPLATE, data);
}

export function uploadFileExtractText(templateId, file, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();
  formData.append("templateId", templateId);
  formData.append("file", file);
  return axios.post(API.TEMPLATE_UPLOAD_THUMBNAIL, formData, config)
    .then(response => {
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    })
    .catch(err => {
      console.log("err", err);
      return null;
    });
}

export function getExamTemplate(query) {
  return getParamsBase(API.GET_EXAM_TEMPLATE, query)
}
export function getAvaiableExamTemplate(query) {
  return getBase(API.GET_AVAILABLE_EXAM_TEMPLATE, query)
}