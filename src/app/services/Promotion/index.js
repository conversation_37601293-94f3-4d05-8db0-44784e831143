import axios from "axios";
import { API } from "@api";
import { getAllPaginationBase, getBase, createBase, updateBase, deleteBase } from "../Base";


export const getAllPromotion = (paging, query, searchFields, populateOpts) => {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.PROMOTION, paging, query, searchFields, populateOpts);
}

export const createPromotion = (data) => {
  return createBase(API.PROMOTION, data);
}

export const updatePromotion = (data) => {
  return updateBase(`${API.PROMOTION_ID}/manager`, data);
}

export const deletePromotion = (id) => {
  return deleteBase(API.PROMOTION_ID, id);
}

export const getAvailablePromotions = () => {
  return getBase(API.PROMOTION_AVAILABLE);
}