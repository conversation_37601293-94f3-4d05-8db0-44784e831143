import axios from "axios";
import { API } from "@api";
import {
  createBase,
  deleteBase,
  getAllBase,
  updateBase,
  getDetailBase,
  getAllPaginationBase,
  getAllManager,
} from "@services/Base";

export function getInstructions(query, populateOpts = [], loading) {
  return getAllBase(API.INSTRUCTION, query, populateOpts);
}

export function getAllInstructions(query, loading) {
  return getAllManager(API.INSTRUCTION, query);
}

export function createInstruction(data, toastError = false) {
  return createBase(API.INSTRUCTION, data, [], false, toastError);
}

export function getInstructionDetail(id) {
  return getDetailBase(API.INSTRUCTION_DETAIL_ID, id);
}

export function updateInstruction(data, toastError = false) {
  return updateBase(API.INSTRUCTION_ID, data, [], false, toastError);
}

export function deleteInstruction(id, toastError = false) {
  return deleteBase(API.INSTRUCTION_ID, id, false, toastError);
}

export async function getPaginationInstruction(paging, query, populateOpts = [], loading) {
  query.sort = query.sort ? query.sort : "-createdAt";
  return getAllPaginationBase(API.INSTRUCTION, paging, query, ["shortName"], ["optionIds", "outputTypeId"]);
}

export function getPromptInstruction(id) {
  return getDetailBase(API.PROMPT_INSTRUCTION, id);
}

export const copyInstruction = (data, toastError = false) => {
  return createBase(API.COPY_INSTRUCTION, data, [], false, toastError);
};