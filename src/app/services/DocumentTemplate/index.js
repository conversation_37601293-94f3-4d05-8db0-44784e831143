import { API } from "@api";
import {
  createBase,
  deleteBase,
  getAllBase,
  getAllPaginationBase,
  getBase,
  getDetailBase,
  joinParams,
  updateBase,
} from "@services/Base";
import axios from "axios";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { genPopulateParam, getFileExtension } from "@common/functionCommons";
import fileDownload from "js-file-download";

export function getAllDocumentTemplate(query) {
  return getAllBase(API.DOCUMENT_TEMPLATE, query);
}

export function getAllDocTemplateForProject(workspaceId) {
  return getBase(API.DOCUMENT_TEMPLATE + `/workspace/${workspaceId}/getAll`);
}

export function getAllOrgDocumentTemplate(orgId, query) {
  return getBase(API.DOCUMENT_TEMPLATE_ORG_ID.format(orgId), query, ["baseTemplateThumbnailId"]);
}

export function previewDocumentTemplate(id) {
  
  const config = { responseType: "arraybuffer" };
  return axios.get(API.STREAM_ID.format(id), config)
              .then(response => {
                if (response.status === 200) {
                  const contentDisposition = response?.headers["content-disposition"];
                  const fileName = contentDisposition?.slice(contentDisposition.indexOf("filename=") + 9).replaceAll("%20", " ");
                  const fileExtension = getFileExtension(fileName);
                  const newBlob = new Blob([response.data], { type: fileExtension === "pdf" ? "application/pdf" : "audio/mpeg" });
                  return URL.createObjectURL(newBlob);
                }
                return null;
              })
              .catch(err => {
                console.log("err", err);
                //renderMessageError(err);
                return null;
              });
}

export function createFileClickeeTemplate(data, config = {}) {
  return axios.post(API.DOC_TEMPLATE_CREATE_PREVIEW_FILE_FOR_ORG, data, config)
              .then(response => {
                if (response?.data) return convertSnakeCaseToCamelCase(response.data);
                return null;
              })
              .catch((err) => err);
}

export function createFileOrgTemplate(data, config = {}) {
  return axios.post(API.DOC_TEMPLATE_PREVIEW_TEMPLATE_FOR_ORG, data, config)
              .then(response => {
                if (response?.data) return convertSnakeCaseToCamelCase(response.data);
                return null;
              })
              .catch((err) => err);
}

export function previewDocTemplateFile(fileName, config = {}) {
  config ||= {};
  config.responseType = "arraybuffer";
  
  return axios.get(API.PREVIEW_DOC_TEMPLATE.format(fileName), config)
              .then(response => {
                if (response.status === 200) {
                  const newBlob = new Blob([response.data], { type: "pdf" });
                  return URL.createObjectURL(newBlob);
                }
                return null;
              })
              .catch(() => null);
}

export function downloadDocTemplateFile(fileName, nameDownload) {
  const config = { responseType: "blob" };
  return axios.get(API.PREVIEW_DOC_TEMPLATE.format(fileName), config)
              .then(response => {
                if (response.data) fileDownload(response.data, nameDownload);
                return null;
              })
              .catch(err => {
                return null;
              });
}

export function getPaginationDocumentTemplate(paging, query) {
  return getAllPaginationBase(API.DOCUMENT_TEMPLATE, paging, query);
}

export function getDocumentTemplateDetail(id) {
  return getDetailBase(API.DOCUMENT_TEMPLATE_ID, id, ["baseTemplateId", "templateId"]);
}

export function editDocumentTemplate(data) {
  return updateBase(API.DOCUMENT_TEMPLATE_ID, data);
}

export function publishDocumentTemplate(data) {
  return axios.post(API.PUBLISH_DOCUMENT_TEMPLATE, (data))
              .then(response => {
                if (response.status === 200) return { success: true };
                return null;
              })
              .catch((err) => null);
  //return createBase(API.PUBLISH_DOCUMENT_TEMPLATE, data);
}

export function deleteDocumentTemplate(id) {
  return deleteBase(API.DOCUMENT_TEMPLATE_ID, id);
}

export function createDocumentTemplate(data) {
  return createBase(API.DOCUMENT_TEMPLATE, data);
}

export function uploadDocumentTemplate(data, file, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();
  
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });
  
  formData.append("fileType", "file");
  formData.append("file", file);
  return axios.post(API.UPLOAD_DOCUMENT_TEMPLATE, formData, config)
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch(err => null);
}

export function uploadOrgDocTemplate(data, file, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();
  
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });
  
  formData.append("fileType", "file");
  formData.append("file", file);
  return axios.post(API.UPLOAD_ORG_DOC_TEMPLATE, formData, config)
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch(err => null);
}



