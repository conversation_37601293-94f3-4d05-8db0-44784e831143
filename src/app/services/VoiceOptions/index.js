import { API } from "@api";
import { createBase, deleteBase, getAllBase, getDetailBase, updateBase } from "@services/Base";
import axios from "axios";

export function getVoiceOptions(query, loading) {
  return getAllBase(API.VOICE_OPTIONS, query);
}

export function getVoiceOptionDetail(id) {
  const populateOpts = ["voiceFileId"];
  return getDetailBase(API.VOICE_OPTION_DETAIL, id, populateOpts);
}

export function createVoiceOption(data) {
  return createBase(API.VOICE_OPTIONS, data);
}

export async function uploadVoice(voiceId, file) {
  const formData = new FormData();
  formData.append("voiceId", voiceId);
  formData.append("file", file);
  const config = {
    headers: { "Content-Type": "multipart/form-data" },
  };
  return await axios
    .post(API.UPLOAD_VOICE, formData, config)
    .then(response => {
      if (response.status === 200) {
        return response.data;
      }
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      // renderMessageError(err);
      return null;
    });
}

export function updateVoiceOption(data) {
  return updateBase(API.VOICE_OPTION_DETAIL, data, [], false);
}

export function deleteVoiceOption(id) {
  return deleteBase(API.VOICE_OPTION_DETAIL, id, false);
}
