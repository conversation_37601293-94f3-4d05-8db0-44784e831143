import { Layout } from "antd";
import { Link } from "react-router-dom";

import HeaderAction from "@app/layout/StudentLayout/Header/HeaderAction";
import HeaderStudentMenu from "./HeaderStudentMenu";

import { LINK } from "@link";

import CLICKEE_LOGO from "@src/asset/logo/clickee-logo.svg";

import "./Header.scss";


const Header = ({ ...props }) => {
  return (<>
    <Layout.Header className="student-header">
      <div className="student-header__left">
        <Link to={LINK.WRITING} className="student-header__logo">
          <img src={CLICKEE_LOGO} alt="" />
        </Link>
        <HeaderStudentMenu />
      </div>
      <div className="student-header__right">
        <HeaderAction />
      </div>
    </Layout.Header>
  </>
  );
};

export default Header;
