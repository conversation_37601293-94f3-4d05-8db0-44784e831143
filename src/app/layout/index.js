import { useEffect, useMemo, useState } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { Layout } from "antd";

import useWindowDimensions from "@common/windowDimensions";

import Aside from "@app/layout/Aside";
import Header from "@app/layout/Header";
import Footer from "@app/layout/Footer";
import DrawerMenu from "@app/layout/DrawerMenu";

import { LINK } from "@link";
import { SCREEN_PC } from "@constant";


const MasterLayout = () => {
  const location = useLocation();
  const { width } = useWindowDimensions();
  
  const [isPageAdmin, setIsPageAdmin] = useState(false);
  
  const isShowAside = useMemo(() => width >= SCREEN_PC, [width]);
  
  useEffect(() => {
    const currentUrl = location.pathname;
    if (currentUrl.includes(LINK.ADMIN_PAGE)) {
      setIsPageAdmin(true);
    } else {
      setIsPageAdmin(false);
    }
  }, [location.pathname]);
  
  const footerStyle = {
    textAlign: "center",
  };
  
  const mainLayout = {
    height: "100vh",
  };
  
  
  return (
    <Layout style={mainLayout}>
      {isShowAside
        ? <Aside />
        : <DrawerMenu>
          <Aside />
        </DrawerMenu>}
      
      <Layout>
        <Header />
        <Layout.Content id="js-layout-content" style={{ flex: 1 }} className="content scrollbar show-scrollbar">
          <Outlet />
        </Layout.Content>
        <Footer style={footerStyle}>Footer</Footer>
      </Layout>
    </Layout>
  );
};

export default MasterLayout;
