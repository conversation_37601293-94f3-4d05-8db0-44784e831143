.share-with-me {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 40px;

  .share-with-me__header {
    display: flex;
    padding: 40px 0px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .share-with-me__title {
      color: #000;
      font-size: 24px;
      font-weight: 600;
      line-height: 20px;
    }

    .share-with-me__description {
      color: #858585;
      text-align: justify;
      font-size: 16px;
      font-weight: 400;
      line-height: 20px
    }
  }

  .share-with-me__body {
    flex: 1;
    display: flex;
    padding: 40px 62px;
    flex-direction: column;
    border-radius: 16px 16px 0px 16px;
    background: var(--background-2nd);
    gap: 40px;
  }
}