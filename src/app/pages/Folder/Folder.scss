:root {
  --content-bg: #ffffff;
  --folder-text-color-heavier: #000;
  --table-cell-border-bottom-color: #ddd;
  --folder-icon-filter: unset;
}

[data-theme="dark"] {
  --content-bg: #1c1a1a;
  --folder-text-color-heavier: #fff;
  --table-cell-border-bottom-color: #343333;
  --folder-icon-filter: invert(100%) sepia(0%) saturate(7435%) hue-rotate(137deg) brightness(115%) contrast(100%);
}

.folder-page {
  display: grid;
  grid-template-columns: repeat(6, minmax(0, 1fr));
  gap: 24px;

  .folder-page__content {
    grid-column: span 5;
  }

  .info-folder-content {
    grid-column: span 1;
  }

  @media screen and (max-width: 1535.98px) {
    grid-template-columns: repeat(4, minmax(0, 1fr));

    .folder-page__content {
      grid-column: span 3;
    }

    .info-folder-content {
      grid-column: span 1;
    }
  }
}

.folder-page__content {

  //height: 100%;
  //padding: 40px 31px;
  //background: var(--content-bg);
  //border-radius: 16px;
  .no-data-folder {
    width: 100%;
  }

  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 32px;

  .content__title {
    color: var(--primary-colours-blue);
    font-size: 32px;
    font-weight: 700;
    line-height: normal;
  }

  .no-project-data {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
    size: 16px;
  }

  .white-page-content {
    width: 100%;
    border-radius: 8px;
    background-color: #ffffff;
    background-clip: content-box;

    .ant-table-tbody>tr {
      cursor: pointer;
    }
  }

  .info-folder-content {
    background-color: #ffffff;
  }

  .content__filter {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;

    @media screen and (max-width: 1200px) {
      flex-direction: column;
      gap: 24px;
    }

    .actions__add-button {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8px;
      border-radius: 8px;
      border: 1px solid var(--primary-colours-blue);
      background: var(--primary-colours-blue);
      height: unset;
    }

    .filter__action-right {
      display: flex;
      align-items: center;
      gap: 24px;
    }

    .filter__actions {
      display: flex;
      align-items: flex-end;

      gap: 16px;

      .filter__actions__search {
        min-width: 384px;

        @media screen and (max-width: 800px) {
          min-width: 100%;
        }

        .ant-btn-icon {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      button {
        span {
          color: var(--folder-text-color-heavier);
          text-align: right;
          font-size: 16px;
          font-weight: 600;
          line-height: 20px;
        }

        img {
          height: 16px;
          width: 16px;
        }
      }

      .actions__add-button {
        gap: 12px;

        span {
          color: #ffffff;
        }
      }

      .actions__dropdown-button {
        img {
          filter: var(--folder-icon-filter);
        }
      }
    }

    .filter__layout-button {
      padding: 12px;
      gap: 10px;
      height: 42px;

      img {
        width: 16px;
        height: 16px;
        filter: var(--folder-icon-filter);
      }
    }
  }

  .folder-table {
    width: 100%;

    .ant-table {
      .ant-table-cell {
        border-color: transparent;
      }

      .ant-table-tbody .ant-table-row {
        position: relative;


        &:after {
          content: "";
          height: 1px;
          position: absolute;
          top: -1px;
          left: 24px;
          right: 24px;
          background: var(--table-cell-border-bottom-color);
        }

        .project-name-cell {
          display: flex;
          flex-direction: row;
          gap: 8px;
          align-items: center;
        }

        .owner-cell {
          display: flex;
          flex-direction: row;
          gap: 8px;
          align-items: center;
          color: var(--typo-colours-support-blue-light);

          .ant-avatar {
            img {
              width: 16px;
              height: 16px;
            }
          }
        }

        .time-cell {
          color: var(--typo-colours-support-blue-light);
        }

        .action-cell {
          display: flex;
          align-items: center;
        }

        &:hover {
          .project-name-cell .star-container {
            opacity: 1;
          }
        }

        &:active {
          &:not(:has(.star-container:active, .ant-dropdown-open, .ant-dropdown-trigger:active)) {
            .ant-table-cell {
              background: var(--primary-colours-blue-navy-light-2);
            }
          }
        }
      }
    }
  }

  .info__folder-menu {
    border: 0 solid #4f4f4f;

    svg {
      margin-inline-end: 8px;
    }
  }
}

.grid-layout {
  grid-template-columns: repeat(6, minmax(0, 1fr));
  gap: 17.6px;

  @media screen and (max-width: 1300px) {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  @media screen and (max-width: 1000px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.grid-layout-table-wraper {
  display: flex;
  border-radius: 8px;
  z-index: 0;
  position: relative;
  gap: 14px;
  background-color: var(--background-light-background-2);
  flex-direction: column;
  box-shadow: var(--shadow-level-2);
  padding: 12px 12px 24px 12px;

  .grid-layout-table__content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    gap: 8px;

    .grid-layout-table__preview {

      width: 100%;
      border-radius: 8px;
      position: relative;
      overflow: hidden;
      aspect-ratio: 156 / 190;
      border: 1px solid var(--background-light-background-grey);

      &:not(:hover) .grid-layout-table__backdrop-hover {
        display: none;
      }

      &:hover .grid-layout-table__backdrop-hover {
        color: var(--white);
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .grid-layout-table__preview-inner {
        object-fit: cover;
        width: 100%;
      }

      &.thumbnail-default {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--white);

        .grid-layout-table__preview-inner {
          height: 80px;
          width: unset;
        }
      }
    }

    .grid-layout-table__info {
      display: flex;
      flex-direction: column;
      word-break: break-word;
      gap: 4px;

      .grid-layout-table__title {
        display: flex;
        align-items: center;
        gap: 4px;

        .grid-layout-table__title-icon{
          width: 12px;
          height: 16px;
        }

        .grid-layout-table__title-text {
          font-size: 16px;
          font-weight: 400;
          line-height: 20px;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          min-height: 1lh;
          color: var(--typo-colours-support-blue-dark)
        }
      }

      .grid-layout-table__last-modified {
        font-size: 14px;
        font-weight: 400;
        color: var(--typo-colours-support-blue-light);
      }

      .grid-layout-table__folder-name {
        font-weight: 400;
        font-size: 14px;
        line-height: 17.5px;
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: var(--typo-colours-support-blue);
      }
    }
  }

  .grid-layout-table__actions {
    padding-top: 4px;
    width: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .grid-layout-table__btnActions {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;

    .project-dropdown {
      width: 16px;
      height: 16px;
      min-width: 16px;

      svg {
        width: 16px !important;
        height: 16px !important;
      }
    }
  }

  .starred-content {
    cursor: pointer;
    display: flex;
  }

  &:hover {
    &::before {
      position: absolute;
      content: '';
      top: 0;
      z-index: -1;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--primary-colours-blue-navy-light-1);

    }

    .star-container {
      opacity: 1;
    }
  }
}