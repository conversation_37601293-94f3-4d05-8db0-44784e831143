.image-topic {
  padding: 16px;
  gap: 8px;
  border-radius: 16px;
  border: 1px solid #DBDBDB;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;

  .image-topic__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .image-topic__title {
      font-weight: 500;
      line-height: 24px;
      color: var(--primary-colours-blue-navy);
    }

    button {
      padding: 4px 8px !important;
      border-radius: 8px !important;
      background-color: #FFDADA !important;
      border: none !important;
      width: 32px !important;

      svg path {
        stroke: #FF0307 !important;
      }
    }
  }

  .image-topic__upload {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    cursor: pointer;

    img {
      margin-bottom: 8px;
      box-shadow: 0px 9.01px 26.85px -2.98px #16345029;
      border-radius: 8.95px;
    }

    .image-topic__upload__title {
      color: #2196F3;
      font-weight: 500;
      line-height: 24px;
    }

    .image-topic__upload__type {
      font-size: 14px;
      line-height: 20px;
      color: #B3B3B3;
    }
  }

  .image-topic__preview {
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .ant-image {
      height: 100%;
      width: auto !important;

      .ant-image-img {
        height: 100% !important;
      }

      .ant-image-mask {
        background: #00000066;

        .image-topic__preview__icon {
          svg {
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    &.uploading {
      .ant-image-mask {
        opacity: 1 !important;

        .image-topic__preview__icon {
          opacity: 0 !important;
        }
      }
    }

    .image-topic__preview__loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80px;
      height: 80px;
    }

    .image-topic__preview__percent {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 16px;
      line-height: 24px;
      color: #FFFFFF;
    }

    .image-topic__preview__backdrop {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
    }
  }

  .image-topic__error {
    height: 108px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    justify-content: center;

    img {
      width: 41px;
      height: 40px;
    }

    font-size: 12px;
    line-height: 16px;
    text-align: center;
    color: #B3B3B3;
  }

  &.image-topic-warning {
    border-color: #FF0307;
    margin-bottom: 32px;
    position: relative;

    .image-topic__warning-text {
      display: flex;
      gap: 8px;
      position: absolute;
      bottom: -32px;
      left: 0;
      font-size: 14px;
      font-weight: 500;
      line-height: 18.2px;
      color: #FF0307;
      align-items: center;
    }
  }
}