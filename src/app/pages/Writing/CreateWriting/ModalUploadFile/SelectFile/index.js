import { useMemo, useRef, useState } from "react";
import clsx from "clsx";
import { useTranslation } from "react-i18next";
import { useDropzone } from 'react-dropzone';
import { v4 as uuidv4 } from 'uuid';

import { uploadFile, uploadImage } from "@services/File";
import { checkFileType, cloneObj } from "@src/common/functionCommons";

import { CONSTANT, UPLOAD_STATUS } from "@constant";

import UPLOAD_ICON from '@src/asset/icon/upload/upload_cloud.svg';

import './SelectFile.scss';

const SelectFile = (props) => {
  const { t } = useTranslation();
  const { imagesDisplay, setImagesDisplay } = props;
  const { pdfDisplay, setPdfDisplay, setPageRangeDisplay, setUploadedPdfId } = props;
  const { setFilesUploadStatus, workspaceId } = props;

  const isDisableImage = useMemo(() => {
    return imagesDisplay.length > 1;
  }, [imagesDisplay]);
  
  const updateUploadStatus = (key, status, percent = 0) => {
    if (!key) {
      setFilesUploadStatus({ status, percent })
    }
    setFilesUploadStatus(prevState => {
      return {
        ...prevState,
        [key]: {
          ...prevState[key],
          status,
          percent
        }
      };
    });
  }

  const deleteUploadStatus = (key) => {
    if (!key) {
      setFilesUploadStatus({});
      return;
    }
    setFilesUploadStatus(prevState => {
      const newState = cloneObj(prevState);
      delete newState[key];
      return newState;
    });
  }

  const addKeyToFiles = (files) => {
    return Array.from(files).map((file) => ({ data: file, key: uuidv4() }));
  }

  // Get files with first type
  const filterFileDrops = (files) => {
    const fileTypeFirst = checkFileType(files[0]);
    const filesWithFirstType = fileTypeFirst === CONSTANT.PDF
      ? [files[0]]
      : Array.from(files).filter(file => checkFileType(file) === CONSTANT.IMAGE);
    return { filteredFiles: filesWithFirstType, fileType: fileTypeFirst };
  }

  const handleUploadPdf = async (file) => {
    setImagesDisplay([]);
    setPageRangeDisplay({ startPage: 0, endPage: 0 });
    setFilesUploadStatus({ status: UPLOAD_STATUS.PENDING, percent: 0 });
    setPdfDisplay(file);
    uploadSingleFile(file, null, CONSTANT.PDF);
  }

  const handleUploadImages = (files) => {
    const limitedFiles = files.slice(0, 2 - imagesDisplay.length);
    const filesWithKey = addKeyToFiles(limitedFiles);
    setImagesDisplay(pre => [...pre, ...filesWithKey]);

    //update upload status
    const newFileUploadStatus = filesWithKey.reduce((acc, file) => (
      { ...acc, [file.key]: { status: UPLOAD_STATUS.PENDING, percent: 0 } }
    ), {});

    if (pdfDisplay) {
      setUploadedPdfId("");
      setPdfDisplay(null);
      setFilesUploadStatus(newFileUploadStatus);
    } else {
      setFilesUploadStatus(prevState => ({ ...prevState, ...newFileUploadStatus }));
    }
    //upload file to server
    filesWithKey.forEach(file => uploadSingleFile(file.data, file.key, CONSTANT.IMAGE));
  }

  async function onDrop(files) {
    if (!files.length) return;

    const { filteredFiles, fileType } = filterFileDrops(files);

    if (fileType === CONSTANT.PDF) {
      handleUploadPdf(filteredFiles[0]);
    } else {
      handleUploadImages(filteredFiles);
    }
  }

  const uploadSingleFile = async (file, key, type) => {
    const onUploadProgress = (progressEvent, key) => {
      const percent = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
      updateUploadStatus(key, UPLOAD_STATUS.UPLOADING, Math.min(percent, 99));
    };

    const axiosConfig = {
      onUploadProgress: (progressEvent) => onUploadProgress(progressEvent, key),
      hideNoti: true
    };
    const fileResponse = type === CONSTANT.IMAGE
      ? await uploadImage(file, workspaceId, axiosConfig, true)
      : await uploadFile(file, { workspaceId, folder: "office", used: false }, axiosConfig);
    if (fileResponse) {
      deleteUploadStatus(key);
      if (type === CONSTANT.IMAGE) {
        setImagesDisplay(prevState => {
          return prevState.map(item => item.key === key ? { ...item, data: fileResponse.data } : item);
        });
      } else {
        setUploadedPdfId(fileResponse._id);
      }
    } else {
      updateUploadStatus(key, UPLOAD_STATUS.ERROR);
    }
  }

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/jpg": [".jpg"],
      "image/jpeg": [".jpeg"],
      "image/png": [".png"],
      "application/pdf": [".pdf"]
    },
    multiple: true,
    onDrop: onDrop,
  });

  return (
    <>
      <div className='writing-select-file' {...getRootProps()}>
        <img src={UPLOAD_ICON} alt='' />
        <div className='file-type'>
          <span className={clsx("file-type__item", { "file-type__item-disable": isDisableImage })}>
            {t("UPLOAD_IMAGE")}
          </span>
          {t("OR")}
          <span className='file-type__item' >pdf</span>
        </div>
        <input {...getInputProps()} />
      </div>
      <span className="notice-images">{`${t("MAXIMUM_IMAGES_OR_PAGES")} : 2`}</span>
    </>
  );
}

export default SelectFile;