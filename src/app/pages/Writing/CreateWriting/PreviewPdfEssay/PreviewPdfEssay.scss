.preview-pdf-essay {
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-pdf-essay-error {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      height: 45px;
    }
  }

  .preview-pdf__content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    justify-content: center;

    .preview-pdf__document {
      height: 300px;
      aspect-ratio: 210/297;
      display: flex;
      justify-content: center;
      position: relative;

      &.preview-pdf__document-result {
        height: 200px;
      }

      &:has(canvas) {
        border: 1px solid #DBDBDB;
      }

      .react-pdf__Page {
        height: 100%;
        width: 100%;

        >* {
          width: 100% !important;
          height: 100% !important;
        }
      }

      .react-pdf__Page__textContent {
        display: none;
      }

      &:not(:hover) {
        .preview-pdf-backdrop {
          &:not(.preview-pdf-backdrop-uploading) {
            display: none;
          }
        }
      }

      .preview-pdf-backdrop {
        cursor: pointer;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #00000066;
        z-index: 10;

        svg {
          width: 16px;
          height: 16px;
          fill: white
        }
      }
    }

    .preview-pdf__page-select {
      display: flex;
      gap: 16px;
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
      color: var(--primary-colours-blue-navy);

      button {
        width: 16px;
        height: 16px;
        min-width: 16px;
      }
    }
  }
}