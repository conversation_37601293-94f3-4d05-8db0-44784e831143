.result-writing__go-back {
  padding: 64px 16px;
  border-radius: 16px;
  border: 1px solid #DBDBDB;
  background: #FAFBFF;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: fit-content;
  box-shadow: 0px 10px 16px 0px #00000029;
  position: relative;
  grid-column: span 2/ span 2;

  .go-back-icon{
    position: absolute;
    top: -56px;
  }

  .go-back-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 312px;
    gap: 16px;
    margin-top: 116px;

    .go-back__btn {
      height: 21px;
    }

    .go-back-content__text {
      line-height: 20.8px;
      text-align: center;
      padding: 0 8px;
    }

    .go-back-content__guide {
      font-weight: 500;
      line-height: 20.8px;
      text-align: center;
      color: var(--primary-colours-blue-navy);
    }
  }
}