
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { LINK } from "@link";
import { BUTTON, CONSTANT } from "@constant";

import AntButton from "@src/app/component/AntButton";

import GO_BACK_ICON from '@src/asset/icon/go-back-icon.png';

import GoBackIcon from '@component/SvgIcons/GoBackIcon';

import './GoBack.scss';

const GoBack = ({ projectData, disabledEdit }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const onGoBack = () => {
    navigate(LINK.WRITING, { state: { projectData } });
  }
  return (
    <div className="result-writing__go-back">
      <img className="go-back-icon" src={GO_BACK_ICON} alt='' />
      <div className="go-back-content" >
        <div className="go-back-content__text">{t("GO_BACK_NOTICE")}</div>
        <div className="go-back-content__guide">{t("GO_BACK_GUIDE")}</div>
        <AntButton
          type={BUTTON.WHITE_COBALT}
          className={"go-back__btn"}
          onClick={onGoBack}
          size='mini'
          icon={<GoBackIcon />}
          iconLocation={CONSTANT.RIGHT}
          disabled={disabledEdit}
        >
          {t("GO_BACK")}
        </AntButton>
      </div>
    </div >
  );
};

export default GoBack;