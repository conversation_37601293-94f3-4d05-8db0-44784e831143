import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import StudentScoreChart from "@component/StudentScoreChart";

import { CONSTANT } from "@constant";
import { toConstantFormat } from "@common/dataConverter";
import { calculateIELTSOverall } from "@src/common/functionCommons";

import "./ResultWritingScore.scss";

function ResultWritingScore({ overallBandScore }) {
  const { t } = useTranslation();

  const [scoreBreakdown, overallScore] = useMemo(() => {
    if (!overallBandScore?.length) return [[], 0];
    const score = overallBandScore.filter(bandScore => toConstantFormat(bandScore.category) !== 'OVERALL').map(bandScore => {
      return { lang: toConstantFormat(bandScore.category), value: bandScore.score };
    });
    const avg = calculateIELTSOverall(score.map(item => item.value));
    return [score, avg];
  }, [overallBandScore]);

  if (!overallBandScore) return null;

  return <div className="result-writing-score-container">
    <div className="result-writing__band-score">
      <div className="result-writing-band-score__score">
        {overallScore}
      </div>
      <div className="result-writing-band-score__label">
        {t("IELTS_BAND_SCORE")}
      </div>
    </div>
    <StudentScoreChart
      chartType={CONSTANT.IELTS}
      label={t("CRITERIA_ASSESSMENT_BAND_SCORE")}
      scoreBreakdown={scoreBreakdown}
    />
  </div>;

}

export default ResultWritingScore;