import { CONSTANT } from '@constant';
import { useTranslation } from 'react-i18next';
import { WRITING_EVALUATION_LIST } from './functionCommon';
import HtmlContent from '@src/app/component/HtmlContent';

const SampleEssay = ({ sampleEssay, evaluationSelected }) => {
  const { t } = useTranslation();

  if (!sampleEssay || !['sampleEssay', CONSTANT.ALL].includes(evaluationSelected)) return null;

  return <div className="evaluation__content__item">
    <div
      className="content__item__title"
      style={{ color: WRITING_EVALUATION_LIST.sampleEssay.color }}
    >
      <img src={WRITING_EVALUATION_LIST.sampleEssay.icon} alt="" />
      {t("SAMPLE_ESSAY")}
    </div>
    <HtmlContent dangerouslySetInnerHTML={{ __html: sampleEssay }} />
  </div>
}

export default SampleEssay;