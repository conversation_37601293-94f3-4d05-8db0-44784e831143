import { calculateIeltsAverage, WRITING_EVALUATION_LIST } from "./functionCommon";
import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";
import { CONSTANT } from "@constant";

const CriteriaAssessment = ({ evaluations, evaluationSelected }) => {
  const { t } = useTranslation();
  if (!evaluations?.length || !['evaluations', CONSTANT.ALL].includes(evaluationSelected)) return null;

  return <div className="evaluation__content__item evaluation-criteria-assessment">
    <div
      className="content__item__title evaluation-task-achievement__title"
      style={{ color: WRITING_EVALUATION_LIST.evaluations.color }}
    >
      <img src={WRITING_EVALUATION_LIST.evaluations.icon} alt="" />
      {t("CRITERIA_ASSESSMENT")}
    </div>
    {Array.isArray(evaluations) ? <div>
      {evaluations.map((evaluation, index) => {
        return (
          <div key={index}>
            <div className="criteria-assessment__item-title">{evaluation.category}</div>

            <ul>
              <li>
                <span className="criteria-assessment__item-title">Band Score:</span>
                {evaluation.score}
              </li>
              <li>
                <span className="criteria-assessment__item-title">Explanation:</span>
                {evaluation.explanation}
              </li>
              <li>
                <span className="criteria-assessment__item-title">How to improve:</span>
                {evaluation.improve}
              </li>
            </ul>
          </div>
        );
      })}
      <li>
        <span className="criteria-assessment__item-title">
          Overall Score: {calculateIeltsAverage(evaluations)}
        </span>
      </li>
    </div>
      : <HtmlContent dangerouslySetInnerHTML={{ __html: evaluations }} />}

  </div>;
};

export default CriteriaAssessment;