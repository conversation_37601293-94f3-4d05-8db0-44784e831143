import { useTranslation } from "react-i18next";

import HomePageSection from "../HomePageSection";
import LatestProjectItem from "./LatestProjectItem";

import TIME_CIRCLE_ICON from "@src/asset/icon/timeCircle/time-circle.svg";

import "./HomePageLatestProject.scss";


function HomePageLatestProject({ dataSource, ...props }) {
  const { t } = useTranslation();
  
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterChangeStarred, handleAfterMove } = props;
  
  return (
    <HomePageSection
      title={t("LATEST_PROJECTS")}
      icon={TIME_CIRCLE_ICON}
    >
      <div className="latest-project-list">
        {dataSource.map((item, index) => (
          <LatestProjectItem
            key={index}
            projectData={item}
            handleAfterCopy={handleAfterCopy}
            handleAfterRename={handleAfterRename}
            handleAfterDelete={handleAfterDelete}
            handleAfterMove={handleAfterMove}
            handleAfterChangeStarred={handleAfterChangeStarred}
          />
        ))}
      </div>
    </HomePageSection>
  );
}

export default HomePageLatestProject;
