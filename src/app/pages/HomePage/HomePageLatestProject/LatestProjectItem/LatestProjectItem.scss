.latest-project-item {
  padding: 12px;
  gap: 12px;
  display: flex;
  border-radius: 8px;
  z-index: 0;
  position: relative;
  background-color: var(--background-light-background-2);
  flex-direction: column;
  user-select: none;

  box-shadow: var(--shadow-level-2);

  .latest-project-item__thumbnail {
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    aspect-ratio: 156 / 190;
    border: 1px solid var(--background-light-background-grey);

    &:not(:hover) .latest-project-item__thumbnail-backdrop {
      display: none;
    }

    .latest-project-item__thumbnail-backdrop {
      z-index: 2;
      color: var(--white);
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .latest-project-item__thumbnail-image {
      object-fit: cover;
      height: 100%;
      width: 100%;
    }

    .latest-project-item__thumbnail-default {
      z-index: 1;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: var(--white);

      img {
        height: 80px;
      }
    }

  }


  .latest-project-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .latest-project-info__item {
      display: flex;
      flex-direction: row;
      gap: 4px;
      align-items: center;

      .latest-project-info__icon{
        width: 13px;
        height: 16px;
      }

      .latest-project-info__name {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        color: var(--typo-colours-support-blue-dark);
        flex-grow: 1;
      }

      .latest-project-info__action {
        display: flex;
        flex-direction: column-reverse;
      }

      .latest-project-info__last-modified {
        font-size: 10px;
        color: var(--typo-colours-support-blue-light);
        align-self: center;
        flex-grow: 1;

        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }


  &:hover {
    background-color: var(--primary-colours-blue-navy-light-1);

    .latest-project-info__star {
      opacity: 1;
    }
  }
}