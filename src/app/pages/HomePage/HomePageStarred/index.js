import { useTranslation } from "react-i18next";

import FolderProjectList from "@component/FolderProjectList";
import HomePageSection from "../HomePageSection";

import STAR from "@src/asset/icon/star/star.svg";

import { LINK } from "@link";

import "./HomePageStarred.scss";
import { useMemo } from "react";


function HomePageStarred({ dataSource, ...props }) {
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterChangeStarred, handleAfterMove } = props;
  const { t } = useTranslation();
  
  
  const linkShowMore = useMemo(() => dataSource?.length > 7 ? LINK.STARRED : "", [dataSource]);
  
  const starredInfo = useMemo(() => {
    const count = dataSource.reduce(function (grouped, element) {
      if (element.hasOwnProperty("projectName")) grouped.project += 1;
      if (element.hasOwnProperty("folderName")) grouped.folder += 1;
      return grouped;
    }, { project: 0, folder: 0 });
    
    const strArr = [];
    if (count.folder) strArr.push(`${count.folder} ${t("FOLDER")}`);
    if (count.project) strArr.push(`${count.project} ${t("PROJECT")}`);
    
    return "(" + strArr.join(", ").toLowerCase() + ")";
  }, [dataSource]);
  
  
  return (<HomePageSection
    title={t("STARRED")}
    icon={STAR}
    info={starredInfo}
    className="home-page-starred"
  >
    
    <FolderProjectList
      dataSource={dataSource?.slice(0, 7)}
      {...linkShowMore ? { linkShowMore } : {}}
      showFolder
      showOwner
      allowActions
      handleAfterCopy={handleAfterCopy}
      handleAfterRename={handleAfterRename}
      handleAfterDelete={handleAfterDelete}
      handleAfterChangeStarred={handleAfterChangeStarred}
      handleAfterMove={handleAfterMove}
    />
  </HomePageSection>);
}

export default HomePageStarred;
