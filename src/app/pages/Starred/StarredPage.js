import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

import { getAllMySaved } from "@src/app/services/MySaved";

import FolderProjectList from "@src/app/component/FolderProjectList";
import Loading from "@component/Loading";

import { usePageViewTracker } from "@src/ga";

function StarredPage() {
  usePageViewTracker("StarredPage");
  const [dataStarred, setDataStarred] = useState([]);
  const [isLoading, setLoading] = useState(true);

  const { t } = useTranslation();

  useEffect(() => {
    getStarredData();
  }, []);

  const getStarredData = async () => {
    const response = await getAllMySaved();
    if (response) {
      setDataStarred(response);
    }
    setLoading(false);
  };

  const folderProjectData = useMemo(() => {
    return dataStarred.map((item) => {
      return {
        ...item.projectId,
        ...item.folderId,
        isSaved: true,
      };
    });
  }, [dataStarred]);

  const handleAfterRename = (data) => {
    const newData = dataStarred.map((item) => {
      if (item?.folderId?._id === data?._id) {
        data.projects = item?.folderId?.projects;
        return { ...item, folderId: data };
      } else if (item?.projectId?._id === data._id) {
        return { ...item, projectId: data };
      }
      return item;
    });
    setDataStarred(newData);
  };

  const handleAfterDelete = (data) => {
    const newData = dataStarred.filter((item) => item?.folderId?._id !== data?._id && item?.projectId?._id !== data?._id);
    setDataStarred(newData);
  };

  const handleAfterUnsaved = (data) => {
    if(data?.projectName) {
      const newData = dataStarred.filter((item) => item?.projectId?._id !== data?._id);
      setDataStarred(newData);
      return;
    } 
    getStarredData();
  };

  const handleAfterMove = (data) => {
    let existNewFolder = false;
    const newStarredData = dataStarred.map((item) => {
      if (item?.folderId?._id === data?.folderId?._id) {
        existNewFolder = true;
        return { ...item, folderId: { ...item?.folderId, projects: item?.folderId?.projects + 1 } };
      }
      return item;
    });
    if (existNewFolder) {
      const removedData = newStarredData.filter((item) => item?.projectId?._id !== data?._id);
      setDataStarred(removedData);
    };
  }

  if (isLoading) {
    return <Loading active transparent />;
  }

  return (
    <FolderProjectList
      dataSource={folderProjectData}
      allowActions={true}
      handleAfterRename={handleAfterRename}
      handleAfterDelete={handleAfterDelete}
      handleAfterChangeStarred={handleAfterUnsaved}
      handleAfterMove={handleAfterMove}
      showOwner={true}
      showFolder={true}
      textNodata={t("NO_DATA_PAGE")}
    />
  );
}

export default StarredPage;
