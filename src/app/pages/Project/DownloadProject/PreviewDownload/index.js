import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { Popover } from "antd";
import axios from "axios";

import { toast } from "@component/ToastProvider";
import { useProject } from "@app/pages/Project";
import { useDownloadProject } from "@app/pages/Project/DownloadProject";

import AntButton from "@component/AntButton";
import Loading from "@src/app/component/Loading";
import PreviewPdf from "@component/PreviewPdf";

import { BUTTON, PROJECT_TYPE } from "@constant";

import { extractIds } from "@common/dataConverter";
import { createDocumentHeaderProject, getDocHeaderForProject } from "@services/DocumentHeader";
import { createOneExam, createOneMarkTest, createProjectFile, previewProjectFile } from "@services/Report";
import { getAllDocumentOption } from "@services/DocumentOption";

import PDFExtends from "@component/SvgIcons/ExportPDF/PDFExtends";
import PdfCollapse from "@component/SvgIcons/ExportPDF/PdfCollapse";
import DocTempHeader from "@app/pages/Organization/OrganizationTemplate/DocTemplateOptions/DocTempHeader";
import ChevronLeft from "@component/SvgIcons/ChevronLeft";
import ChevronRight from "@component/SvgIcons/ChevronRight";


function PreviewDownload({ ...props }) {
  const { t } = useTranslation();
  const {
    examIndexPreview,
    setExamIndexPreview,
    markTestResponses,
    markTestIndexPreview,
    setMarkTestIndexPreview,
  } = props;
  const { projectId, projectData, isExam, totalExam, examOutputData, isMark, projectContentData } = useProject();
  const {
    isCollapseDownload,
    setCollapseDownload,
    previewType,
    contentSelectedId,
    editOptionsState, setEditOptionsState,
    docTemplateSelected,
    
    getDataDocTemplate,
  } = useDownloadProject();
  
  const [filePreview, setFilePreview] = useState(null);
  const [isLoadingOptions, setLoadingOptions] = useState(false);
  
  
  const [documentHeaderData, setDocumentHeaderData] = useState(null);
  const [documentOptionsData, setDocumentOptionsData] = useState(null);
  
  const [isLoading, setLoading] = useState(false);
  const [request, setRequest] = useState(null);
  
  function handleClearHeader() {
    handleSaveHeader();
  }
  
  
  function handleCancelPreview() {
    if (request) {
      request.cancel();
      setRequest(null);
    }
    
    setFilePreview(null);
    setDocumentHeaderData(null);
    setDocumentOptionsData(null);
  }
  
  function toggleEditOptions() {
    if (editOptionsState.isShowModal) {
      setEditOptionsState({ isShowModal: false, optionsSelected: null });
    } else {
      setEditOptionsState({ isShowModal: true, optionsSelected: contentSelectedId });
    }
  }
  
  useEffect(() => {
    if (!!docTemplateSelected?._id) {
      getDocumentOptionData();
    }
  }, [docTemplateSelected?._id]);
  
  useEffect(() => {
    if (contentSelectedId?.length || markTestResponses.length) {
      createFilePreview();
    } else {
      handleCancelPreview();
    }
  }, [docTemplateSelected?._id, contentSelectedId, previewType, examIndexPreview, markTestIndexPreview, markTestResponses]);
  
  
  async function getDocumentOptionData() {
    if (!!docTemplateSelected?.organizationId) {
      setDocumentHeaderData(null);
      setDocumentOptionsData(null);
      return;
    }
    setLoadingOptions(true);
    const allRequest = [
      getAllDocumentOption({ docxTemplateId: docTemplateSelected._id }),
      getDocHeaderForProject(docTemplateSelected._id),
    ];
    
    const [
      docOptionResponse,
      docHeaderResponse,
    ] = await Promise.all(allRequest);
    
    if (docOptionResponse?.length) setDocumentOptionsData(docOptionResponse);
    if (docHeaderResponse) setDocumentHeaderData(docHeaderResponse);
    setLoadingOptions(false);
  }
  
  async function createProjectFilePreview() {
    const source = axios.CancelToken.source();
    setRequest(source);
    
    const apiRequest = {
      projectId,
      contentIds: contentSelectedId,
      type: previewType,
      isPDF: true,
    };
    if (docTemplateSelected?._id) {
      apiRequest.docxTemplateId = docTemplateSelected._id;
      
      const headerResponse = await getDocHeaderForProject(docTemplateSelected._id);
      if (!!headerResponse?.customHeader) {
        apiRequest.customHeader = headerResponse.customHeader;
      }
    }
    const config = {
      params: { workspaceId: projectData?.workspaceId },
      cancelToken: source.token,
    };
    return createProjectFile(apiRequest, config);
  }
  
  
  async function createExamFilePreview() {
    const responsesSelected = examOutputData[examIndexPreview].filter(data => contentSelectedId.includes(data.contentId));
    let responseIds = extractIds(responsesSelected);
    
    if (!responseIds?.length) return;
    const source = axios.CancelToken.source();
    setRequest(source);
    
    const apiRequest = {
      projectId,
      responseIds,
      type: previewType,
      isPDF: true,
    };
    
    if (docTemplateSelected?._id) {
      apiRequest.docxTemplateId = docTemplateSelected._id;
      
      const headerResponse = await getDocHeaderForProject(docTemplateSelected._id);
      if (!!headerResponse?.customHeader) {
        apiRequest.customHeader = headerResponse.customHeader;
      }
    }
    const config = {
      params: { workspaceId: projectData?.workspaceId },
      cancelToken: source.token,
    };
    
    return createOneExam(apiRequest, config);
  }
  
  async function createMarkTestFilePreview() {
    const source = axios.CancelToken.source();
    setRequest(source);
    const apiRequest = {
      projectId,
      responseId: markTestResponses[markTestIndexPreview]?._id,
      type: previewType,
      isPDF: true,
    };
    if (docTemplateSelected?._id) {
      apiRequest.docxTemplateId = docTemplateSelected._id;
      
      const headerResponse = await getDocHeaderForProject(docTemplateSelected._id);
      if (!!headerResponse?.customHeader) {
        apiRequest.customHeader = headerResponse.customHeader;
      }
    }
    
    const config = {
      params: { workspaceId: projectData?.workspaceId },
      cancelToken: source.token,
    };
    
    return createOneMarkTest(apiRequest, config);
  }
  
  async function createFilePreview() {
    setLoading(true);
    if (request) {
      request.cancel();
      setRequest(null);
    }
    
    let createFileResponse;
    switch (projectData.type) {
      case PROJECT_TYPE.NORMAL:
        createFileResponse = await createProjectFilePreview();
        break;
      case PROJECT_TYPE.EXAM_SCHOOL:
        createFileResponse = await createExamFilePreview();
        break;
      case PROJECT_TYPE.MARK_TEST_SCHOOL:
      case PROJECT_TYPE.MARK_TEST_IELTS:
        createFileResponse = await createMarkTestFilePreview();
        break;
      default:
        break;
    }
    
    if (createFileResponse?.fileName) {
      const previewResponse = await previewProjectFile(createFileResponse.fileName);
      if (previewResponse) {
        setFilePreview(previewResponse);
      } else {
        setFilePreview(null);
      }
      setRequest(null);
    } else {
      setFilePreview(null);
    }
    setLoading(false);
  }
  
  
  async function handleSaveHeader(values = {}) {
    
    const apiRequest = {
      docxTemplateId: docTemplateSelected._id,
      customHeader: values,
    };
    
    const apiResponse = await createDocumentHeaderProject(apiRequest);
    if (apiResponse) {
      setEditOptionsState({ isShowModal: false, optionsSelected: null });
      createFilePreview();
      setDocumentHeaderData(apiResponse);
      toast.success("SAVE_DATA_SUCCESS");
      
      getDataDocTemplate();
    }
  }
  
  const disabledAction = useMemo(() => {
    return editOptionsState.isShowModal || isLoadingOptions;
  }, [editOptionsState?.isShowModal, isLoadingOptions]);
  
  const studentName = useMemo(() => {
    const inputs = projectContentData[0]?.inputs;
    if (!inputs?.length) return null;
    return inputs[markTestIndexPreview]?.inputData?.studentName;
  }, [projectContentData, markTestIndexPreview]);
  
  return <div className="download-project__section download-project__preview">
    
    <div className="download-project__section-title">
      {t("PREVIEW_PDF")}
      
      {isExam && !!totalExam && <div className="download-project__change-preview">
        <AntButton
          size="xsmall"
          type={BUTTON.WHITE_BLUE}
          className="change-preview__prev"
          icon={<ChevronLeft />}
          disabled={examIndexPreview === 1}
          onClick={() => setExamIndexPreview(prevState => prevState - 1)}
        />
        
        <div className="change-preview__index">
          {t("EXAM_NUMBER").format(examIndexPreview)}
        </div>
        
        <AntButton
          size="xsmall"
          type={BUTTON.WHITE_BLUE}
          className="change-preview__next"
          icon={<ChevronRight />}
          disabled={examIndexPreview === totalExam}
          onClick={() => setExamIndexPreview(prevState => prevState + 1)}
        />
      </div>}
      
      {isMark && markTestResponses.length && <div className="download-project__change-preview">
        <AntButton
          size="xsmall"
          type={BUTTON.WHITE_BLUE}
          className="change-preview__prev"
          icon={<ChevronLeft />}
          disabled={markTestIndexPreview === 0}
          onClick={() => setMarkTestIndexPreview(prevState => prevState - 1)}
        />
        
        <div className="change-preview__index">
          {t("GRADED_ASSIGNMENT_FOR_").format(studentName)}
        </div>
        
        <AntButton
          size="xsmall"
          type={BUTTON.WHITE_BLUE}
          className="change-preview__next"
          icon={<ChevronRight />}
          disabled={markTestIndexPreview === markTestResponses.length - 1}
          onClick={() => setMarkTestIndexPreview(prevState => prevState + 1)}
        />
      </div>}
      
      <div className="download-project__section-action">
        {!isCollapseDownload && docTemplateSelected && !docTemplateSelected?.organizationId
          && <>
            {!!contentSelectedId && <AntButton
              size="xsmall"
              type={BUTTON.LIGHT_NAVY}
              onClick={toggleEditOptions}
              disabled={disabledAction}
            >
              {t("EDIT")}
            </AntButton>}
            
            <AntButton
              size="xsmall"
              type={BUTTON.LIGHT_PINK}
              disabled={disabledAction}
              onClick={handleClearHeader}
            >
              {t("CLEAR")}
            </AntButton>
          </>}
        
        <AntButton
          size="xsmall"
          type={BUTTON.GHOST_WHITE}
          icon={isCollapseDownload ? <PDFExtends /> : <PdfCollapse />}
          onClick={() => setCollapseDownload(prevState => !prevState)}
          disabled={disabledAction}
        />
      
      </div>
    
    </div>
    
    <Popover
      align={{ offset: [-8, 0] }}
      placement="leftTop"
      open={editOptionsState.isShowModal}
      arrow={false}
      overlayClassName="edit-options-container"
      content={<div>
        <DocTempHeader
          docOptionData={documentOptionsData}
          docHeaderData={documentHeaderData}
          onSaveHeader={handleSaveHeader}
          onCancel={toggleEditOptions}
        />
      </div>}>
      {(request || isLoading)
        ? <Loading active />
        : <div className="download-project__preview-pdf">
          {!!filePreview && <PreviewPdf file={filePreview} />}
        </div>}
    </Popover>
  
  
  </div>;
}

export default PreviewDownload;