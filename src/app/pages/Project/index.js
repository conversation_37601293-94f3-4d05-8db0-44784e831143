import React, { useContext, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { connect } from "react-redux";
// import html2canvas from "html2canvas";
import { usePageViewTracker } from "@src/ga";
import domtoimage from "dom-to-image-more";

import useWindowDimensions from "@common/windowDimensions";

import NeedAccess from "@component/NeedAccess";
import YoutubeModal from "@component/YoutubeModal";
import ProjectDetail from "@app/pages/Project/ProjectDetail";
import Guide from "@app/pages/Project/Guide";
import NoData from "@component/NoData";
import Loading from "@component/Loading";

import { CONSTANT, PERMISSION, PROJECT_TYPE, TYPE_OF_TOOL } from "@constant";
import { cloneObj } from "@common/functionCommons";
import { convertArrayToObject, extractKeys, groupBy } from "@common/dataConverter";

import { createContentBlock, getProjectDetail, insertContentBlock, uploadImageProject } from "@services/Project";
import { updateContent } from "@services/Content";
import { downloadResponse } from "@services/Report";
import { getInputDetail } from "@services/Input";
import { cancelRetryResponse, deleteMarkTestResponse, deleteResponse, updateResponseOutPut } from "@services/Response";

import * as tool from "@src/ducks/tool.duck";
import * as app from "@src/ducks/app.duck";

import "./Project.scss";

export const ProjectContext = React.createContext();

function Project({ listAllTool, listToolAvailable, user, ...props }) {
  usePageViewTracker("Project");
  
  const projectId = useParams()?.id;
  const { width } = useWindowDimensions();
  
  const [isFirst, setFirst] = useState(true);
  const [projectData, setProjectData] = useState({});
  const [projectContentData, setProjectContentData] = useState([]);
  const [isPreviewProject, setPreviewProject] = useState(false);
  const [isShowPlainText, setShowPlainText] = useState(false);
  const [toolDemoState, setToolDemoState] = useState({
    open: false,
    link: null,
  });
  
  const [guideData, setGuideData] = useState({ showGuide: false, content: null });
  
  const [submitState, setSubmitState] = useState({
    isSubmit: false, submitValue: null, contentId: null,
  });
  const [stateMoreTool, setStateMoreTool] = useState({
    isShowModal: false,
    contentIndex: null,
  });
  
  const [examOrderSelected, setExamOrderSelected] = useState(1);
  const [examOptionData, setExamOptionData] = useState([]);
  
  const [subSelectedIndex, setSubSelectedIndex] = useState(0);
  
  const { permission } = projectData;
  const isShowInfoMobile = useMemo(() => width < 1280, [width]);
  const isShowOutlineMobile = useMemo(() => width < 1024, [width]);
  
  useEffect(() => {
    if (!listAllTool) props.getTool();
    if (!listToolAvailable) props.getToolAvailable();
  }, []);
  
  useEffect(() => {
    handleGetProjectData();
  }, [projectId]);
  
  useEffect(() => {
    const jsContent = document.getElementById("js-layout-content");
    if (jsContent) {
      jsContent.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [isPreviewProject]);
  
  useEffect(() => {
    if (!stateMoreTool.isShowModal && !!stateMoreTool.contentIndex) {
      setStateMoreTool({ isShowModal: false, contentIndex: null });
    }
  }, [stateMoreTool.isShowModal]);
  
  const isAllowEditing = useMemo(() => {
    return [PERMISSION.OWNER, PERMISSION.EDITOR].includes(permission);
  }, [permission]);
  
  const [isNormal, isExam, isMark] = useMemo(() => {
    return [
      [PROJECT_TYPE.NORMAL].includes(projectData.type),
      [PROJECT_TYPE.EXAM_SCHOOL, PROJECT_TYPE.EXAM_IELTS].includes(projectData.type),
      [PROJECT_TYPE.MARK_TEST_SCHOOL, PROJECT_TYPE.MARK_TEST_IELTS].includes(projectData.type),
    ];
  }, [projectData.type]);
  
  const isMarkStudent = useMemo(() => {
    return projectData.type === PROJECT_TYPE.MARK_TEST_IELTS
      && [TYPE_OF_TOOL.MARK_TEST_TASK_2, TYPE_OF_TOOL.MARK_TEST_TASK_1, TYPE_OF_TOOL.STUDENT_TASK_1, TYPE_OF_TOOL.STUDENT_TASK_2].includes(projectContentData[0]?.toolId?.inputType);
  }, [projectData, projectContentData]);
  
  const isMarkSpeaking = useMemo(() => {
    return projectData.type === PROJECT_TYPE.MARK_TEST_IELTS
      && (projectContentData[0]?.toolId?.inputType === TYPE_OF_TOOL.AUDIO_STREAM
        || projectContentData[0]?.toolId?.inputType === TYPE_OF_TOOL.STUDENT_SPEAKING);
    
  }, [projectData, projectContentData]);
  
  const examOutputData = useMemo(() => {
    
    const responses = extractKeys(projectContentData, "responses")?.flat()?.filter(x => !!x);
    const contentObj = convertArrayToObject(projectContentData, "_id");
    
    responses.forEach(response => {
      if (contentObj[response?.contentId]?.contentIndex) {
        response.contentIndex = contentObj[response.contentId].contentIndex;
        response.contentTitle = contentObj[response.contentId].title;
      }
    });
    
    if (!responses?.length) return {};
    const responseGrouped = groupBy(responses.flat(), "examOrder");
    Object.keys(responseGrouped).forEach(examOrder => {
      responseGrouped[examOrder].sort((a, b) => a.contentIndex - b.contentIndex);
    });
    return responseGrouped;
  }, [projectContentData]);
  
  const totalExam = useMemo(() => Object.keys(examOutputData)?.length || 0, [examOutputData]);
  
  
  const contentDownload = useMemo(() => {
    return cloneObj(projectContentData)
      .map(content => {
        content.response = content?.responses?.find(response => response.isActivate);
        content.toolId = listAllTool?.find((tool) => tool._id === content?.toolId?._id);
        return content;
      })
      .filter(content => !!content.response);
  }, [projectContentData, listAllTool]);
  
  function handleSaveContentData(contentData) {
    if (!contentData) return;
    setProjectContentData(() => {
      return contentData.map(contentItem => {
        delete contentItem.lastInput;
        contentItem.response = isNormal
          ? contentItem.responses?.find(response => response?.isActivate)
          : isExam ? contentItem.responses?.find(response => response.examOrder === examOrderSelected)
            : null;
        
        return contentItem;
      });
    });
  }
  
  async function handleGetProjectData() {
    const projectResponse = await getProjectDetail(projectId);
    if (projectResponse) {
      switch (projectResponse.code) {
        case 200:
          const { content, ...projectDetail } = projectResponse.data;
          handleSaveContentData(content);
          
          if (projectDetail.permission === PERMISSION.VIEWER) {
            setPreviewProject(true);
          }
          setProjectData(projectDetail);
          if (projectDetail.type === PROJECT_TYPE.EXAM_SCHOOL && projectDetail?.activeExam) {
            setExamOrderSelected(projectDetail.activeExam);
          }
          props.setBreadcrumb({ project: projectDetail, folder: projectDetail.folderId });
          break;
        case 403:
          setProjectData({ permission: PERMISSION.NO_PERMISSION });
          break;
        case 404:
          setProjectData({ permission: CONSTANT.NOT_FOUND });
          break;
      }
    }
    setFirst(false);
  }
  
  async function handleChangeTitle(contentId, title) {
    const apiResponse = await updateContent({ _id: contentId, title });
    if (apiResponse) {
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState.forEach(state => {
          if (state._id === contentId) state.title = apiResponse.title;
        });
        return newState;
      });
    }
  }
  
  async function handleChangeDescription(contentId, description) {
    const apiResponse = await updateContent({ _id: contentId, description });
    if (apiResponse) {
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState.forEach(state => {
          if (state._id === contentId) state.description = apiResponse.description;
        });
        return newState;
      });
    }
  }
  
  async function handleSubmitResponse(contentId, dataResponse = {}) {
    const apiResponse = await updateResponseOutPut(dataResponse);
    if (apiResponse) {
      setProjectContentData(prevState => {
        return cloneObj(prevState).map(state => {
          if (state._id === contentId) {
            state.responses = state.responses?.map(response => {
              if (response._id === apiResponse._id) {
                response.output = apiResponse.output;
                response.plaintext = apiResponse.plaintext;
              }
              return response;
            });
          }
          return state;
        });
      });
    }
  }
  
  async function handleCancelRetryResponse(responseId) {
    const apiResponse = await cancelRetryResponse(responseId);
    //console.log("apiResponse", apiResponse);
  }
  
  async function handleDeleteResponse(contentId, id) {
    const deleteService = isMark ? deleteMarkTestResponse : deleteResponse;
    const apiResponse = await deleteService(id);
    if (apiResponse) {
      const inputData = await getInputDetail(apiResponse?.inputId);
      
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState.forEach(state => {
          if (Array.isArray(state.responses) && state._id === contentId) {
            let activeIndex = state.responses.findIndex(response => response._id === apiResponse._id);
            if (activeIndex === state.responses.length - 1) {
              activeIndex--;
            }
            
            if (!!inputData?._id) {
              state.inputs = state.inputs?.map(input => inputData._id === input._id ? inputData : input);
            }
            
            state.responses = state.responses?.filter(response => response._id !== apiResponse._id);
            if (state.responses[activeIndex]) {
              state.responses[activeIndex].isActivate = true;
            }
            
          }
        });
        return newState;
      });
    }
  }
  
  async function handleDownloadResponse(contentId, id) {
    await downloadResponse(contentId, id);
  }
  
  function onCreateContent(contentData) {
    setProjectContentData(prevState => [...prevState, contentData]);
    scrollToContent(contentData._id);
  }
  
  
  function scrollToContent(contentId) {
    
    const delta = (isExam && !isPreviewProject) ? 340 : 0;
    
    let offset = 104 - delta;
    
    if (isExam) {
      if (!isPreviewProject) offset += 200;
      const jsExamOutput = document.getElementById("js-exam-output");
      if (jsExamOutput) {
        offset -= jsExamOutput.offsetTop;
      }
    }
    
    const jsProjectContent = isPreviewProject
      ? document.getElementById(`js-project-preview-${contentId}`)
      : document.getElementById(`js-project-content-${contentId}`);
    const jsContent = document.getElementById("js-layout-content");
    if (jsProjectContent && jsContent) {
      const offsetTop = jsProjectContent.offsetTop;
      jsContent.scrollTo({ top: offsetTop - offset, behavior: "smooth" });
    } else {
      setTimeout(() => scrollToContent(contentId), 10);
    }
  }
  
  
  async function addContentBlock({ toolId, inputType, inputData }) {
    const contentData = { toolId, inputData, inputType };
    if (stateMoreTool.contentIndex) {
      contentData.contentIndex = stateMoreTool.contentIndex + 1;
      const contentInsertResponse = await insertContentBlock(projectId, contentData);
      
      if (contentInsertResponse) {
        setProjectContentData(prevState => {
          const newState = cloneObj(prevState);
          newState.splice(contentInsertResponse.contentIndex - 1, 0, contentInsertResponse);
          
          return newState.map((state, index) => {
            state.contentIndex = index + 1;
            return state;
          });
        });
        scrollToContent(contentInsertResponse._id);
      }
      
    } else {
      const contentCreateResponse = await createContentBlock(projectId, contentData);
      if (contentCreateResponse) {
        onCreateContent(contentCreateResponse);
      }
    }
  }
  
  const onCloseToolDemo = () => {
    setToolDemoState({
      open: !toolDemoState.open,
      link: null,
    });
  };
  
  async function captureThumbContent(ignoreElement) {
    const jsProjectContent = document.getElementById("js-project-content");
    return await domtoimage.toBlob(jsProjectContent,
      {
        style: {
          transform: "scale(0.5)",
          transformOrigin: "top left",
        },
        width: jsProjectContent.offsetWidth / 2,
        height: (jsProjectContent.offsetWidth / 2) * (190 / 156),
        filter: ignoreElement ? node => node.id !== ignoreElement : null,
      },
    );
    //   return new Promise(function (resolve, reject) {
    //     const jsProjectContent = document.getElementById("js-project-content");
    //     html2canvas(jsProjectContent, {
    //       allowTaint: true,
    //       useCORS: true,
    //       //backgroundColor: "red",
    //       windowWidth: 1600,
    //       windowHeight: 1000,
    //       height: Math.min(jsProjectContent.offsetHeight, 1300),
    //       scale: 0.5,
    //       onclone: (clonedDocument) => {
    //         Array.from(clonedDocument.querySelectorAll("textarea")).forEach((textArea) => {
    //           const div = clonedDocument.createElement("div");
    //           div.innerText = textArea.value;
    //           div.style.border = "1px solid #F1F1F1";
    //           div.style.borderRadius = "4px";
    //           div.style.padding = "9px 15px";
    //           textArea.style.display = "none";
    //           textArea.parentElement.append(div);
    //         });
    //       },
    //     })
    //       .then((canvas) => {
    //         canvas.toBlob((imageBlob) => resolve(imageBlob));
    //       })
    //       .catch(() => reject(null));
    //   });
  }
  
  async function createContentThumbnail(ignoreElement) {
    const image = await captureThumbContent(ignoreElement);
    if (image) {
      uploadImageProject(projectId, image);
    }
  }
  
  if (isFirst || !listAllTool || !listToolAvailable) {
    return <Loading active transparent/>;
  }
  
  if (permission === PERMISSION.NO_PERMISSION) {
    return <NeedAccess/>;
  }
  
  if (permission === CONSTANT.NOT_FOUND) {
    return <NoData>Folder not found</NoData>;
  }
  
  return <ProjectContext.Provider value={{
    projectId,
    projectData, setProjectData,
    projectContentData, contentDownload,
    setProjectContentData,
    isPreviewProject, setPreviewProject,
    isShowPlainText, setShowPlainText,
    permission,
    isAllowEditing,
    // function
    handleGetProjectData,
    
    addContentBlock,
    handleChangeTitle,
    handleChangeDescription,
    handleSubmitResponse,
    handleDeleteResponse,
    handleCancelRetryResponse,
    handleDownloadResponse,
    setToolDemoState,
    
    scrollToContent,
    
    // thumbnail
    createContentThumbnail,
    
    // guide
    guideData, setGuideData,
    
    // submit content input
    submitState, setSubmitState,
    
    //
    stateMoreTool, setStateMoreTool,
    
    //
    isShowInfoMobile,
    isShowOutlineMobile,
    
    // normal
    isNormal,
    handleSaveContentData,
    
    // exam
    isExam, examOutputData, totalExam,
    examOrderSelected, setExamOrderSelected,
    examOptionData, setExamOptionData,
    
    // mark exam
    isMark, subSelectedIndex, setSubSelectedIndex,
    isMarkStudent,
    
    isMarkSpeaking,
  }}>
    <ProjectDetail/>
    
    <YoutubeModal isOpen={toolDemoState.open} linkYoutube={toolDemoState.link} handleCancel={onCloseToolDemo}/>
    
    <Guide/>
  
  </ProjectContext.Provider>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  const { listAllTool, listToolAvailable } = store.tool;
  return { user, listAllTool, listToolAvailable };
}

const mapDispatchToProps = {
  ...app.actions,
  ...tool.actions,
};

const ConnectedProject = connect(mapStateToProps, mapDispatchToProps)(Project);
const useProject = () => useContext(ProjectContext);

export { ConnectedProject as Project, useProject };
