import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import _ from "lodash";

import { toast } from "@component/ToastProvider";
import { useProject } from "@app/pages/Project";

import ContentHeader from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentHeader";
import { ContentInputForm } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm";
import ExamSubmission from "@app/pages/Project/ProjectDetail/MarkExamContent/ExamSubmission";
import MarkingResults from "@app/pages/Project/ProjectDetail/MarkExamContent/MarkingResults";

import { CONSTANT } from "@constant";
import { getToolInfo } from "@app/pages/Project/ProjectDetail/projectCommons";
import { updateProject } from "@services/Project";

import { streamSseMarkExam, submitMark } from "@services/Content";
import { cloneObj } from "@common/functionCommons";
import { convertArrayToObject, extractIds, extractKeys } from "@common/dataConverter";

import "./MarkExamContent.scss";

export const MarkExamContext = React.createContext();

function MarkExamContent() {
  const { projectId, projectContentData, subSelectedIndex } = useProject();
  const { projectData, setProjectData, setProjectContentData } = useProject();
  
  const projectCommonOptions = useRef({});
  const submitMarkType = useRef(undefined);
  const isStreaming = useRef(false);
  const eventSource = useRef(undefined);
  const [instructionSelected, setInstructionSelected] = useState(null);
  const [examSubData, setExamSubData] = useState([]);
  const [isMarking, setMarking] = useState(false);
  
  const subSelectedData = useMemo(() => examSubData?.[subSelectedIndex], [examSubData, subSelectedIndex]);
  
  useEffect(() => {
    return () => closeEv();
  }, []);
  
  useEffect(() => {
    projectCommonOptions.current = projectData?.commonOptions;
  }, [projectData?.commonOptions]);
  
  const contentData = useMemo(() => {
    if (!projectContentData?.length) return null;
    return projectContentData[0];
  }, [projectContentData]);
  
  useEffect(() => {
    handleStreamSubmission();
  }, [contentData?.responses]);
  
  function setSubmitMarkType(submitType) {
    submitMarkType.current = submitType;
  }
  
  function handleStreamSubmission() {
    const isExistProcessing = contentData?.responses?.some(response => response.state?.toUpperCase() === CONSTANT.PROCESSING);
    
    if (!isExistProcessing && !isStreaming.current) return;
    isStreaming.current = true;
    eventSource.current = streamSseMarkExam(projectId);
    eventSource.current.addEventListener(projectId, async (event) => {
      const data = JSON.parse(event?.data);
      
      if (data?.state?.toUpperCase() === CONSTANT.DONE) {
        const inputGrouped = convertArrayToObject(extractKeys(data.responses, "inputId"), "_id");
        const responseGrouped = convertArrayToObject(data.responses, "_id");
        
        setProjectContentData(prevState => {
          const newState = cloneObj(prevState);
          newState[0].inputs = newState[0].inputs?.map(input => inputGrouped[input?._id] || input);
          newState[0].responses = newState[0].responses?.map(response => {
            if (responseGrouped[response?._id]) {
              const newResponse = responseGrouped[response?._id];
              return {
                ...newResponse,
                typingEffect: subSelectedData?._id === response?.inputId?._id && !newResponse?.canceledSubmit,
              };
            }
            return response;
          });
          return newState;
        });
        isStreaming.current = false;
      }
    });
    eventSource.current.addEventListener("error", async (error) => {
      closeEv();
    });
  }
  
  function closeEv() {
    eventSource.current?.close();
    eventSource.current = null;
  }
  
  useEffect(() => {
    if (instructionSelected?.options?.length && !projectData?.commonOptions) {
      createDefaultCommonOptions();
    }
  }, [projectData?.commonOptions, instructionSelected]);
  
  function createDefaultCommonOptions() {
    const commonOptions = {};
    instructionSelected.options
                       .filter(option => !!option.code)
                       .forEach(option => {
                         commonOptions[option.code] = option.defaultValue || "";
                       });
    updateCommonData(commonOptions);
  }
  
  async function updateCommonData(commonData = {}) {
    const commonOptions = cloneObj(commonData);
    delete commonOptions.instructionId;
    
    const apiRequest = { _id: projectId, commonOptions };
    const apiResponse = await updateProject(apiRequest, false);
    if (apiResponse?.commonOptions) {
      projectCommonOptions.current = apiResponse?.commonOptions;
    }
  }
  
  const resultData = useMemo(() => {
    if (!Array.isArray(contentData?.responses)) return {};
    return contentData?.responses?.reduce(function (grouped, element) {
      grouped[element?.inputId?._id] = element;
      grouped[element?.inputId?._id].studentName = element.inputId?.studentName;
      return grouped;
    }, {});
  }, [contentData?.responses]);
  
  const toolInfo = useMemo(() => getToolInfo(contentData), [contentData]);
  
  const resultSelected = useMemo(() => resultData?.[subSelectedData?._id], [resultData, subSelectedData]);
  
  
  const debouncedSaveCommonOptions = useCallback(_.debounce(updateCommonData, 500), []);
  
  function onChange(changedValues) {
    const commonOptions = Object.assign({}, projectCommonOptions.current, changedValues);
    
    debouncedSaveCommonOptions(commonOptions);
  }
  
  async function onFinish(values) {
    const apiRequest = {
      projectId,
      workspaceId: projectData.workspaceId,
      contentId: contentData._id,
      //commonOptions: projectData?.commonOptions,
    };
    if (submitMarkType.current === CONSTANT.SUBMIT_ALL) {
      apiRequest.inputIds = extractIds(examSubData);
    } else if (submitMarkType.current === CONSTANT.SUBMIT_ONE) {
      apiRequest.inputIds = [subSelectedData._id];
    }
    
    submitMarkType.current = undefined;
    
    setMarking(true);
    const apiResponse = await submitMark(apiRequest);
    if (Array.isArray(apiResponse)) {
      const typingEffectResponse = apiResponse.map(response => {
        response.typingEffect = true;
        return response;
      });
      
      const inputResponse = extractKeys(apiResponse, "inputId");
      const inputGrouped = convertArrayToObject(inputResponse, "_id");
      
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState[0].inputs = newState[0].inputs?.map(input => {
          if (inputGrouped[input._id]) input = inputGrouped[input._id];
          return input;
        });
        
        newState[0].responses ||= [];
        newState[0].responses.push(...typingEffectResponse);
        return newState;
      });
    } else if (apiResponse?.success && apiResponse?.message) {
      toast.warning({ description: apiResponse.message });
    }
    setMarking(false);
  }
  
  if (!projectContentData.length) return null;
  
  return <MarkExamContext.Provider value={{
    examSubData, setExamSubData,
    subSelectedData,
    contentData, resultData, resultSelected,
    
    //
    isMarking,
    setSubmitMarkType,
  }}>
    
    <div className="mark-exam-content">
      <ContentHeader toolInfo={toolInfo} />
      
      <ContentInputForm
        formKey={CONSTANT.MARK_EXAM}
        content={contentData}
        inputData={projectData?.commonOptions}
        
        toolInfo={toolInfo}
        instructionSelected={instructionSelected}
        setInstructionSelected={setInstructionSelected}
        onChange={onChange}
        isDisabledContent={false}
        
        onFinish={onFinish}
      />
      
      <ExamSubmission
        instructionId={instructionSelected}
        toolInfo={toolInfo}
      />
      
      <MarkingResults
        
        contentData={contentData}
        instructionSelected={instructionSelected}
        toolInfo={toolInfo}
      />
    
    </div>
  </MarkExamContext.Provider>;
}

const useMarkExam = () => useContext(MarkExamContext);

export { MarkExamContent, useMarkExam };