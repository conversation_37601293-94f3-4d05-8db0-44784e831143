import React, { useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button, Image, Popover } from "antd";
import clsx from "clsx";
import { useDropzone } from "react-dropzone";
import { ReactSortable } from "react-sortablejs";

import { CONSTANT } from "@constant";
import { API } from "@api";

import ADD_ICON from "@src/asset/icon/plus/plus-navy-border.svg";
import Maximize from "@src/asset/icon/maximize-24.svg";
import Trash2 from "@component/SvgIcons/Trash/Trash2";

const ImagesContent = (props) => {
  const { t } = useTranslation();

  const { imageList, setImageList, inputType, clearDataOtherTab } = props;

  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState(null);

  const inputRef = useRef();
  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop,
    multiple: true,
    noClick: true,
    accept: {
      "image/jpg": [".jpg"],
      "image/jpeg": [".jpeg"],
      "image/png": [".png"],
    },
    noDrag: true,
  });

  function onDrop(files) {
    if (!files.length) return;
    setImageList(pre => [...pre, ...files]);
    clearDataOtherTab();
  }

  const hanldePreviewImage = (index) => {
    setCurrent(index);
    setVisible(true);
  }

  const handleDeleteImage = (e, deleteIndex) => {
    e.stopPropagation();
    setImageList(pre => pre.filter((item, index) => index !== deleteIndex));
  }

  const imageUrls = useMemo(() => {
    return imageList?.map((image) => {
      const url = image.imageFileId ? API.STREAM_ID.format(image.imageFileId) : URL.createObjectURL(image);
      return ({ url, file: image })
    })
  }, [imageList])

  const onUpdateImageList = (urlList) => {
    const updateImages = urlList.map(item => item.file)
    setImageList(updateImages);
  }

  const renderOrderImage = (index) => {
    if (index < 9) return `0${index + 1}`;
    return `${index + 1}`
  }

  // Not upload more image for old project, which have image in inpuData
  if (inputType !== CONSTANT.IMAGE) return null;
  return (<>
    <Image.PreviewGroup
      preview={{
        onVisibleChange: setVisible,
        onChange: setCurrent,
        current: current,
        visible: visible,
      }}
    >
      <div className={clsx("images-content", { "previewing": visible }, { "has-image": !!imageList?.length })}>
        <ReactSortable
          setList={onUpdateImageList}
          list={imageUrls}
          className="sortable-container"
        >
          {imageList?.map((item, index) => {
            const src = imageUrls[index]?.url;
            return (
              <div key={index} className={clsx("images-content__item", { "warning-exceed-image": index > 4 })}>
                <img src={src} alt="" />

                <Popover
                  placement="bottom"
                  content={item.name}
                  trigger="hover"
                  className="item__name"
                >
                  {item.name}
                </Popover>

                <div className="item__order">{renderOrderImage(index)}</div>

                <div className="item__backdrop" onClick={() => hanldePreviewImage(index)}>
                  <img src={Maximize} alt="" />
                  <Button icon={<Trash2 />}
                    size="small"
                    className={"item__delete"}
                    onClick={(e) => handleDeleteImage(e, index)} />
                </div>

                <Image
                  style={{ display: "none" }}
                  src={src}
                />
              </div>
            )
          })}
        </ReactSortable>

        {imageList?.length < 5 && <div className="images-content__upload" onClick={open}>
          <img src={ADD_ICON} />
          <span>{t("ADD_IMAGE")}</span>
        </div>}
      </div>
      <div className="hidden" {...getRootProps()}>
        <input {...getInputProps()} ref={inputRef} />
      </div>
    </Image.PreviewGroup>
    <div className={clsx("limit-images-notice", { "limit-images-error": imageList?.length > 5 })}>
      {t("IMAGES_NUMBER_LIMIT")}
    </div>
  </>
  );

};

export default ImagesContent;