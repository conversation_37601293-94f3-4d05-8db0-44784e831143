import React from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "antd";
import clsx from "clsx";

import Zap from "@component/SvgIcons/Zap";
import AlignCenter from "@component/SvgIcons/AlignCenter";

import { useProject } from "@app/pages/Project";

import "./ViewMode.scss";
import AntButton from "@component/AntButton";
import { BUTTON } from "@constant";

function ViewMode() {
  const { t } = useTranslation();
  const { isShowPlainText, setShowPlainText, projectContentData, isMarkSpeaking } = useProject();
  
  async function handleShowViewMode(preview) {
    setShowPlainText(preview);
  }
  
  if (!projectContentData?.length || isMarkSpeaking) return;
  return <div className="project-content__view-mode">
    <AntButton
      size="small"
      type={!isShowPlainText ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
      onClick={() => handleShowViewMode(false)}
      icon={<Zap />}
      bordered
    >
      {t("FANCY")}
    </AntButton>
    
    <AntButton
      size="small"
      type={isShowPlainText ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
      onClick={() => handleShowViewMode(true)}
      icon={<AlignCenter />}
      bordered
    >
      {t("PLAIN")}
    </AntButton>
  </div>;
}

export default ViewMode;