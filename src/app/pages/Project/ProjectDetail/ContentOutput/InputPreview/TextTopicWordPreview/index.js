import React from "react";
import { useTranslation } from "react-i18next";

import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import "./TextTopicWordPreview.scss";

function TextTopicWordPreview({...props}) {
  const { t } = useTranslation();
  
  const inputData = props.inputData || useContent()?.inputData;
  const toolInfo = props.toolInfo || useContent()?.toolInfo;
  
  return <div className="text-topic-word-preview-container">
    {t(toolInfo?.inputType?.toUpperCase())}: {inputData?.topic || inputData?.text}
  </div>;
}

export default TextTopicWordPreview;