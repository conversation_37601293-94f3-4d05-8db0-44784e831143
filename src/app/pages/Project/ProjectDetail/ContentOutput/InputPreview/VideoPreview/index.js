import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import Loading from "@component/Loading";
import AntButton from "@component/AntButton";
import QrCodeDownload from "@app/pages/Project/ProjectDetail/ContentOutput/InputPreview/QrCodeDownload";

import { convertSecondToHHMMSS, convertSecondToMs, getYoutubeVideoId } from "@common/functionCommons";
import { getOfflineVideoDetail } from "@services/OfflineVideo";
import { getVideoDetail } from "@services/Video";

import { API } from "@api";
import { LINK } from "@link";
import { BUTTON, CONSTANT } from "@constant";

import PlaySoft from "@component/SvgIcons/Play/PlaySoft";
import YOUTUBE_ICON from "@src/asset/resourceIcon/youtube-icon.svg";

import "./VideoPreview.scss";


function VideoPreview({ ...props }) {
  const { t } = useTranslation();
  const inputData = props.inputData || useContent()?.inputData;
  
  const [isFirst, setFirst] = useState(true);
  const [loadingImage, setLoadingImage] = useState(true);
  const [videoInfo, setVideoInfo] = useState(props.videoInfo || {
    type: "",
    videoTitle: "",
    thumb: "",
    linkPreview: "",
    linkDownload: "",
  });
  
  
  useEffect(() => {
    if (inputData?.url) {
      getInfoYoutubeVideo();
    } else if (inputData?.offlineVideoId) {
      getOfflineVideoInfo();
    }
  }, [inputData?.url, inputData?.offlineVideoId]);
  
  async function getOfflineVideoInfo() {
    const apiResponse = await getOfflineVideoDetail(inputData.offlineVideoId, ["videoFileId"]);
    if (apiResponse) {
      setVideoInfo({
        type: CONSTANT.VIDEO,
        videoTitle: apiResponse.name,
        thumb: API.STREAM_ID.format(apiResponse.thumbnailFileId),
        linkPreview: LINK.VIDEO_ID.format(inputData.offlineVideoId) + `?start=${inputData.cutStart}&end=${inputData.cutEnd}`,
        linkDownload: API.DOWNLOAD_OFFLINE_VIDEO.format(inputData.offlineVideoId, inputData.cutStart, inputData.cutEnd),
      });
    }
    setFirst(false);
  }
  
  
  async function getInfoYoutubeVideo() {
    const videoId = getYoutubeVideoId(inputData.url);
    const apiResponse = await getVideoDetail(videoId);
    if (apiResponse?.lengthSeconds) {
      setVideoInfo({
        type: CONSTANT.AUDIO,
        videoTitle: apiResponse.title,
        thumb: apiResponse.thumbnailBase64 ? `data:image/png;base64,${apiResponse.thumbnailBase64}` : "",
        linkPreview: apiResponse.url + `&t=${inputData.cutStart}`,
        linkDownload: API.DOWNLOAD_AUDIO_YOUTUBE.format(apiResponse.url, inputData.cutStart, inputData.cutEnd),
      });
    }
    setFirst(false);
  }
  
  function onLoadImage() {
    setLoadingImage(false);
  }
  
  const videoLinkIcon = inputData?.url ? <img src={YOUTUBE_ICON} alt="" /> : <PlaySoft />;
  return <div className="video-preview-container">
    <div className="video-preview-inner">
      <Loading active={loadingImage} className="video-preview__thumbnail">
        <img src={videoInfo?.thumb} alt="" onLoad={onLoadImage} />
      </Loading>
      
      <Loading active={isFirst} className="video-preview-info">
        <div className="video-preview-info__title">
          {t("VISIT_LINK")}
        </div>
        <div className="video-preview-info__preview">
          <a
            href={videoInfo?.linkPreview}
            className="video-info-preview__link"
            target="_blank" rel="noopener noreferrer"
          
          >
            <AntButton
              size="small"
              type={BUTTON.LIGHT_BLUE}
              icon={videoLinkIcon}
            >
              <AntButton.Label>
                {videoInfo?.videoTitle}
              </AntButton.Label>
            </AntButton>
          </a>
        
        </div>
        <div className="video-info__time">
          <div className="video-info__time-duration">
            {t("DURATION")}: {convertSecondToMs(inputData.cutEnd - inputData.cutStart)}
          </div>
          <div className="video-info__time-start">
            {t("START")}: {convertSecondToHHMMSS(inputData.cutStart)}
          </div>
          <div className="video-info__time-end">
            {t("END")}: {convertSecondToHHMMSS(inputData.cutEnd)}
          </div>
        </div>
      </Loading>
    
    </div>
    <div className="video-preview-divider" />
    <QrCodeDownload
      type={videoInfo.type}
      link={window.location.origin + videoInfo.linkDownload}
    />
  </div>;
}

export default VideoPreview;