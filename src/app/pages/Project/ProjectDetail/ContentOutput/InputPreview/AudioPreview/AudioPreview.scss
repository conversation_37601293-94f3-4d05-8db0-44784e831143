.audio-preview-container {
  display: flex;
  flex-direction: row;
  gap: 24px;
  page-break-inside: avoid;
  max-width: 100%;

  .audio-preview-inner {
    display: flex;
    flex-direction: row;
    gap: 24px;
    width: calc(100% - 190px - 24px - 24px - 1px);

    .audio-preview__thumbnail {
      width: 312px;
      height: 176px;
      border-radius: 8px;
      overflow: hidden;

      > img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .audio-preview-info {
      width: 367px;
      text-align: center;
      align-self: center;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .audio-info__title {
        color: var(--primary-colours-blue);
        font-weight: 600;
      }

      .audio-info__preview {
        align-self: center;

        .audio-info-preview__link {
          display: flex;
          max-width: 333px;
          justify-content: center;

          .ant-btn {
            padding: 0 15px;
          }
        }
      }

      .audio-info__time {
        display: flex;
        flex-direction: row;
        row-gap: 8px;
        column-gap: 24px;
        align-self: center;
        color: var(--typo-colours-support-blue-light);
        flex-wrap: wrap;
        justify-content: center;

        .audio-info__time-duration {

        }

        .audio-info__time-start {

        }

        .audio-info__time-end {

        }
      }
    }
  }

  .audio-preview-divider {
    display: flex;

    &:before {
      height: 100%;
      width: 1px;
      content: '';
      background-color: var(--support-colours-grey-light);
    }
  }
}

@media screen and (max-width: 1919.98px) {
  .audio-preview-container {
    justify-content: space-between;

    .audio-preview-inner {
      width: 367px;
      flex-direction: column;
      align-items: center;

      .audio-preview-info {
        width: 100%;

        .audio-info__preview {
          padding: 0 17px;
        }
      }
    }
  }
}

@media screen and (max-width: 1535.98px) {
  .audio-preview-container {
    flex-direction: column;
    align-items: center;

    .audio-preview-inner {
      flex-direction: column;
      align-items: center;

      .audio-preview-info {
        width: 367px;
      }
    }

    .audio-preview-divider {
      display: none;
    }
  }
}