import React, { useEffect, useState } from "react";

import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import Loading from "@component/Loading";

import { API } from "@api";

import { getImageDetail } from "@services/Image";

import "./ImagePreview.scss";

function ImagePreview({ ...props }) {
  const inputData = props.inputData || useContent()?.inputData;
  
  const [isLoading, setLoading] = useState(true);
  const [imageInfo, setImageInfo] = useState(props?.imageInfo || null);
  
  useEffect(() => {
    getImageData(inputData?.imageId || inputData?.topicImageId);
  }, [inputData?.imageId, inputData?.topicImageId]);
  
  async function getImageData(imageId) {
    if (!imageId) return;
    const apiResponse = await getImageDetail(imageId);
    if (apiResponse?.imageFileId) {
      setImageInfo(apiResponse);
    }
  }
  
  function onLoadImage() {
    setLoading(false);
  }
  
  return <Loading active={isLoading} className="input-image-preview-container">
    <img
      src={API.STREAM_ID.format(imageInfo?.imageFileId)}
      alt="preview"
      onLoad={onLoadImage}
    />
  </Loading>;
}

export default ImagePreview;