import React from "react";
import { useTranslation } from "react-i18next";

import TypingEffect from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TypingEffect";
import Answer from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Answer";
import TfQuestion from "./TfQuestion";
import TfAnswer from "./TfAnswer";

function TrueFalseValue({ responseSelected, ...props }) {
  const { t } = useTranslation();
  const outputData = responseSelected.output;
  
  const trueFalseHtml = <div className="true-false-output">
    <div className="true-false-output__title">{t("QUESTION")}</div>
    
    <TfQuestion questions={outputData?.questions} />
    
    <Answer>
      <TfAnswer correctAnswers={outputData.correctAnswers} />
    </Answer>
  </div>;
  
  if (responseSelected.typingEffect)
    return <TypingEffect html={trueFalseHtml} />;
  return trueFalseHtml;
}

export default TrueFalseValue;