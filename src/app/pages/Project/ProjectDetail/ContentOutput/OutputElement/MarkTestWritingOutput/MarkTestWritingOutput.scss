@mixin generate-nested-first-items($depth, $prefix: '*') {
  @for $i from 1 through $depth {
    > *:first-child {
      margin-top: 0;

      @if $i < $depth {
        @include generate-nested-first-items($depth - 1);
      }
    }
  }
}

@mixin generate-nested-last-items($depth, $prefix: '*') {
  @for $i from 1 through $depth {
    > *:last-child {
      margin-bottom: 0;

      @if $i < $depth {
        @include generate-nested-last-items($depth - 1);
      }
    }
  }
}

.mark-test-writing-value {
  @include generate-nested-first-items(6);
  @include generate-nested-last-items(6);

  overflow: auto;
  padding: 24px;
  background: var(--primary-colours-blue-navy-light-1);
  border-radius: 8px;
  width: 100%;

  * {
    text-wrap: wrap;
  }

  .mark-test-writing-value__score{
    font-weight: 700;
    font-size: 24px;
  }
}


.mark-test-writing-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-explain-error {
      margin-bottom: 0 !important;
    }
  }
}
