.open-question-output {

  .true-false__content {
    display: flex;
    flex-direction: row;
    gap: 8px;

    .true-false__input {
      flex: 1;

      .true-false__question textarea {
        font-weight: 700;
      }
      .true-false-output__question-item{
        page-break-inside: auto;
      }

      .true-false__radio .ant-radio-group {
        display: flex;
        gap: 16px;

        .ant-radio-button-wrapper {
          border-radius: 4px;
          border-width: 1px;
          padding-left: 23px;
          padding-right: 23px;

          &:not(.ant-radio-button-wrapper-checked) {
            border: 1px solid var(--lighttheme-content-background-stroke);
          }

          &:before {
            content: none;
          }
        }
      }
    }

    .true-false__action {
      //height: 40px;
      display: flex;
      align-items: center;

      button.ant-btn {
        box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.10);
      }
    }

    .true-false__explain {
      margin: 0;
    }
  }

}
