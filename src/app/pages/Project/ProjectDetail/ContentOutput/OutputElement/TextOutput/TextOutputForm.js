import React, { useEffect } from "react";
import { Input } from "antd";
import clsx from "clsx";

import { useProject } from "@app/pages/Project";
import { AntForm } from "@component/AntForm";

import RULE from "@rule";

function TextOutputForm({ responseSelected, isEditing, setEditing, ...props }) {
  const { handleSubmitResponse } = useProject();
  
  const { content, outputForm } = props;
  
  useEffect(() => {
    outputForm.setFieldsValue({ response: responseSelected?.output?.text });
  }, [responseSelected]);
  
  
  useEffect(() => {
    if (!isEditing) {
      outputForm.setFieldsValue({ response: responseSelected?.output?.text });
    }
  }, [isEditing]);
  
  function onFinishResponse(values) {
    handleSubmitResponse(content._id, { _id: responseSelected._id, output: { text: values.response } })
      .then(() => setEditing(false));
  }
  
  return <AntForm
    id={`form-response-${responseSelected._id}`}
    form={outputForm}
    onFinish={onFinishResponse}
    className={clsx("text-output", { hidden: !isEditing })}
  >
    <AntForm.Item name="response" rules={[RULE.REQUIRED]}>
      <Input.TextArea className="text-output__text-area" autoSize disabled={!isEditing} />
    </AntForm.Item>
  </AntForm>;
}

export default TextOutputForm;