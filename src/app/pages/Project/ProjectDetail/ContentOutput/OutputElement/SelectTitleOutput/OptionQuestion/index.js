import React from "react";

import "./OptionQuestion.scss";

function OptionQuestion({ options }) {
  return <div className="option-list-output__value-container">
    {options?.map((option, index) => {
      return <div key={option.optionId || index} className="option-list-output__question-item">
        <div className="option-list-output__question-index">
          <span className={"js-index-question-margin"}>{option.optionId}</span>
        </div>
        <div className="option-list-output__question-text">{option.text}</div>
      </div>;
    })}
  </div>
}

export default OptionQuestion;