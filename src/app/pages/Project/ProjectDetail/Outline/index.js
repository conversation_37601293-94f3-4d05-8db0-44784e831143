import React from "react";
import { connect } from "react-redux";

import { useProject } from "@app/pages/Project";

import OutlineMobile from "@app/pages/Project/ProjectDetail/Outline/OutlineMobile";
import OutlineDesktop from "@app/pages/Project/ProjectDetail/Outline/OutlineDesktop";

import "./Outline.scss";

function Outline() {
  const { isShowOutlineMobile } = useProject();
  const { projectContentData, isExam, isMark } = useProject();
  
  if (isMark) return;
  if (!projectContentData?.length && !isExam) return;
  
  if (isShowOutlineMobile)
    return <OutlineMobile />;
  return <OutlineDesktop />;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(Outline);