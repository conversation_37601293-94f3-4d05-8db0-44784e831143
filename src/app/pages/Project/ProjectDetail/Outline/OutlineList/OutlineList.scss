@import "src/app/styles/scroll";

.outline-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 15px;
  border-radius: 8px;
  background: #FFF;
  max-height: calc(100vh - 32px - 32px - 72px - 32px - 24px);
  overflow-y: auto;
  box-shadow: var(--shadow-level-2);

  .outline__title {
    font-size: 24px;
    font-weight: 600;
    padding: 24px 24px 0 24px;
  }

  .outline__list {
    flex: 1;
    padding: 0 24px 0 24px;
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    @extend .scrollbar;
    @extend .scrollbar-show;

    .outline__item {
      user-select: none;
      cursor: pointer;
      display: flex;
      flex-direction: row;
      gap: 4px;
      padding: 7.5px;
      margin: 0 -7.5px;

      &.outline__item-ghost {
        opacity: 0.4;
      }

      &.outline__item-chosen {
        background-color: var(--blue-light-2);
      }

      &.outline__item-fallback {
        //background: linear-gradient(0.5turn, transparent, var(--blue-light-2), transparent);
      }

      &.outline__item-active {
        .outline-item__icon:before {
          content: '';
          background-image: url("../../../../../../asset/gif/outline-active.gif");
          height: 24px;
          width: 24px;
          background-size: cover;
        }
      }

      .outline-item__icon {
        display: flex;
        width: 24px;
        height: 17.5px;
      }

      .outline-item__text {
        flex: 1;
        font-size: 14px;
      }
    }
  }
}