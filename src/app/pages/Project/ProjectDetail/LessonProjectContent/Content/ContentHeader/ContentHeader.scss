.project-content__header {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .project-content__title {
    display: flex;
    flex-direction: row;
    column-gap: 24px;
    row-gap: 8px;
    flex-wrap: wrap;

    .project-content__title-text {
      color: var(--primary-colours-blue-navy);
      font-size: 24px;
      font-weight: 700;
      display: flex;
      gap: 8px;

      .project-content__title-video-tutorial {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }

    .project-content__title-action {
      margin-left: auto
    }
  }

  .project-tool-item__description {
    color: var(--typo-colours-support-blue-light);
  }
}