import React, { useEffect, useState } from "react";
import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import { CONSTANT, TYPE_OF_TOOL } from "@constant";
import { API } from "@api";
import { getMediaFileName } from "@services/Content";

import VoiceViolet from "@component/SvgIcons/Voice/VoiceViolet";
import Download from "@component/SvgIcons/Download";
import PlaySoft from "@component/SvgIcons/Play/PlaySoft";


import "./DownloadMedia.scss";

function DownloadMedia() {
  const { responseSelected, isEditing } = useContent();
  
  const [linkDownload, setLinkDownload] = useState({
    video: "",
    audio: "",
  });
  const [mediaFileName, setMediaFileName] = useState("");
  
  useEffect(() => {
    if (responseSelected?.inputId) {
      handleLinkDownload(responseSelected.inputId);
      getMediaName(responseSelected.inputId);
    }
  }, [responseSelected?.inputId?._id]);
  
  async function getMediaName(input) {
    const response = await getMediaFileName(input._id);
    setMediaFileName(response);
  }
  
  function handleLinkDownload(input) {
    const { inputType, inputData } = input;
    const { offlineVideoId, audioId, url, cutStart, cutEnd } = inputData || {};
    let video = "", audio = "";
    switch (inputType) {
      case TYPE_OF_TOOL.OFFLINE_VIDEO:
        video = API.DOWNLOAD_OFFLINE_VIDEO.format(offlineVideoId, cutStart, cutEnd);
        audio = API.DOWNLOAD_OFFLINE_AUDIO.format(offlineVideoId, cutStart, cutEnd);
        break;
      case TYPE_OF_TOOL.VIDEO:
        audio = API.DOWNLOAD_AUDIO_YOUTUBE.format(url, cutStart, cutEnd);
        break;
      case TYPE_OF_TOOL.AUDIO:
        audio = API.DOWNLOAD_AUDIO.format(audioId, cutStart, cutEnd);
        break;
      default:
    }
    setLinkDownload({ video, audio });
  }
  
  const showDownload = !isEditing
    && responseSelected?.state?.toUpperCase() === CONSTANT.DONE
    && [CONSTANT.OFFLINE_VIDEO, CONSTANT.AUDIO, CONSTANT.VIDEO].includes(responseSelected?.inputId?.inputType.toUpperCase());
  
  if (!showDownload) return null;
  
  return <div className="download-media-container">
    <a
      href={linkDownload.audio}
      className="ant-btn ant-btn-sm ant-btn-light-blue download-media-item"
    >
      <span className="ant-btn-icon">
        <VoiceViolet />
      </span>
      <span className="media-name">
        {mediaFileName}
      </span>
      <span className="media-extension">
        .mp3
      </span>
      <span className="ant-btn-icon">
        <Download />
      </span>
    </a>
    
    {responseSelected.inputId.inputType.toUpperCase() === CONSTANT.OFFLINE_VIDEO
      && <a
        href={linkDownload.video}
        className="ant-btn ant-btn-sm ant-btn-light-blue download-media-item"
      >
        <span className="ant-btn-icon">
          <PlaySoft />
        </span>
        <span className="media-name">
          {mediaFileName}
        </span>
        <span className="media-extension">
          .mp4
        </span>
        <span className="ant-btn-icon">
          <Download />
        </span>
      </a>}
  </div>;
}

export default DownloadMedia;