import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Form } from "antd";
import clsx from "clsx";
import _ from "lodash";

import { useProject } from "@app/pages/Project";

import AntButton from "@component/AntButton";
import Loading from "@component/Loading";
import ContentActions from "./ContentActions";
import ContentHeader from "./ContentHeader";
import ContentInput from "@app/pages/Project/ProjectDetail/ContentInput";
import ContentOutput from "@app/pages/Project/ProjectDetail/ContentOutput";

import ContentFooter from "./ContentFooter";
import DownloadMedia from "./DownloadMedia";

import { BUTTON, CONSTANT, VISIBLE_TOOL } from "@constant";
import { cloneObj } from "@common/functionCommons";

import { streamSSEResponse } from "@services/Content";
import { getAllPDFForm } from "@services/PDFForm";

import "./Content.scss";

export const ContentContext = React.createContext();

function Content({ listAllTool, user, content }) {
  const { t } = useTranslation();
  
  const eventSource = useRef(undefined);
  const [outputForm] = Form.useForm();
  
  const { createContentThumbnail } = useProject();
  const { isAllowEditing, projectContentData, setProjectContentData, isPreviewProject } = useProject();
  const [instructionSelected, setInstructionSelected] = useState(undefined);
  const [isDisabledSubmit, setDisabledSubmit] = useState(false);
  
  
  const [isEditing, setEditing] = useState(false);
  const [historyMorePDF, setHistoryMorePDF] = useState([]);
  const [isMinimize, setMinimize] = useState(false);
  const [isFetchingContent, setFetchingContent] = useState(false);
  
  useEffect(() => {
    getHistoryMoreInfoPDF();
  }, []);
  
  const getHistoryMoreInfoPDF = async () => {
    const dataResponse = await getAllPDFForm({ ownerId: user?._id });
    if (dataResponse) {
      setHistoryMorePDF(dataResponse);
    }
  };
  
  const toolInfo = useMemo(() => {
    
    function checkDisable(tool) {
      if (tool.isDeleted || tool.visible === VISIBLE_TOOL.developing.value) return true;
      if (tool.visible === VISIBLE_TOOL.private.value && user?.organizationId?._id) {
        return !toolData.organizationIds.includes(user.organizationId._id);
      }
      return false;
    }
    
    const toolData = listAllTool?.find((tool) => tool._id === content?.toolId?._id);
    if (toolData) {
      toolData.isDisable = checkDisable(toolData);
    }
    return toolData;
  }, [listAllTool, content]);
  
  const isDisabledContent = useMemo(() => !isAllowEditing || toolInfo?.isDisable, [isAllowEditing, toolInfo]);
  const responseSelected = useMemo(() => content?.responses?.find(response => response.isActivate), [projectContentData, content]);
  
  const [responseId, inputData] = useMemo(() => {
    return [responseSelected?._id, responseSelected?.inputId?.inputData];
  }, [responseSelected]);
  
  
  useEffect(() => {
    if (responseId && responseSelected.state === CONSTANT.PROCESSING.toLowerCase()) {
      handleStreamOutput();
    }
    return () => closeEv();
  }, [responseId]);
  
  function handleStreamOutput() {
    eventSource.current = streamSSEResponse(responseId);
    eventSource.current.addEventListener(responseId, async (event) => {
      const data = JSON.parse(event?.data);
      
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState.forEach(state => {
          if (state._id === content._id) {
            state.responses = state.responses.map(response => {
              if (response._id === data._id) {
                if (data.canceledSubmit || data.outputType === "audio") delete response.typingEffect;
                
                response.output = data.output;
                response.outputType = data.outputType;
                response.plaintext = data.plaintext;
                response.state = data.state;
              }
              return response;
            });
          }
        });
        return newState;
      });
      
    });
    eventSource.current.addEventListener("error", async (error) => {
      closeEv();
    });
  }
  
  function closeEv() {
    eventSource.current?.close();
    eventSource.current = null;
  }
  
  //if ((isPreviewProject && (!responseSelected || responseSelected.state !== CONSTANT.DONE.toLowerCase())) || !toolInfo) return;
  if (!toolInfo) return;
  
  const hideContent = useMemo(() => {
    return isPreviewProject && (!responseSelected || responseSelected.state !== CONSTANT.DONE.toLowerCase());
  }, [isPreviewProject, responseSelected]);
  
  const contentClassName = clsx("project-content-layout",
    {
      "project-content-layout-hidden": content.isHidden,
      "hidden": hideContent,
    },
  );
  
  return <ContentContext.Provider
    value={{
      content, toolInfo,
      inputData,
      isDisabledSubmit, setDisabledSubmit,
      responseSelected,
      outputForm,
      isDisabledContent,
      isEditing, setEditing,
      isMinimize, setMinimize,
      instructionSelected, setInstructionSelected,
      historyMorePDF, setHistoryMorePDF,
      setFetchingContent,
    }}
  >
    <div
      id={`js-project-content-${content?._id}`}
      className={contentClassName}>
      <ContentActions
        content={content}
        toolInfo={toolInfo}
        isMinimize={isMinimize}
        setMinimize={setMinimize}
      
      />
      <ContentHeader toolInfo={toolInfo} isMinimize={isMinimize} />
      <Loading
        active={isFetchingContent}
        className={clsx("project-content__body", { "project-content__body-hidden": isMinimize })}
      >
        <ContentInput
          content={content}
          toolInfo={toolInfo}
          isEditing={isEditing}
          inputData={inputData}
          isDisabledContent={isDisabledContent}
          setFetchingContent={setFetchingContent}
          instructionSelected={instructionSelected}
          setInstructionSelected={setInstructionSelected}
        />
        
        {!!isAllowEditing && !toolInfo.isDisable && <div className="project-content__submit">
          <AntButton
            htmlType="submit"
            size="large"
            type={BUTTON.DEEP_NAVY}
            form={`${CONSTANT.INSTRUCTION}-${content._id}`}
            disabled={isEditing}
          >
            {t("SUBMIT")}
          </AntButton>
        </div>}
        
        <ContentOutput />
        
        <DownloadMedia />
        
        <ContentFooter handleStreamOutput={handleStreamOutput} />
      </Loading>
    </div>
  </ContentContext.Provider>;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  const { listAllTool } = store.tool;
  return { user, listAllTool };
}

const ConnectedContent = connect(mapStateToProps)(Content);
const useContent = () => useContext(ContentContext);

export { ConnectedContent as Content, useContent };
