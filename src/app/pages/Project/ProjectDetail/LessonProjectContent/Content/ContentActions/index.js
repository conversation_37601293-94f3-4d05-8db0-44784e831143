import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { confirm } from "@component/ConfirmProvider";
import { useProject } from "@app/pages/Project";

import { CONSTANT } from "@constant";
import { cloneObj } from "@common/functionCommons";

import { deleteContent, moveDownContent, moveUpContent, updateContent } from "@services/Content";
import { getSampleContent } from "@services/Sample";
import { getProjectDetail, removeProjectThumbnail } from "@services/Project";

import AntButton from "@component/AntButton";
import ArrowUp from "@component/SvgIcons/ArrowUp";
import ArrowDown from "@component/SvgIcons/ArrowDown";
import Eye from "@component/SvgIcons/Eye";
import Trash from "@component/SvgIcons/Trash";
import Minimize from "@component/SvgIcons/Minimize";
import Maximize from "@component/SvgIcons/Maximize";
import Training from "@component/SvgIcons/Training";
import UNION from "@src/asset/icon/union/union.svg";

import "./ContentAction.scss";


const ContentActions = ({ iconOnly = false, ...props }) => {
  const { t } = useTranslation();
  
  const {
    isExam,
    isAllowEditing,
    projectContentData,
    setProjectContentData,
    setGuideData,
    projectId,
    createContentThumbnail,
    handleGetProjectData,
  } = useProject();
  const { content, toolInfo, isMinimize, setMinimize } = props;
  
  const handleMoveDown = async () => {
    const apiResponse = await moveDownContent({ _id: content._id });
    if (apiResponse) {
      const updateContents = [
        { ...projectContentData[content.contentIndex], contentIndex: content.contentIndex },
        { ...projectContentData[content.contentIndex - 1], contentIndex: content.contentIndex + 1 },
      ];
      setProjectContentData([...projectContentData.slice(0, content.contentIndex - 1), ...updateContents, ...projectContentData.slice(content.contentIndex + 1)]);
    }
  };
  
  const handleMoveUp = async () => {
    const apiResponse = await moveUpContent({ _id: content._id });
    if (apiResponse) {
      const updateContents = [
        { ...projectContentData[content.contentIndex - 1], contentIndex: content.contentIndex - 1 },
        { ...projectContentData[content.contentIndex - 2], contentIndex: content.contentIndex },
      ];
      setProjectContentData([...projectContentData.slice(0, content.contentIndex - 2), ...updateContents, ...projectContentData.slice(content.contentIndex)]);
    }
  };
  
  const handleHideOrShow = async () => {
    const dataRequest = {
      _id: content._id,
      isHidden: !content.isHidden,
    };
    const apiResponse = await updateContent(dataRequest);
    if (apiResponse) {
      setProjectContentData(prevState => {
        return prevState.map(state => {
          return { ...state, isHidden: state._id === apiResponse._id ? apiResponse.isHidden : state.isHidden };
        });
      });
    }
  };
  const handleDelete = () => {
    confirm.delete({
      content: t("ARE_YOU_SURE_YOU_WANT_TO_DELETE_THE_TOOL_CONTENT?"),
      handleConfirm: async () => {
        const isDeleteAllContent = projectContentData.length === 1;
        const isDeleteFirstContent = !isDeleteAllContent && content.contentIndex === 1;
        const [contentResponse, _] = await Promise.all([
          deleteContent(content._id),
          isDeleteAllContent ? removeProjectThumbnail(projectId) : [],
        ]);
        if (contentResponse) {
          await handleGetProjectData();
          
          if (isDeleteFirstContent) {
            createContentThumbnail(`js-project-content-${content._id}`);
          }
        }
      },
    });
  };
  
  
  const onClickGuide = async () => {
    const apiResponse = await getSampleContent(toolInfo?._id);
    if (apiResponse) {
      const { input, response, ...guideResponse } = apiResponse;
      guideResponse.lastInput = input;
      guideResponse.responses = response ? [{ ...response, isActivate: true, state: CONSTANT.DONE.toLowerCase() }] : [];
      setGuideData({ showGuide: true, toolInfo, content: guideResponse });
    }
  };
  
  function renderButtonTitle(titleLang) {
    //if (iconOnly) return null;
    return iconOnly ? null : t(titleLang);
  }
  
  if (!isAllowEditing) return;
  return <div className="content-header">
    {projectContentData[content.contentIndex] && <AntButton
      size="small"
      className="content-header__action"
      icon={<ArrowDown />}
      onClick={handleMoveDown}
    >
      {renderButtonTitle("MOVE_DOWN")}
    </AntButton>}
    {projectContentData[content.contentIndex - 2] && <AntButton
      size="small"
      className="content-header__action"
      icon={<ArrowUp />}
      onClick={handleMoveUp}
    >
      {renderButtonTitle("MOVE_UP")}
    </AntButton>}
    
    {/* {!isExam && <AntButton
      size="small"
      className="content-header__action"
      icon={<Eye />}
      onClick={handleHideOrShow}
    >
      {renderButtonTitle(content.isHidden ? "SHOW" : "HIDE")}
    </AntButton>} */}
    
    <AntButton
      size="small"
      className="content-header__action"
      icon={isMinimize ? <Maximize /> : <Minimize />}
      onClick={() => setMinimize(!isMinimize)}
    >
      {renderButtonTitle(isMinimize ? "EXPAND" : "COLLAPSE")}
    </AntButton>
    
    {toolInfo?.existGuide && !isExam && <AntButton
      size="small"
      className="content-header__action"
      icon={<Training />}
      onClick={onClickGuide}
    >
      {t("GUIDE")}
      <AntButton.Icon>
        <img src={UNION} alt="union" />
      </AntButton.Icon>
    </AntButton>}
    
    <AntButton
      size="small"
      className="content-header__action"
      icon={<Trash />}
      onClick={handleDelete}
    >
      {renderButtonTitle("DELETE")}
    </AntButton>
  </div>;
};
export default ContentActions;
