import React from "react";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";
import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";

import AntButton from "@component/AntButton";
import { BUTTON } from "@constant";

import AlertCircleIcon from "@component/SvgIcons/AlertCircleIcon";

import "./OutputError.scss";


function OutputError(props) {
  const { t } = useTranslation();
  const { isAllowEditing } = useProject();
  const responseSelected = props?.responseSelected || useContent();
  
  return <>
    <div className="output-status">
      <div className="output-status__title">
        <AlertCircleIcon />
        <span>{t("OOPS_WE_ARE_SORRY")}</span>
      </div>
      <div className="output-status__text">
        {responseSelected?.output?.message}
      </div>
      
      {isAllowEditing && <div className="output-status__action">
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={props.handleRegenerate}
        >
          {t("RETRY")}
        </AntButton>
        
        <AntButton
          size="large"
          type={BUTTON.DEEP_RED}
          onClick={props.deleteOutput}
        >
          {t("DELETE")}
        </AntButton>
      </div>}
    </div>
  </>;
}

export default OutputError;