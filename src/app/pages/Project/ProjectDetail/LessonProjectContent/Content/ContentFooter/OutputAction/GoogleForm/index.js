import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";
import { LINK } from "@link";

import { calGoogleWindowPosition } from "@common/functionCommons";
import { createForm } from "@services/Google";

function GoogleForm({ responseSelected }) {
  const { t } = useTranslation();
  
  
  const isRequestForm = useRef(false);
  const [isLoading, setLoading] = useState(false);
  
  function handleGoogle() {
    const { width, height, top, left } = calGoogleWindowPosition(600, 600);
    const options = {
      response_type: "code",
      client_id: process.env.GOOGLE_FORM_CLIENT_ID,
      redirect_uri: `${window.location.origin}${LINK.GOOGLE_FORM_CALLBACK}`,
      access_type: "offline",
      prompt: "consent",
      scope: [
        //'https://www.googleapis.com/auth/userinfo.profile',
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/forms.body",
        "https://www.googleapis.com/auth/forms.body.readonly",
        "https://www.googleapis.com/auth/forms.responses.readonly",
        "https://www.googleapis.com/auth/drive.file",
      ].join(" "),
    };
    const qs = new URLSearchParams(options);
    
    window.open(
      "https://accounts.google.com/o/oauth2/v2/auth?" + qs.toString(),
      "google-login",
      `
      scrollbars=yes,
      width=${width},
      height=${height},
      top=${top},
      left=${left}
      `,
    );
  }
  
  async function handeCreateGoogleForm() {
    setLoading(true);
    const apiResponse = await createForm(responseSelected);
    console.log("apiResponse", apiResponse);
    if (apiResponse?.hasOwnProperty("success") && !apiResponse.success) {
      toast.error("AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR");
    } else if (apiResponse?.formId) {
      const url = `https://docs.google.com/forms/d/${apiResponse.formId}/edit`;
      window.open(url, "_blank", "noopener,noreferrer");
      isRequestForm.current = false;
    } else {
      // login
      isRequestForm.current = true;
      handleGoogle();
    }
    setLoading(false);
  }
  
  
  useEffect(() => {
    
    function onCallback(event) {
      if (isRequestForm.current && event.storageArea === localStorage && event.key === CONSTANT.GOOGLE_FORM) {
        if (event.newValue === CONSTANT.SUCCESS) {
          handeCreateGoogleForm();
        } else if (event.newValue === CONSTANT.ERROR) {
          toast.error("AN_ERROR_OCCURRED");
        }
        localStorage.removeItem(CONSTANT.GOOGLE_FORM);
      }
    }
    
    window.addEventListener("storage", onCallback, false);
    return () => window.removeEventListener("storage", onCallback);
  }, []);
  
  if (responseSelected?.outputType !== "multi_choice") return;
  return <>
    <AntButton
      size="large"
      type={BUTTON.DEEP_PURPLE}
      loading={isLoading}
      onClick={handeCreateGoogleForm}
    >
      {t("CREATE_GOOGLE_FORM")}
    </AntButton>
  </>;
}

export default GoogleForm;