.output-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;

  .output-status__title {
    color: #000;
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    font-size: 24px;
    font-weight: 600;

    svg {
      height: 24px;
      width: 24px;
    }
  }

  .output-status__text {

    font-size: 16px;
    line-height: 20px;
    align-self: center;
  }

  .output-status__action {
    display: flex;
    gap: 16px;
  }
}

.content-action {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .content-action__regenerate {
    display: flex;
    flex-direction: row;
    gap: 8px;

    .regenerate__input {
      flex: 1;
      margin: 0;
    }

    .regenerate__submit {
      margin-left: auto;
      border-radius: 4px;
      box-shadow: var(--shadow-level-2);
      place-self: end;
    }
  }

  .content-action__result-tool {
    //display: flex;
    //justify-content: space-between;

    .result-tool__vote {
      float: left;
      display: flex;
      flex-direction: row;
      gap: 16px;

      .ant-btn {
        border-radius: 4px;
        box-shadow: var(--shadow-level-2);
      }
    }

    .result-tool__action {
      float: right;
      display: flex;
      gap: 16px;

      > .ant-btn {
        box-shadow: var(--shadow-level-2);
      }
    }
  }

  .content-action__next-tool {
    display: flex;
    flex-direction: column;

    .next-tool__panel {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .ant-btn {
        border-radius: 4px;
        box-shadow: var(--shadow-level-2);
      }
    }

    .next-tool__content {
      .ant-collapse .ant-collapse-item {
        .ant-collapse-header {
          display: none;
        }

        .ant-collapse-content {
          padding-top: 8px;

          .ant-collapse-content-box {
            padding: 0;

            .content-action__list {
              display: flex;
              column-gap: 10px;
              row-gap: 8px;
              flex-wrap: wrap;
            }
          }
        }
      }
    }
  }
}

.save-example-tooltip {
  .ant-tooltip-arrow {

    &::before {
      display: none;
    }

    &::after {
      background-color: var(--background-light-background-2);
    }
  }

  .ant-tooltip-content {
    box-shadow: var(--shadow-level-2);
  }

  .ant-tooltip-inner {
    //padding: 8px 16px;
    background-color: var(--background-light-background-2);
    border-radius: 8px;
    color: var(--typo-colours-support-blue-dark);
    display: flex;
    flex-direction: column;
    gap: 8px;
    white-space: nowrap;

    .save-as-example__actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }
  }
}