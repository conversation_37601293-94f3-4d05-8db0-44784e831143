.speaking-audio {
  padding: 14px 20px;
  border-bottom: 1px solid #d1d1d1;
  display: flex;
  justify-content: space-between;
  gap: 20px;

  .speaking-audio-player {
    display: flex;
    flex-direction: row;
    gap: 8px;
    flex: 1;

    .speaking-audio-player__play {
      display: flex;
      flex-direction: row;
      align-self: center;

      .ant-btn {
        border-color: transparent;

        .ant-btn-icon img {
          width: 28px;
          height: 28px;
        }
      }
    }

    .speaking-audio-player__current-time, .speaking-audio-player__duration {
      align-self: center;
    }

    .speaking-audio-player__seek-bar {
      align-self: center;
      flex: 1;
      padding: 0 5px;
    }
  }
}