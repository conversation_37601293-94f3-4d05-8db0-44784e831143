import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

import { useMarkStudent } from "..";

import CustomCKEditor from "@src/app/component/CKEditor";
import SuggestEvaluation from "./Evaluation/SuggestEvaluation";
import VocabularyEvaluation from "./Evaluation/VocabularyEvaluation";
import OutputProcessing from "../../LessonProjectContent/Content/ContentFooter/OutputProcessing";
import OutputError from "../../LessonProjectContent/Content/ContentFooter/OutputError";
import CriteriaAssessment from "./Evaluation/CriteriaAssessment";
import Evaluation from "./Evaluation/Evaluation";
import ImprovedEssay from "./Evaluation/ImprovedEssay";
import AntButton from "@src/app/component/AntButton";

import { confirm } from "@component/ConfirmProvider";
import { createOneMarkTest } from '@services/Report';

import { BUTTON, CONSTANT } from "@constant";

import './MarkStudentOutput.scss';
import { cleanFileName, cloneObj, downloadUsingBrowser, getFileExtension } from "@src/common/functionCommons";
import { useProject } from "../../..";
import { API } from "@api";
import EssayAssessment from "./Evaluation/EssayAssessment";
import { retryResponse } from "@src/app/services/Response";
import IdeasSuggest from "./Evaluation/IdeasSuggest";
import Arguments from "./Evaluation/Arguments";
import GrammarStructureSuggestion from "./Evaluation/GrammarStructureSuggestion";

const MarkStudentOutput = () => {
  const { t } = useTranslation();
  const { projectId, projectData, setProjectContentData } = useProject();
  const { handleDeleteResponse } = useProject();
  const { contentData, setMarking } = useMarkStudent();
  const { responses } = contentData || {};

  const [assignment, setAssignment] = useState('');

  const responseActive = useMemo(() => {
    if (!responses || !responses?.length) return null;
    return responses?.find(response => response.isActivate);
  }, [responses]);

  const {
    suggests, vocabularies, evaluations, criteria, ideas, arguments: argumention,
    inputText, improvedEssay, essayAssessment, reason, grammarStructureSuggestion, state: resultState
  } = responseActive?.output || {};

  useEffect(() => {
    if (inputText) setAssignment(inputText.replace(/\n/g, '<br>'));
  }, [inputText]);

  const saveAsWord = async () => {
    const apiRequest = {
      projectId,
      responseId: responseActive?._id,
    };

    const filePrevew = await createOneMarkTest(apiRequest);
    if (filePrevew?.fileName) {
      const displayName = cleanFileName(projectData.projectName) + "." + getFileExtension(filePrevew.fileName);
      const workspaceId = projectData?.workspaceId;
      const logParamsString = `&workspaceId=${workspaceId}&projectId=${projectId}`;
      downloadUsingBrowser(API.DOWNLOAD_REPORT_FILE.format(filePrevew.fileName, displayName) + logParamsString);
      // handleDownloadFinish();
    }
  }

  function cancelResponseProcessing() {
    if (responses?.length) {
      deleteOutput();
    }
  }

  async function handleRegenerate(values) {
    const apiRequest = { _id: responseActive?._id, projectId, workspaceId: projectData?.workspaceId };
    const apiResponse = await retryResponse(apiRequest);
    if (apiResponse) {
      await setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState.forEach(state => {
          if (state._id === contentData._id) {
            state.responses = state.responses?.map(response => {
              return response._id === apiResponse._id ? { ...apiResponse, typingEffect: true } : response;
            });
          }
        });
        return newState;
      });
    }
  }

  function deleteOutput() {
    confirm.delete({
      content: t("ARE_YOU_SURE_YOU_WANT_TO_DELETE_THE_TOOL_RESULT?"),
      handleConfirm: async () => {
        await handleDeleteResponse(contentData._id, responseActive?._id);
        setMarking(false);
      },
    });
  }

  const contentFooterProps = {
    deleteOutput,
    cancelResponseProcessing,
    handleRegenerate,
    responseSelected: responseActive,
  };

  const outputState = responseActive?.state?.toUpperCase();
  if (outputState === CONSTANT.PROCESSING) {
    return <OutputProcessing {...contentFooterProps} />;
  } else if (outputState === CONSTANT.ERROR) {
    return <OutputError {...contentFooterProps} />;
  } else if (outputState === CONSTANT.DONE) {

    return (<div className="mark-student-output">
      <div className="mark-student-output__assignment">
        <div className="mark-student-output__title">{t("ESSAY")}</div>
        <div className="assignment__content">
          <CustomCKEditor
            value={assignment}
            allowedContent='span(highlight-text)' />
        </div>
        {resultState !== 'invalid' && <AntButton
          className={"mark-student-output__save-btn"}
          type={BUTTON.GHOST_BLUE}
          onClick={() => saveAsWord()}
        >
          {t("DOWNLOAD")}
        </AntButton>}
      </div>
      <div className="mark-student-output__evaluation">
        <div className="mark-student-output__title">{t("RESULT")}</div>
        <div className="evaluation__content">
          {reason && <div className="evaluation__content__item">{reason}</div>}
          {suggests && <SuggestEvaluation suggests={suggests} setAssignment={setAssignment} assignment={assignment} />}
          {essayAssessment && <EssayAssessment essayAssessment={essayAssessment} />}
          {argumention && <Arguments argumention={argumention} />}
          {criteria && <CriteriaAssessment criteria={criteria} />}
          {evaluations && <Evaluation evaluations={evaluations} />}
          {improvedEssay && <ImprovedEssay improvedEssay={improvedEssay} />}
          {vocabularies && <VocabularyEvaluation vocabularies={vocabularies} />}
          {ideas && <IdeasSuggest ideas={ideas} />}
          {grammarStructureSuggestion && <GrammarStructureSuggestion grammarStructureSuggestion={grammarStructureSuggestion} />}
        </div>

      </div>
    </div>)
  }

  return <></>;
};

export default MarkStudentOutput;