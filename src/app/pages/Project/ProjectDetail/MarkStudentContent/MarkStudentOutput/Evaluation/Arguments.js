import { calculateIeltsAverage, convertCamelAndNumberToTitleCase } from "./functionCommon";
import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";

const Arguments = ({ argumention = {} }) => {
  const { t } = useTranslation();
  if (!Object.keys(argumention)?.length) return null;
  
  return <div className="evaluation__content__item evaluation-criteria-assessment">
    <div className="content__item__title evaluation-task-achievement__title">{t("ARGUMENTS")}</div>
    
    {typeof argumention === "object" ? Object.entries(argumention).map(([key, values], index) => {
      const feedback = values.feedback || [];
      
      return (
        <div key={index}>
          <div className="criteria-assessment__item-title">{convertCamelAndNumberToTitleCase(key)}</div>
          <ul>
            <li>{values.original}
              <ul>
                {feedback.map((item, itemIndex) => (
                  <li key={itemIndex}>
                    <span
                      className="criteria-assessment__item-title">{convertCamelAndNumberToTitleCase(item.category)}:</span>
                    {item.feedback}
                  </li>
                ))}
              </ul>
            </li>
            <li>
              <span className="criteria-assessment__item-title">Improved {convertCamelAndNumberToTitleCase(key)}:</span>
              {values.improved}
            </li>
          </ul>
        </div>
      );
    }) : <HtmlContent dangerouslySetInnerHTML={{ __html: argumention }}/>}
  </div>;
};

export default Arguments;