import { calculateIeltsAverage } from "./functionCommon";
import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";

const CriteriaAssessment = ({ criteria = [] }) => {
  const { t } = useTranslation();
  if (!criteria?.length) return null;
  
  
  return <div className="evaluation__content__item evaluation-criteria-assessment">
    <div className="content__item__title evaluation-task-achievement__title">{t("CRITERIA_ASSESSMENT")}</div>
    
    {Array.isArray(criteria)
      ? <div>
        {criteria.map((criteriaItem, index) => {
          const { detailedPoints } = criteriaItem.feedback || [];
          return (
            <div key={index}>
              <div>
                <span className="criteria-assessment__item-title">{criteriaItem.category}:</span>
                {criteriaItem.bandScore}
              </div>
              
              <ul>
                {detailedPoints.map((point, pointIndex) => (
                  <li key={pointIndex}>
                    <div className="criteria-assessment__item-title">{point.aspect}</div>
                    <div>
                      <span className="criteria-assessment__item-title">Detailed Explanation:</span>
                      {point.detailedExplanation}
                    </div>
                    <div>
                      <span className="criteria-assessment__item-title">How To Improve:</span>
                      {point.howToImprove}
                    </div>
                  </li>
                ))}
                
                {criteriaItem.feedback?.overallComment &&
                  <li>
                    <span className="criteria-assessment__item-title">Overall Comment:</span>
                    {criteriaItem.feedback.overallComment}
                  </li>
                }
              
              </ul>
            </div>
          );
        })}
        <div>
          <span className="criteria-assessment__item-title">Overall Score: {calculateIeltsAverage(criteria)}</span>
        </div>
      </div>
      : <HtmlContent dangerouslySetInnerHTML={{ __html: criteria }}/>
    }
  
  
  </div>;
};

export default CriteriaAssessment;