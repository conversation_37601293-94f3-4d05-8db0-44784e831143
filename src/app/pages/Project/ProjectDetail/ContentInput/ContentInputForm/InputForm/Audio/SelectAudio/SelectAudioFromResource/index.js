import React, { useEffect, useMemo, useState } from "react";
import { connect } from "react-redux";
import { Popover, Table } from "antd";
import { useTranslation } from "react-i18next";

import { toast } from "@component/ToastProvider";
import { useAudio } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm/InputForm/Audio";

import AntButton from "@component/AntButton";
import SearchInput from "@component/SearchInput";
import ResourceAudioPlayer from "@app/pages/Resource/ResourceContent/ResourceAudio/ResourceAudioPlayer";

import { BUTTON, CONSTANT } from "@constant";
import { formatTimeDate, getFileExtension } from "@common/functionCommons";
import { checkResourceExist, getAllResource } from "@services/Resource";

import "./SelectAudioFromResource.scss";

function SelectAudioFromResource({ user, categorySelected, ...props }) {
  const { t } = useTranslation();
  
  const [isLoading, setLoading] = useState(false);
  const { setAudioId, isShowUploadAudio, setShowUploadAudio } = useAudio();
  
  const [myResourceData, setMyResourceData] = useState([]);
  const [orgResourceData, setOrgResourceData] = useState([]);
  
  
  const [audioSearchValue, setAudioSearchValue] = useState("");
  const [audioSelected, setAudioSelected] = useState(null);
  
  const audioListFilterer = useMemo(() => {
    const audioData = categorySelected === CONSTANT.MY_RESOURCE
      ? myResourceData
      : categorySelected === CONSTANT.ORG_RESOURCE ? orgResourceData : [];
    
    
    if (audioSearchValue) {
      return audioData?.filter(x => x?.name?.toLowerCase()?.includes(audioSearchValue?.toLowerCase()));
    }
    return audioData;
  }, [myResourceData, orgResourceData, audioSearchValue, categorySelected]);
  
  
  useEffect(() => {
    if (isShowUploadAudio) {
      getResourceData();
    } else {
      resetResourcePage();
    }
  }, [isShowUploadAudio]);
  
  useEffect(() => {
    resetResourcePage();
  }, [categorySelected]);
  
  function resetResourcePage() {
    if (!!audioSelected) setAudioSelected(null);
    if (!!audioSearchValue) setAudioSearchValue("");
  }
  
  async function getResourceData() {
    setLoading(true);
    const query = { type: CONSTANT.AUDIO };
    const allRequest = [
      getAllResource({ ...query, userId: user._id }),
    ];
    
    if (user.organizationId?._id) {
      allRequest.push(getAllResource({ ...query, organizationId: user.organizationId._id }));
    }
    
    const [myResourceList, orgResourceList] = await Promise.all(allRequest);
    if (Array.isArray(myResourceList)) setMyResourceData(myResourceList);
    if (Array.isArray(orgResourceList)) setOrgResourceData(orgResourceList);
    setLoading(false);
  }
  
  function onRow(record) {
    return {
      onClick: () => setAudioSelected(record),
    };
  }
  
  const columns = [
    {
      title: t("ORDER"),
      width: 80,
      render: (value, row, index) => index + 1,
    },
    {
      title: t("FILE_NAME"),
      dataIndex: "name",
      render: value => <Popover
        className="audio-file-name"
        placement="topLeft"
        content={value}
        trigger="hover"
      >
        {value}
      </Popover>,
    },
    { title: t("TYPE"), dataIndex: "name", render: getFileExtension, width: 100 },
    { title: t("DATE_MODIFIED"), dataIndex: "updatedAt", render: formatTimeDate, width: 200 },
    {
      title: t("AUDIO_PLAYER"),
      dataIndex: "audioId",
      width: 150,
      render: (value, row, index) => {
        return <ResourceAudioPlayer
          id={row._id + index}
          resourceData={row}
          audioDuration={value?.duration}
        />;
      },
    },
  ];
  
  
  async function handleSelectAudio() {
    if (!audioSelected?.audioId?.audioFileId) {
      return toast.error("FILE_NOT_FOUND");
    }
    const apiResponse = await checkResourceExist(audioSelected?._id);
    if (apiResponse) {
      setAudioId(audioSelected.audioId.audioFileId._id);
      setShowUploadAudio(false);
    } else {
      toast.error("FILE_NOT_FOUND");
    }
  }
  
  function onSearch(e) {
    setAudioSearchValue(e.target.value);
  }
  
  if (categorySelected === CONSTANT.MY_COMPUTER) return null;
  return <div className="select-from-resource">
    <div className="resource-table-container">
      <div className="resource-search">
        <SearchInput
          size={"large"}
          placeholder={t("SEARCH")}
          value={audioSearchValue}
          onChange={onSearch}
          onClear={() => setAudioSearchValue(null)}
        />
      </div>
      <Table
        // scroll={{ x: "max-content"}}
        dataSource={audioListFilterer}
        columns={columns}
        onRow={onRow}
        rowSelection={{
          type: "radio",
          selectedRowKeys: audioSelected ? [audioSelected._id] : [],
        }}
        pagination={false}
      />
    </div>
    <div className="resource-submit">
      <AntButton
        size="large"
        onClick={() => setShowUploadAudio(false)}
      >
        {t("CANCEL")}
      </AntButton>
      <AntButton
        size="large"
        type={BUTTON.DEEP_NAVY}
        disabled={!audioSelected}
        onClick={handleSelectAudio}
      >
        {t("SELECT")}
      </AntButton>
    </div>
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(SelectAudioFromResource);