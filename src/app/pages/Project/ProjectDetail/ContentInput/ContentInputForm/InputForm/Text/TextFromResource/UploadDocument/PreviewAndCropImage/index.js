import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import React<PERSON>rop from "react-image-crop";

import { useProject } from "@app/pages/Project";
import { useText } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm/InputForm/Text";

import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";

import { uploadImageExtractText } from "@services/File";

import TrashYellow from "@component/SvgIcons/Trash/TrashYellow";

import "react-image-crop/dist/ReactCrop.css";
import "./PreviewAndCropImage.scss";

function PreviewAndCropImage({ ...props }) {
  const { t } = useTranslation();
  
  const { projectData } = useProject();
  const { setShowTextFromDocument, onExtractDocument } = useText();
  const { crops, setCrops, previewData } = props;
  const { clearState } = props;
  
  const [loading, setLoading] = useState(false);
  
  
  useEffect(() => {
    setMaxHeight();
  }, [previewData?.type]);
  
  
  function setMaxHeight() {
    const jsCropImage = document.getElementById("js-crop-image");
    
    if (jsCropImage) {
      const maxHeight = jsCropImage.clientHeight;
      const maxWidth = jsCropImage.clientWidth;
      
      const jsReactCrop = document.getElementsByClassName("js-react-crop");
      for (let i = 0; i < jsReactCrop?.length; i++) {
        if (jsReactCrop[i]) {
          jsReactCrop[i].style.maxHeight = `${maxHeight}px`;
          jsReactCrop[i].style.maxWidth = `${maxWidth}px`;
        }
      }
    }
  }
  
  const handleExtractImage = async () => {
    setLoading(true);
    
    const { x, y, height, width } = crops;
    const dataRequest = { file: previewData.file, height, width, left: x, top: y };
    
    const apiResponse = await uploadImageExtractText(dataRequest, projectData.workspaceId);
    
    if (apiResponse?.image) {
      const extractedDocument = {
        imageFileId: apiResponse.image.imageFileId,
        thumbnailFileId: apiResponse.image.thumbnailFileId,
      };
      onExtractDocument(CONSTANT.IMAGE, apiResponse.text, extractedDocument);
      setShowTextFromDocument(false);
    }
    setLoading(false);
  };
  
  
  const onImageLoad = () => {
    if (!crops) setCrops({ unit: "%", x: 0, y: 0, width: 100, height: 100 });
  };
  
  const xhtml = <div className="crop-image-container">
    <AntButton
      size="small"
      className="crop-image__remove-image"
      type={BUTTON.LIGHT_YELLOW}
      icon={<TrashYellow />}
      onClick={clearState}
    >
      {t("REMOVE_IMAGE")}
    </AntButton>
    <div className="crop-image__preview">
      <div id="js-crop-image" className="crop-image__inner">
        <ReactCrop
          className="js-react-crop"
          crop={crops}
          onChange={(_, crop) => setCrops(crop)}
        >
          <img src={previewData.blob} alt="" onLoad={onImageLoad} />
        </ReactCrop>
      </div>
    </div>
    
    <AntButton
      size="large"
      type={BUTTON.DEEP_NAVY}
      loading={loading}
      onClick={handleExtractImage}
    >
      {t("EXTRACT_TEXT")}
    </AntButton>
  </div>;
  
  
  if (previewData?.type === CONSTANT.IMAGE) return xhtml;
  return null;
}

export default PreviewAndCropImage;