import React, { useEffect, useMemo, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Popover, Table } from "antd";

import { toast } from "@component/ToastProvider";
import { useText } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm/InputForm/Text";

import Loading from "@component/Loading";
import SearchInput from "@component/SearchInput";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";
import { formatTimeDate, getFileExtension } from "@common/functionCommons";
import { checkResourceExist, getAllResource } from "@services/Resource";

import "./SelectDocumentFromResource.scss";
import { getFileById } from "@services/File";
import { render } from "less";

function SelectDocumentFromResource({ user, ...props }) {
  const { t } = useTranslation();
  
  const { isShowTextFromDocument, setShowTextFromDocument, onExtractDocument } = useText();
  const { previewData, setPreviewData, categorySelected} = props;

  
  const [isLoading, setLoading] = useState(false);
  const [myResourceData, setMyResourceData] = useState([]);
  const [orgResourceData, setOrgResourceData] = useState([]);
  const [documentSelected, setDocumentSelected] = useState(null);
  
  const [documentSearchValue, setDocumentSearchValue] = useState("");
  const [isLoadingSelect, setIsLoadingSelect] = useState(false);
  
  const imageListFilterer = useMemo(() => {
    const documentData = categorySelected === CONSTANT.MY_RESOURCE
      ? myResourceData
      : categorySelected === CONSTANT.ORG_RESOURCE ? orgResourceData : [];
    
    if (documentSearchValue) {
      return documentData?.filter(x => x?.name?.toLowerCase()?.includes(documentSearchValue?.toLowerCase()));
    }
    return documentData;
  }, [myResourceData, orgResourceData, documentSearchValue, categorySelected]);
  

  useEffect(() => {
    if (isShowTextFromDocument) {
      getResourceImage();
    } else {
      resetImagePage();
    }
  }, [isShowTextFromDocument]);
  
  useEffect(() => {
    resetImagePage();
  }, [categorySelected]);
  
  function resetImagePage() {
    if (!!documentSelected) setDocumentSelected(null);
    if (!!documentSearchValue) setDocumentSearchValue("");
    if (!!documentSelected) setDocumentSelected(null);
  }
  
  async function getResourceImage() {
    setLoading(true);
    const query = { type: CONSTANT.DOCUMENT };
    const allRequest = [
      getAllResource({ ...query, userId: user._id }),
    ];
    
    if (user.organizationId?._id) {
      allRequest.push(getAllResource({ ...query, organizationId: user.organizationId._id }));
    }
    
    const [myResourceList, orgResourceList] = await Promise.all(allRequest);
    if (Array.isArray(myResourceList)) setMyResourceData(myResourceList);
    if (Array.isArray(orgResourceList)) setOrgResourceData(orgResourceList);
    setLoading(false);
  }
  
  
  function onSearch(e) {
    setDocumentSearchValue(e.target.value);
    if (documentSelected) setDocumentSelected(null);
  }
  
  function handleSelectDocument() {
    if (documentSelected?.imageId) {
      handleSelectImage();
    } else {
      handleSelectFile();
    }
  }

  async function handleSelectImage() {
    const apiResponse = await checkResourceExist(documentSelected?._id);
    if (apiResponse) {
      const extractedDocument = {};
      if (documentSelected.imageId) {
        extractedDocument.imageFileId = documentSelected.imageId.imageFileId?._id;
        extractedDocument.thumbnailFileId = documentSelected.imageId.thumbnailFileId;
      } 
      onExtractDocument(CONSTANT.IMAGE, documentSelected.text, extractedDocument);
      setShowTextFromDocument(false);
    } else {
      toast.error("FILE_NOT_FOUND");
    }
  }

  async function handleSelectFile() {
    setIsLoadingSelect(true);
    const fileData = await getFileById(documentSelected?.fileId?._id);
    if (fileData) {
      const newPreviewData = {
        type: CONSTANT.DOCUMENT,
        fileId: documentSelected.fileId._id,
        blob: fileData.url,
        category: categorySelected,
      };
      setPreviewData(newPreviewData);
    } else { toast.error("FILE_NOT_FOUND"); }
    setIsLoadingSelect(false);
  }
  
  const columns = [
    {
      title: t("ORDER"),
      width: 80,
      render: (value, row, index) => index + 1,
    },
    {
      title: t("FILE_NAME"),
      dataIndex: "name",
      render: value => <Popover
        className="line-clamp-1"
        placement="topLeft"
        content={value}
        trigger="hover"
      >
        {value}
      </Popover>
    },
    {
      title: t("TYPE"),
      render: value => getFileExtension(value.fileId?.displayName || value.imageId?.name),
      width: 200,
    },
    {
      title: t("DATE_MODIFIED"),
      dataIndex: ["updatedAt"],
      render: formatTimeDate,
      width: 200,
    },
  ];
  
  function onRow(record) {
    return {
      onClick: () => setDocumentSelected(record),
    };
  }

  const showPreviewData = previewData?.type === CONSTANT.DOCUMENT && previewData?.category === categorySelected
  if (categorySelected === CONSTANT.MY_COMPUTER || showPreviewData) return null;

  return <div className="select-from-resource">
    <Loading active={isLoading} className="resource-table-container">
      <div className="resource-search">
        <SearchInput
          size={"large"}
          placeholder={t("SEARCH")}
          value={documentSearchValue}
          onChange={onSearch}
          onClear={() => setDocumentSearchValue(null)}
        />
      </div>
      
      <Table
        dataSource={imageListFilterer}
        columns={columns}
        onRow={onRow}
        rowSelection={{
          type: "radio",
          selectedRowKeys: documentSelected ? [documentSelected._id] : [],
        }}
        pagination={false}
      />
    
    </Loading>
    <div className="resource-submit">
      <AntButton
        size="large"
        onClick={() => setShowTextFromDocument(false)}
      >
        {t("CANCEL")}
      </AntButton>
      <AntButton
        size="large"
        type={BUTTON.DEEP_NAVY}
        disabled={!documentSelected}
        onClick={handleSelectDocument}
        loading={isLoadingSelect}
      >
        {t("SELECT")}
      </AntButton>
    </div>
  </div>;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(SelectDocumentFromResource);