.crop-image-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  gap: 16px;

  .crop-image__remove-image {
    align-items: center;
    margin-left: auto;
  }

  .crop-image__preview {
    display: flex;

    background: var(--lighttheme-content-background-stroke);
    justify-content: center;
    align-items: center;

    width: 100%;
    position: relative; /* If you want text inside of it */
    padding-top: 56.25%;


    .crop-image__inner {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      display: flex;

      .ReactCrop {
        margin: auto;
      }
    }
  }

}