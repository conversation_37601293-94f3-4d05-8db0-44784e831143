import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { useText } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm/InputForm/Text";

import ResourceCategories from "@component/Resource/ResourceCategories";
import AntModal from "@component/AntModal";
import UploadDocument from "./UploadDocument";
import SelectDocumentFromResource from "./SelectDocumentFromResource";

import { CONSTANT } from "@constant";

import "./TextFromImage.scss";
import PreviewPDF from "./PreviewPDF";

const INIT_DOCUMENT = { type: null, file: null, blob: null, category: null }

const TextFromImage = ({ ...props }) => {
  const { t } = useTranslation();
  
  const { isShowTextFromDocument, setShowTextFromDocument } = useText();
  const [categorySelected, setCategorySelected] = useState(CONSTANT.MY_COMPUTER);
  const [previewData, setPreviewData] = useState(INIT_DOCUMENT);
  
  useEffect(() => {
    if (isShowTextFromDocument) {
      setCategorySelected(CONSTANT.MY_COMPUTER);
    }
  }, [isShowTextFromDocument]);

  const resetPreviewData = () => {
    setPreviewData(INIT_DOCUMENT);
  }
  
  return <AntModal
    width={1056}
    title={t("SELECT_FROM_RESOURCE")}
    open={isShowTextFromDocument}
    onUpload={props.onUpload}
    onCancel={() => setShowTextFromDocument(false)}
    footerless
    destroyOnClose
    maskClosable={false}
    className="select-from-resource-modal"
  >
    <div className="text-from-image-modal">
      <ResourceCategories
        allowUpload
        categorySelected={categorySelected}
        setCategorySelected={setCategorySelected}
      />
      
      <UploadDocument
        categorySelected={categorySelected}
        setPreviewData={setPreviewData}
        resetPreviewData={resetPreviewData}
        previewData={previewData}
      />

      <SelectDocumentFromResource
        categorySelected={categorySelected}
        previewData={previewData}
        setPreviewData={setPreviewData}
        resetPreviewData={resetPreviewData}
      />

      <PreviewPDF
        previewData={previewData}
        categorySelected={categorySelected}
        resetPreviewData={resetPreviewData}
      />
    </div>
  </AntModal>;
};

export default TextFromImage;