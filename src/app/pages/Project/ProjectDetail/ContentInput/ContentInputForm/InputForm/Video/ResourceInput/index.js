import React, { useEffect, useMemo, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import _ from "lodash";

import AntModal from "@component/AntModal";
import ResourceCategories from "@component/Resource/ResourceCategories";
import VideoFromComputer from "./VideoFromComputer";
import VideoFromResource from "./VideoFromResource";


import { CONSTANT } from "@constant";

import "./ResourceInput.scss";

function ResourceInput({ ...props }) {
  const { t } = useTranslation();
  const { stateResource, toggleResourceModal } = props;

  const [categorySelected, setCategorySelected] = useState(CONSTANT.MY_RESOURCE);

  useEffect(() => {
    if (!stateResource.isShowModal) {
      setCategorySelected(CONSTANT.MY_COMPUTER);
    }
  }, [stateResource]);

  return <>
    <AntModal
      width={1280}
      title={t("SELECT_FROM_RESOURCE")}
      open={stateResource.isShowModal}
      onCancel={() => toggleResourceModal()}
      footerless
      destroyOnClose
      className="select-from-resource-modal"
    >
      <div className="select-resource-video">
        <ResourceCategories
          allowUpload
          categorySelected={categorySelected}
          setCategorySelected={setCategorySelected}
        />

        <VideoFromComputer categorySelected={categorySelected} {...props} />

        <VideoFromResource
          categorySelected={categorySelected}
          setCategorySelected={setCategorySelected}
          onCancel={() => toggleResourceModal()}
          {...props}
        />
      </div>
    </AntModal>
  </>;
}


function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(ResourceInput);
