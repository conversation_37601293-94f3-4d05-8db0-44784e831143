import React, { useEffect, useMemo, useState } from "react";
import { Form, InputNumber } from "antd";
import { Document, Page, pdfjs } from "react-pdf";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";
import { useText } from "../../index";

import Loading from "@component/Loading";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT, MAX_PAGE_RANGER } from "@constant";

import { cloneObj } from "@common/functionCommons";
import { extractTextByFileId, uploadFileExtractText } from "@services/File";

import TrashYellow from "@component/SvgIcons/Trash/TrashYellow";
import ChevronLeft from "@component/SvgIcons/ChevronLeft";
import ChevronRight from "@component/SvgIcons/ChevronRight";
import DANGER_ICON from "@src/asset/icon/danger/danger.svg";

import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "./PreviewPDF.scss";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.js",
  import.meta.url,
).toString();

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;


function PreviewPDF({ ...props }) {
  const { t } = useTranslation();
  const { previewData, resetPreviewData, categorySelected } = props;
  
  const { projectData } = useProject();
  const { setShowTextFromDocument, onExtractDocument } = useText();
  
  const [selectPageForm] = Form.useForm();
  const [isFirst, setFirst] = useState(true);
  const [isLoadingExtract, setLoadingExtract] = useState(false);
  
  const [pageNumber, setPageNumber] = useState(1);
  const [pageRange, setPageRange] = useState({ startPage: 0, endPage: 0 });
  const [numPages, setNumPages] = useState(0);
  
  useEffect(() => {
    if (!isFirst && previewData?.type === CONSTANT.DOCUMENT) {
      setPageNumber(1);
      setPageRange({ startPage: 0, endPage: 0 });
      setNumPages(0);
    }
  }, [previewData]);
  
  useEffect(() => {
    setFirst(true);
  }, [categorySelected]);
  
  useEffect(() => {
    const endPage = Math.min(numPages, MAX_PAGE_RANGER);
    const pageRangeData = { startPage: 1, endPage };
    setPageRange(pageRangeData);
    selectPageForm.setFieldsValue(pageRangeData);
  }, [numPages]);
  
  const validatePageRange = useMemo(() => {
    const { startPage, endPage } = pageRange;
    if (!startPage || !endPage || startPage < 1 || endPage > numPages || startPage > endPage) {
      return { isError: true, errorMsg: t("PAGE_NUMBER_INVALID") };
    } else if (endPage - startPage >= MAX_PAGE_RANGER) {
      return { isError: true, errorMsg: t("PAGE_NUMBER_LIMIT") };
    } else {
      return { isError: false, errorMsg: "" };
    }
  }, [pageRange]);
  
  function onLoadSuccess({ numPages }) {
    setNumPages(numPages);
    setFirst(false);
  }
  
  function onLoadError(error) {
    console.log("error", error);
    setFirst(false);
  }
  
  function handlePrevPage() {
    setPageNumber(prevState => prevState - 1);
  }
  
  function handleNextPage() {
    setPageNumber(prevState => prevState + 1);
  }
  
  const onValuesChange = (changedValues, allValues) => {
    setPageRange(allValues);
  };
  
  async function onExtractText(values) {
    const dataRequest = cloneObj(values);
    dataRequest.totalPages = numPages;
    let serviceSubmitForm;
    if (!!previewData?.fileId) {
      serviceSubmitForm = extractTextByFileId;
      dataRequest.fileId = previewData.fileId;
    } else if (!!previewData?.file) {
      serviceSubmitForm = uploadFileExtractText;
      dataRequest.file = previewData.file;
      dataRequest.workspaceId = projectData.workspaceId;
    }
    
    setLoadingExtract(true);
    const apiResponse = await serviceSubmitForm(dataRequest);
    if (apiResponse?.file) {
      const extractedDocument = {
        fileId: apiResponse.file._id,
        fileName: apiResponse.file.displayName,
      };
      const extractedText = Array.isArray(apiResponse.text) ? apiResponse.text.join(" ") : apiResponse.text;
      onExtractDocument(CONSTANT.DOCUMENT, extractedText, extractedDocument);
      setShowTextFromDocument(false);
      resetPreviewData();
    }
    setLoadingExtract(false);
  }
  
  const previewPdfHtml = <Loading active={isFirst} className="preview-pdf">
    <AntButton
      size="small"
      className="preview-pdf__remove-file"
      type={BUTTON.LIGHT_YELLOW}
      onClick={resetPreviewData}
      icon={<TrashYellow />}
    >
      {t("REMOVE_FILE")}
    </AntButton>
    
    <div className="preview-pdf__preview">
      <div className="preview-pdf__preview-inner">
        <div className="preview-file__document">
          {previewData?.blob && <Document
            file={previewData.blob}
            onLoadSuccess={onLoadSuccess}
            onLoadError={onLoadError}
            loading=""
          >
            <Page pageNumber={pageNumber} loading={<div className="document-loading">{t("LOADING_PAGE")}...</div>} />
          </Document>}
        </div>
        
        <div className="preview-file__page-select">
          <AntButton
            size="mini"
            type={BUTTON.GHOST_WHITE}
            icon={<ChevronLeft />}
            onClick={handlePrevPage}
            disabled={pageNumber < 2}
          />
          
          {!!numPages && `${pageNumber} / ${numPages}`}
          
          <AntButton
            size="mini"
            type={BUTTON.GHOST_WHITE}
            icon={<ChevronRight />}
            onClick={handleNextPage}
            disabled={pageNumber >= numPages}
          />
        </div>
      </div>
    </div>
    <div className="preview-pdf__select-page">
      <Form
        id="form-select-page"
        name="form-select-page"
        layout="horizontal"
        form={selectPageForm}
        onValuesChange={onValuesChange}
        onFinish={onExtractText}
      >
        <Form.Item label={t("LIMIT_PAGES")}>
          <span className="limit-pages-number">{MAX_PAGE_RANGER}</span>
        </Form.Item>
        <Form.Item label={t("SELECT_PAGE")} name="startPage">
          <InputNumber
            size="large"
            // min={1}
            controls={false}
            changeOnBlur
          />
        </Form.Item>
        <label className="px-8">-</label>
        <Form.Item name="endPage">
          <InputNumber
            size="large"
            // max={numPages}
            controls={false}
            changeOnBlur
          />
        </Form.Item>
      </Form>
      {validatePageRange.isError && <div className="preview-pdf__error">
        <img src={DANGER_ICON} alt="" />
        {validatePageRange?.errorMsg}
      </div>}
    </div>
    
    <AntButton
      size="large"
      type={BUTTON.DEEP_NAVY}
      form="form-select-page"
      loading={isLoadingExtract}
      htmlType="submit"
      disabled={validatePageRange.isError}
    >
      {t("EXTRACT_TEXT")}
    </AntButton>
  </Loading>;
  
  if (previewData?.type === CONSTANT.DOCUMENT && previewData?.category === categorySelected) return previewPdfHtml;
  return null;
  
}

export default PreviewPDF;