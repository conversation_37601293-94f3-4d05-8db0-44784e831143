.upload-audio-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;

  .upload-audio__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .upload-audio__title {
      font-weight: 600;
    }

    .upload-audio__action {
      margin-top: -2px;
      margin-bottom: -2px;
    }
  }

  .upload-audio__audio-name {
    cursor: default;
    background-color: unset;
    color: var(--black);
  }

  .audio-file__info__file {
    width: 100%;
    height: 48px;
    display: flex;
    justify-content: space-between;
    padding: 13px 16px;
    align-items: center;
    border-radius: 4px;
    border: 1px solid var(--lighttheme-content-background-stroke);

    .audio-file__info__file-name {
      line-height: 20px;
    }

    .audio-file__info__reupload {
      display: flex;
      gap: 7px;
      align-items: center;
      background: #FFF;
      color: var(--primary-colours-blue);
      line-height: 20px;
      font-weight: 400;
      padding: 0 !important;
      cursor: pointer;
      min-width: 103px;
    }
  }
}

.upload-audio__form-data {
  //display: none;
}