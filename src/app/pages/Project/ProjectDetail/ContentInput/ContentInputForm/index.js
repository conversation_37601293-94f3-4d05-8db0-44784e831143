import React, { useContext, useEffect, useRef } from "react";
import { Form, Input } from "antd";

import InputForm from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm/InputForm";
import InstructionForm from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm/InstructionForm";
import { handleDataRequest } from "@app/pages/Project/ProjectDetail/projectCommons";


import "./ContentInputForm.scss";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { CONSTANT } from "@constant";
import { extractKeys } from "@common/dataConverter";
import { cloneObj, stringSplit } from "@src/common/functionCommons";

export const ContentInputFormContext = React.createContext();


function ContentInputForm({
                            content,
                            inputData,
                            isDisabledContent,
                            setFetchingContent = () => null,
                            toolInfo,
                            instructionSelected,
                            setInstructionSelected,
                            onFinish,
                            onChange = () => null,
                            formKey,
                            showAddReq = false,
                          }) {
  
  const { t } = useTranslation();
  
  const formLastValue = useRef({});
  
  useEffect(() => {
    formLastValue.current = cloneObj(inputData);
  }, [inputData]);
  
  async function onFormChange(name, info) {
    const fieldChanged = extractKeys(info.changedFields, "name")?.flat();
    const inputData = {};
    let allValues = {};
    fieldChanged.forEach(field => {
      if (info.forms[name]) {
        allValues = { ...allValues, ...info.forms[name].getFieldValue() };
        inputData[field] = info.forms[name].getFieldValue(field);
      }
    });
    
    let changedValues;
    Object.entries(inputData).forEach(([key, value]) => {
      if (formLastValue.current?.[key] !== value) {
        changedValues ||= {};
        changedValues[key] = value;
      }
    });
    formLastValue.current = Object.assign({}, formLastValue.current, inputData);

    if (changedValues) onChange(changedValues, allValues);
  }
  
  async function onFormFinish(name, info) {
    if (name === "form-select-page") return;
    
    const validateForm = async (form) => {
      const { errorFields } = await form.validateFields().catch((err) => err);
      if (errorFields && errorFields.length) {
        return false;
      }
    };
    const validateAllForms = await Promise.all(Object.entries(info?.forms).map(([_, form]) => validateForm(form)));
    if (validateAllForms.includes(false)) {
      return;
    }
    
    const dataRequest = await handleDataRequest(info.forms, toolInfo);
    if (!dataRequest) return;
    const additionalRequest = dataRequest.additionalRequest;
    delete dataRequest.additionalRequest;
    onFinish({ inputData: dataRequest, inputType: toolInfo.inputType, additionalRequest });
  }
  
  return <ContentInputFormContext.Provider value={{
    formKey: formKey || "",
    //
    onChange,
  }}>
    <div className="project-content-input">
      <Form.Provider
        onFormFinish={onFormFinish}
        onFormChange={onFormChange}
      >
        <InputForm
          content={content}
          inputData={inputData}
          isDisabledContent={isDisabledContent}
          setFetchingContent={setFetchingContent}
          toolInfo={toolInfo}
          onChange={onChange}
        />
        <InstructionForm
          isDisabledContent={isDisabledContent}
          toolInfo={toolInfo}
          content={content}
          inputData={inputData}
          instructionSelected={instructionSelected}
          setInstructionSelected={setInstructionSelected}
        />
        
        {showAddReq && <Form
          name={`${CONSTANT.REGENERATE}-${content._id}${formKey}`}
          layout="vertical"
          disabled={isDisabledContent}
        >
          <Form.Item label={t("REQUESTS")} name="additionalRequest"
                     rules={[() => ({
                       validator(_, value) {
                         if (value && stringSplit(value).length > 1000) {
                           return Promise.reject(t("PLEASE_ENTER_NO_MORE_THAN_WORDS").format(1000));
                         }
                         return Promise.resolve();
                       },
                     })]}
          >
            <Input.TextArea
              size="large"
              count={{
                show: true, max: 1000,
                strategy: (txt) => stringSplit(txt).length,
              }}
              autoSize={{ minRows: 1 }}
              placeholder={t("ENTER_ADDITIONAL_REQUEST")}
            />
          </Form.Item>
        </Form>}
      
      </Form.Provider>
    </div>
  </ContentInputFormContext.Provider>;
}

const ConnectedContentInputForm = connect(null, null)(ContentInputForm);
const useContentInput = () => useContext(ContentInputFormContext);

export { ConnectedContentInputForm as ContentInputForm, useContentInput };
