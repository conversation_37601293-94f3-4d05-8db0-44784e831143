import React, { useState } from "react";
import { Collapse } from "antd";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";

import Loading from "@component/Loading";
import AntButton from "@component/AntButton";
import ExamInputItem from "@app/pages/Project/ProjectDetail/ExamProjectContent/ExamProjectInput/ExamInputItem";

import { BUTTON, CONSTANT } from "@constant";

import { wait } from "@common/functionCommons";

import Minimize from "@component/SvgIcons/Minimize";
import Maximize from "@component/SvgIcons/Maximize";

import "./ExamProjectInput.scss";


function ExamProjectInput({ isSubmitting, formSubmitExam }) {
  const { t } = useTranslation();
  
  const { projectContentData } = useProject();
  
  const [isExpandInput, setExpandInput] = useState(true);
  
  async function submitOne() {
    const numberOfExam = formSubmitExam.getFieldValue("numberOfExam");
    formSubmitExam.setFieldsValue({ numberOfExam: 1 });
    await wait(100);
    formSubmitExam.submit();
    await wait(200);
    formSubmitExam.setFieldsValue({ numberOfExam });
  }
  
  return <>
    <Collapse
      className="exam-project-collapse exam-project-input-container"
      ghost
      expandIcon={null}
      activeKey={isExpandInput ? [CONSTANT.INPUT] : []}
      items={[{
        key: CONSTANT.INPUT,
        className: "",
        label: <>
          {t("INPUT")}
          <AntButton
            size="small"
            type={BUTTON.LIGHT_NAVY}
            onClick={() => setExpandInput(prevState => !prevState)}
            icon={isExpandInput ? <Minimize /> : <Maximize />}
          >
            {t(isExpandInput ? "COLLAPSE" : "EXPAND")}
          </AntButton>
        </>,
        children: projectContentData?.length
          ? <Loading active={isSubmitting} className="exam-project-input__body">
            {projectContentData.map(content => {
              return <ExamInputItem
                key={content._id}
                content={content}
              />;
            })}
            <div className="exam-project-input__body-submit">
              <AntButton
                size="large"
                type={BUTTON.DEEP_NAVY}
                disabled={isSubmitting}
                onClick={submitOne}
              >
                {t("CREATE_AN_EXAM")}
              </AntButton>
            </div>
          </Loading>
          : null,
      }]}
    />
  </>;
}


export default ExamProjectInput;