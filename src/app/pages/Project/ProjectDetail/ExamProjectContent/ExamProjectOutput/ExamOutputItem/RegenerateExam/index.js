import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";

import Loading from "@component/Loading";
import AntModal from "@component/AntModal";
import AntButton from "@component/AntButton";
import { ContentInputForm } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm";

import { BUTTON, CONSTANT } from "@constant";

import { cloneObj } from "@common/functionCommons";
import { retryResponse } from "@services/Response";

import Rotate from "@component/SvgIcons/Rotate";

import "./RegenerateExam.scss";

function RegenerateExam({ output, contentData, insInit, toolInfo, disabled }) {
  const { t } = useTranslation();
  
  const { projectData, setProjectContentData } = useProject();
  
  const [insSlt, setInsSlt] = useState(null);
  const [isShowPopup, setShowPopup] = useState(false);
  const [isLoading, setLoading] = useState(false);
  
  
  useEffect(() => {
    setInsSlt(insInit);
  }, [insInit]);
  
  
  function replaceResponseData(resData) {
    setProjectContentData(prevState => {
      const newState = cloneObj(prevState);
      newState.forEach(state => {
        if (state._id === resData.contentId) {
          state.responses = state.responses?.map(response => {
            return response._id === resData._id ? resData : response;
          });
        }
      });
      return newState;
    });
  }
  
  function handleCancel() {
    setShowPopup(false);
  }
  
  async function onFinish(values) {
    const apiRequest = {
      _id: output._id,
      workspaceId: projectData?.workspaceId,
      inputData: values.inputData,
      additionalRequest: values?.additionalRequest,
    };
    setLoading(true);
    const apiResponse = await retryResponse(apiRequest);
    
    if (apiResponse) {
      replaceResponseData(apiResponse);
      setShowPopup(false);
    }
    setLoading(false);
  }
  
  
  return <>
    <AntButton
      size="large"
      type={BUTTON.DEEP_NAVY}
      icon={<Rotate />}
      onClick={() => setShowPopup(prevState => !prevState)}
      disabled={disabled}
    >
      {t("REGENERATE")}
    </AntButton>
    
    <AntModal
      width={774}
      open={isShowPopup}
      onCancel={handleCancel}
      footerless
      closeIcon={null}
      centered
    >
      <Loading active={isLoading} className="regenerate-container">
        <div className="regenerate-title">{output.contentIndex}. {output.contentTitle}</div>
        
        <ContentInputForm
          showAddReq={true}
          formKey={CONSTANT.REGENERATE}
          content={contentData}
          inputData={output.inputId.inputData}
          
          toolInfo={toolInfo}
          instructionSelected={insSlt}
          setInstructionSelected={setInsSlt}
          onFinish={onFinish}
          isDisabledContent={isLoading}
        />
        
        
        <div className="regenerate__submit">
          <AntButton
            size="large"
            type={BUTTON.WHITE}
            onClick={handleCancel}
          >
            {t("CANCEL")}
          </AntButton>
          
          <AntButton
            size="large"
            type={BUTTON.DEEP_NAVY}
            htmlType="submit"
            form={`${CONSTANT.INSTRUCTION}-${contentData._id}${CONSTANT.REGENERATE}`}
          >
            {t("SUBMIT")}
          </AntButton>
        </div>
      
      </Loading>
    </AntModal>
  </>;
  
}

export default RegenerateExam;