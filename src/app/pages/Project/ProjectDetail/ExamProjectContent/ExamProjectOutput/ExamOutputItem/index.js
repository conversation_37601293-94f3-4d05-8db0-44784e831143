import React, { useEffect, useMemo, useRef, useState } from "react";
import { Form } from "antd";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";

import { CONSTANT } from "@constant";
import { cloneObj, renderPlainTextWithLineBreaks } from "@common/functionCommons";
import { streamSSEResponse } from "@services/Content";

import TitleInput from "@app/pages/Project/ProjectDetail/ContentOutput/TitleInput";
import HtmlOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/HtmlOutput";
import HtmlQuestionOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/HtmlQuestionOutput";
import TextOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TextOutput";
import AbcdOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/AbcdOutput";
import SelectTitleOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/SelectTitleOutput";
import OpenQuestionOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/OpenQuestionOutput";
import QuestionOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/QuestionOutput";
import TrueFalseOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/TrueFalseOutput";
import PlaintextOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/PlaintextOutput";
import AudioOutput from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/AudioOutput";

import ExamOutputAction
  from "@app/pages/Project/ProjectDetail/ExamProjectContent/ExamProjectOutput/ExamOutputItem/ExamOutputAction";
import OutputProcessing
  from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentFooter/OutputProcessing";

import AlertCircleIcon from "@component/SvgIcons/AlertCircleIcon";

import "./ExamOutputItem.scss";

function ExamOutputItem({ listAllTool, output }) {
  const { t } = useTranslation();
  
  const eventSource = useRef(undefined);
  
  const { isAllowEditing, isPreviewProject, isShowPlainText } = useProject();
  const { projectContentData, setProjectContentData, handleChangeTitle } = useProject();
  
  const [outputForm] = Form.useForm();
  const [isEditing, setEditing] = useState(false);
  
  const contentData = useMemo(() => {
    return projectContentData.find(content => content._id === output.contentId);
  }, [projectContentData, output]);
  
  const toolInfo = useMemo(() => {
    return listAllTool.find(tool => tool._id === output?.inputId?.toolId);
  }, [output]);
  
  useEffect(() => {
    if (output?.state?.toUpperCase() === CONSTANT.PROCESSING) {
      handleStreamOutput();
    }
    return () => closeEv();
  }, [output?.state]);
  
  function handleStreamOutput() {
    
    eventSource.current = streamSSEResponse(output._id);
    eventSource.current.addEventListener(output._id, async (event) => {
      const data = JSON.parse(event?.data);
      setProjectContentData(prevState => {
        const newState = cloneObj(prevState);
        newState.forEach(state => {
          if (state._id === output.contentId) {
            state.responses = state.responses.map(response => {
              if (response._id === data._id) {
                response.output = data.output;
                response.outputType = data.outputType;
                response.plaintext = data.plaintext;
                response.state = data.state;
                //response.typingEffect = !data.canceledSubmit;
              }
              return response;
            });
          }
        });
        return newState;
      });
      
    });
    eventSource.current.addEventListener("error", async (error) => {
      closeEv();
    });
  }
  
  function closeEv() {
    eventSource.current?.close();
    eventSource.current = null;
  }
  
  
  const generateOutput = () => {
    
    const outputProps = {
      isEditing, setEditing,
      responseSelected: output,
      outputForm,
      content: { _id: output.contentId },
    };
    
    if (isShowPlainText && isPreviewProject) {
      return <div className="result-response__plaintext">
        {renderPlainTextWithLineBreaks(output.plaintext)}
      </div>;
    }
    
    switch (output?.outputType) {
      case "html":
        return <HtmlOutput {...outputProps} />;
      case "html_questions":
        return <HtmlQuestionOutput {...outputProps} />;
      case "text":
      case "picture_description":
        return <TextOutput {...outputProps} />;
      case "abcd_question":
      case "multi_choice":
        return <AbcdOutput {...outputProps} />;
      case "summary":
      case "three_titles":
      case "three_ideas":
      case "options":
        return <SelectTitleOutput {...outputProps} />;
      case "open_question":
        return <OpenQuestionOutput {...outputProps} />;
      case "discuss_question":
        return <QuestionOutput {...outputProps} />;
      case "tf_question":
        return <TrueFalseOutput {...outputProps} />;
      case "words":
        return <PlaintextOutput {...outputProps} />;
      case "audio":
        return <AudioOutput {...outputProps} />;
      default:
        //return null;
        return <PlaintextOutput {...outputProps} />;
    }
  };
  
  return <div id={`js-project-content-${contentData?._id}`} className="exam-output-content">
    <TitleInput
      content={contentData}
      value={contentData.title}
      onSubmit={title => handleChangeTitle(contentData._id, title)}
      showSubmit={!isPreviewProject && isAllowEditing}
    />
    
    <div>
      {output?.state?.toUpperCase() === CONSTANT.PROCESSING
        ? <OutputProcessing />
        : output?.state?.toUpperCase() === CONSTANT.DONE
          ? generateOutput()
          : output?.state?.toUpperCase() === CONSTANT.ERROR
            ? <div className="exam-output__error-content">
              <AlertCircleIcon />
              <span>{output?.output?.message || t("OOPS_WE_ARE_SORRY")}</span>
            </div>
            : null}
    </div>
    
    <ExamOutputAction
      contentData={contentData}
      output={output}
      outputForm={outputForm}
      isEditing={isEditing} setEditing={setEditing}
      toolInfo={toolInfo}
    />
  </div>;
}

function mapStateToProps(store) {
  const { listAllTool } = store.tool;
  return { listAllTool };
}

export default connect(mapStateToProps)(ExamOutputItem);