.exam-output-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;

  &:not(:last-child):after {
    content: '';
    position: absolute;
    bottom: -17px;
    left: 0;
    right: 0;
    border-top: 1px dashed var(--support-colours-grey-light);
  }

  .title-input .title-input__value {
    font-size: 24px;
    font-weight: 600;
  }

  .exam-output__error-content {
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: 600;
    line-height: 20px;
  }
}