import React from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import TemplateAndTool from "@app/pages/Project/TemplateAndTool";

import PROJECT_BANNER from "@src/asset/image/project-banner.svg";

import * as tool from "@src/ducks/tool.duck";

import "./StartProject.scss";
import { useProject } from "@app/pages/Project";

function StartProject({ suggestToolData, ...props }) {
  const { t } = useTranslation();
  const { projectContentData, isExam, isMark } = useProject();
  
  if (projectContentData?.length || isExam) return null;
  
  return <>
    <div className="start-project">
      <div className="start-project__content">
        <div className="start-project__banner">
          <div className="start-project__banner-img">
            <img src={PROJECT_BANNER} alt="" />
          </div>
          <div className="start-project__banner-text">
            {t("START_NEW_PROJECT_BANNER_1")}
          </div>
          <div className="start-project__banner-description">
            {isMark ? t("START_NEW_GRADDING_PROJECT_BANNER_2") : t("START_NEW_PROJECT_BANNER_2")}
          </div>
        </div>
      </div>
    </div>
  
  
  </>;
}

function mapStateToProps(store) {
  const { suggestToolData } = store.tool;
  return { suggestToolData };
}

const mapDispatchToProps = {
  ...tool.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(StartProject);