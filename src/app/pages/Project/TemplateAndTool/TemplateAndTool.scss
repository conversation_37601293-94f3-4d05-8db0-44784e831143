.template-and-tool-container {
  display: flex;
  align-self: center;
  max-width: 100%;
  box-shadow: var(--shadow-level-2);;

  &.template-and-tool-full {
    .template-and-tool {
      width: 100% !important;
    }
  }

  .template-and-tool {
    width: 100%;
    max-width: 100%;

    @media screen and (min-width: 1920px) {
      width: 1060px;
    }

    @media screen and (min-width: 1536px) and (max-width: 1919.98px) {
      width: 791px;
    }

    @media screen and (min-width: 1024px) and (max-width: 1535.98px) {
      width: 805px;
    }

    @media screen and (min-width: 768px) and (max-width: 1023.98px) {
      width: 550px;
    }

    padding: 24px 32px;
    background-color: var(--lighttheme-background-1);
    display: flex;
    flex-direction: column;
    gap: 24px;
    border-radius: 8px;

    .template-and-tool__title {
      display: flex;
      gap: 16px;
      align-items: center;

      .template-and-tool__title-img {
        height: 24px;
        width: 24px;
      }

      .template-and-tool__title-text {
        font-weight: 600;
        font-size: 20px;
      }

      .template-and-tool__title-action {
        margin-left: auto;
      }

      &.template-and-tool__title-template .template-and-tool__title-text {
        color: var(--typo-colours-support-purple)
      }

      &.template-and-tool__title-tool .template-and-tool__title-text {
        color: var(--typo-colours-support-blue)
      }
    }


    .template-and-tool__template-list {
      .template-list {

        grid-template-columns: repeat(1, minmax(0, 1fr));

        @media screen and (min-width: 1920px) {
          grid-template-columns: repeat(5, minmax(0, 1fr));
        }

        @media screen and (min-width: 1536px) and (max-width: 1919.98px) {
          grid-template-columns: repeat(3, minmax(0, 1fr));
        }

        @media screen and (min-width: 1024px) and (max-width: 1535.98px) {
          grid-template-columns: repeat(3, minmax(0, 1fr));
        }

        @media screen and (min-width: 768px) and (max-width: 1023.98px) {
          grid-template-columns: repeat(2, minmax(0, 1fr));
        }
      }
    }

    .template-and-tool__tool-list {
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
      gap: 24px;

      @media screen and (min-width: 1920px) {
        grid-template-columns: repeat(4, minmax(0, 1fr));
      }

      @media screen and (min-width: 1536px) and (max-width: 1919.98px) {
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }

      @media screen and (min-width: 1024px) and (max-width: 1535.98px) {
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }

      @media screen and (min-width: 768px) and (max-width: 1023.98px) {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }

    }

  }
}

.template-and-tool__modal {
  top: 40px;

  @media screen and (min-width: 720px) {
    top: 24px;

    .modal-content {
      padding: 24px;

      .ant-modal-close {
        inset-inline-end: 24px;
      }
    }
  }
}