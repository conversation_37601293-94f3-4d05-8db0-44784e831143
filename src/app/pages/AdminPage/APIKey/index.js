import React, { useEffect, useState } from "react";
import { Card, Col, Form, Input, Row, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import { EditOutlined, PlusOutlined, SearchOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { connect } from "react-redux";
import { BUTTON, PAGINATION_INIT } from "@constant";
import APIKeyDetail from "./APIKeyDetail";
import { createAPIKey, deleteAPIKey, getAllWithPaginationAPIKeys, updateAPIKey } from "@services/APIKey";
import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";
import "./APIKey.scss";
import { AntForm } from "@component/AntForm";
import { handleReplaceUrlSearch, handleSearchParams, orderColumn, paginationConfig } from "@common/functionCommons";
import i18n from "i18next";
import { handlePagingData } from "@common/dataConverter";
import Loading from "@component/Loading";

import TableAdmin from "@src/app/component/TableAdmin";


const APIKey = ({ ...props }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [form] = Form.useForm();
  const [modelState, setModelState] = useState({ isShowModal: false, apiKey: null });
  const [apiKeyData, setAPIKeyData] = useState(PAGINATION_INIT);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    form.setFieldsValue(query);
    getAPIKeyData(paging, query);
  }, [location.search]);


  const getAPIKeyData = async (paging = apiKeyData.paging, query = apiKeyData.query) => {
    setLoading(true);
    const dataResponse = await getAllWithPaginationAPIKeys(paging, query);
    if (dataResponse) {
      setAPIKeyData(handlePagingData(dataResponse, query));
    }
    setLoading(false);
  };

  const handleEdit = (apiKey) => {
    setModelState({ isShowModal: true, apiKey });
  };

  const openModalcreateAPIKeys = () => {
    setModelState({ isShowModal: true, apiKey: null });
  };

  const handleDelete = optionId => {
    confirm.delete({
      title: t("DELETE_API_KEY"),
      content: t("CONFIRM_DELETE_API_KEY"),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setLoading(true);
        const apiResponse = await deleteAPIKey(optionId, true);
        if (apiResponse) {
          await getAPIKeyData();
          toast.success(t("DELETE_API_KEY_SUCCESS"));
        } else {
          toast.error(t("DELETE_API_KEY_ERROR"));
          setLoading(false);
        }
      },
    });
  };

  const handleCloseModal = (apiKey) => {
    setModelState(pre => ({ isShowModal: !pre.isShowModal, apiKey: apiKey }));
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    const dataRequest = { ...values };
    const isUpdate = Boolean(values._id);

    const dataResponse = isUpdate ? await updateAPIKey(dataRequest, true) : await createAPIKey(dataRequest, true);

    if (dataResponse) {
      toast.success(t(isUpdate ? "UPDATE_API_KEY_SUCCESS" : "CREATE_API_KEY_SUCCESS"));
      await getAPIKeyData();
      handleCloseModal();
    } else {
      toast.error(t(isUpdate ? "UPDATE_API_KEY_ERROR" : "CREATE_API_KEY_ERROR"));
      setLoading(false);
    }
  };

  const columns = [
    orderColumn(apiKeyData.paging),
    {
      title: t("API_KEY"),
      dataIndex: "apiKey",
      ellipsis: true,
      width: "45%",
      render: (text) => <span className="api-key-value">{text}</span>,
    },
    {
      title: t("MODEL_INTERFACE"),
      dataIndex: "modelInterface",
      ellipsis: true,
      width: "15%",
      render: (text) => <span className="model-interface-value">{text}</span>,
    },
    {
      title: t("URL"),
      dataIndex: "url",
      ellipsis: true,
      width: "20%",
      render: (text) => <span className="url-value">{text}</span>,
    },
    {
      title: t("ACTION"),
      key: "action",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className={"api-key-table-actions"}>
          <Tooltip title={t("EDIT_API_KEY")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-edit-api-key"}
              icon={<EditOutlined/>}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title={t("DELETE_API_KEY")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-api-key"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(record._id)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];
  const submitFormFilter = (values) => {
    handleReplaceUrlSearch(1, apiKeyData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    form.resetFields();
    submitFormFilter({});
  };

  const pagination = paginationConfig(apiKeyData.paging, apiKeyData.query, i18n.language);

  return (
    <Loading active={loading} transparent>
      <div className="api-key-container">
        <Card className="api-key-info-card">
          <div className="api-key-info-header">
            <div>
              <h1 className="api-key-title">{t("API_KEY_MANAGEMENT")}</h1>
              <p className="api-key-description">{t("API_KEY_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size={"large"}
              icon={<PlusOutlined/>}
              onClick={openModalcreateAPIKeys}
              className={"btn-create-api-key"}
            >
              {t("CREATE_API_KEY")}
            </AntButton>
          </div>
        </Card>

        <Card className="api-key-search-card">
          <AntForm form={form} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={12} lg={16}>
                <AntForm.Item name="modelInterface" className="search-form-item" style={{ marginBottom: 0 }}>
                  <Input
                    placeholder={t("SEARCH_MODEL_INTERFACE_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="api-key-table-card">
          <TableAdmin
            columns={columns}
            dataSource={apiKeyData.rows}
            pagination={{ ...pagination}}
            className="api-key-table"
            scroll={{ x: "max-content" }}
            rowClassName={() => "api-key-table-row"}
            locale={{ emptyText: t("NO_API_KEY_FOUND") }}
          />
        </Card>

        <APIKeyDetail
          isShowModal={modelState.isShowModal}
          apiKeyData={modelState.apiKey}
          handleCancel={handleCloseModal}
          handleOk={handleSubmit}
        />
      </div>
    </Loading>
  );
};

const mapDispatchToProps = {};


export default connect(() => {
}, mapDispatchToProps)(APIKey);
