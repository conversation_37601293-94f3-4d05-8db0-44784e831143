import React, { useEffect, useState } from "react";
import { Col, Form, Input, InputNumber, Row, Select } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import AntButton from "@component/AntButton";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import CustomModal from "@component/CustomModal";

import { cloneObj, coverLangArrayToObject, coverLanguageObjectToArray } from "@common/functionCommons";

import { BUTTON, INPUT_TYPE, LANG_OPTIONS, RULES } from "@constant";

import "./ModalAPIKeyDetail.scss";


const APIKeyDetail = ({ ...props }) => {
  const { t } = useTranslation();
  const { isShowModal, apiKeyData } = props;
  const [form] = Form.useForm();
  
  useEffect(() => {
    if (apiKeyData) {
      form.setFieldsValue(apiKeyData);
    }
  }, [apiKeyData]);

  const onFinish = async (values) => {
    const data = apiKeyData ? {
      ...values,
      ...(values.apiKey === apiKeyData.apiKey && { apiKey: undefined }),
      _id: apiKeyData._id,
    } : values;
    
    await props.handleOk(data);
    form.resetFields();
  };
  
  const handleCancel = () => {
    form.resetFields();
    props.handleCancel();
  };
  
  
  return (
    <CustomModal
      isShowModal={isShowModal}
      closeIcon
      handleCancel={handleCancel}
      className="option-modal"
      form="option-form"
      footerAlign="center"
      width={800}
      okText={apiKeyData ? t("UPDATE") : t("CREATE")}
      title={apiKeyData ? "Option detail" : "Create Option"}
    
    >
      <div className="option-modal__content">
        <div className="option-modal__body">
          <Form id="option-form" onFinish={onFinish} layout="vertical" size={"large"} form={form}>
            <Row gutter={24}>
              <Col xs={24} lg={24}>
                <Form.Item label="API Key" name="apiKey"
                           rules={[{ required: true, message: "API Key can't be blank!" }]}>
                  <Input placeholder={"Enter API Key"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={24}>
                <Form.Item label="Model interface" name="modelInterface"
                           rules={[{ required: true, message: "Model interface can't be blank!" }]}>
                  <Input placeholder={"Enter model interface"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={24}>
                <Form.Item label="Url" name="url">
                  <Input placeholder={"Enter url"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item label="Requests per minute" name="requestsPerMinute">
                  <InputNumber
                    min={1}
                    step={1}
                    placeholder={"Requests per minute"}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item label="Requests per day" name="requestsPerDay" >
                  <InputNumber
                    min={1}
                    step={1}
                    placeholder={"Requests per day"}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </CustomModal>
  );
};

export default APIKeyDetail;
