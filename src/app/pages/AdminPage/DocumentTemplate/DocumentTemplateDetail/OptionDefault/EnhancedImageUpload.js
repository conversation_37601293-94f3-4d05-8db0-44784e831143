import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDropzone } from "react-dropzone";

import AntButton from "@component/AntButton";
import ImagePreviewModal from "@component/ImagePreviewModal";

import { API } from "@api";
import { BUTTON } from "@constant";

import PlusIcon from "@component/SvgIcons/PlusIcon";
import Close from "@component/SvgIcons/Close";
import Reload from "@component/SvgIcons/Reload";
import Eye from "@component/SvgIcons/Eye";

import "./EnhancedImageUpload.scss";

function EnhancedImageUpload({ loading, imageId, ...props }) {
  const { t } = useTranslation();
  const [previewVisible, setPreviewVisible] = useState(false);

  const dropzoneConfig = useMemo(() => ({
    onDrop: handleUpload,
    noClick: true,
    accept: {
      "image/jpg": [".jpg"],
      "image/jpeg": [".jpeg"],
      "image/png": [".png"],
    },
    multiple: false,
  }), []);

  const { getRootProps, getInputProps, open } = useDropzone(dropzoneConfig);

  function handleUpload(files) {
    if (!files?.[0]) return;
    props.onDrop(files[0]);
  }

  function handlePreview() {
    setPreviewVisible(true);
  }

  function handleClosePreview() {
    setPreviewVisible(false);
  }

  return <>
    <div {...getRootProps()} className="enhanced-image-upload">
      <input {...getInputProps()} />

      <div className="enhanced-image-upload__inner">
        {imageId
          ? <img
              src={API.STREAM_ID.format(imageId)}
              alt=""
              loading="lazy"
              width="100%"
              height="auto"
            />
          : <div className="enhanced-image-upload__upload" onClick={open}>
            <PlusIcon />
            <span>{t("UPLOAD_IMAGE")}</span>
          </div>}

        {imageId && <div className="enhanced-image-upload__backdrop">
          <div className="enhanced-image-upload__actions">
            <AntButton
              size="small"
              type={BUTTON.LIGHT_NAVY}
              icon={<Eye />}
              onClick={handlePreview}
            >
              {t("PREVIEW")}
            </AntButton>

            <AntButton
              size="small"
              type={BUTTON.LIGHT_NAVY}
              icon={<Reload />}
              onClick={open}
            >
              {t("CHANGE")}
            </AntButton>

            {!!props.onClear && <AntButton
              size="small"
              type={BUTTON.LIGHT_RED}
              icon={<Close />}
              onClick={props.onClear}
            >
              {t("REMOVE")}
            </AntButton>}
          </div>
        </div>}
      </div>
    </div>

    {imageId && <ImagePreviewModal
      isOpen={previewVisible}
      handleCancel={handleClosePreview}
      imageFileId={imageId}
    />}
  </>;
}

export default EnhancedImageUpload;
