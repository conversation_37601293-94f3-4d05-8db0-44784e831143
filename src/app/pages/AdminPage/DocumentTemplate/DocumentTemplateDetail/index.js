import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Form, Input, Switch } from "antd";

import { toast } from "@component/ToastProvider";

import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import ReactDropzone from "@component/ReactDropzone";
import PreviewDocumentTemplate from "@app/pages/AdminPage/DocumentTemplate/PreviewDocumentTemplate";
import DocumentOptions from "@app/pages/AdminPage/DocumentTemplate/DocumentTemplateDetail/DocumentOptions";
import OptionDefault from "@app/pages/AdminPage/DocumentTemplate/DocumentTemplateDetail/OptionDefault";

import RULE from "@rule";
import { BUTTON, DOCUMENT_TEMPLATE_TYPE } from "@constant";

import { editDocumentTemplate, getDocumentTemplateDetail, uploadDocumentTemplate } from "@services/DocumentTemplate";

import "./DocumentTemplateDetail.scss";


function DocumentTemplateDetail() {
  const { t } = useTranslation();
  const documentTemplateId = useParams().id;

  const [formDocumentTemplate] = Form.useForm();
  const [formDocStatus] = Form.useForm();

  const [isLoadingInfo, setLoadingInfo] = useState(false);
  const [isLoadingTemplateFile, setLoadingTemplateFile] = useState(false);
  const [isLoadingPublic, setLoadingPublic] = useState(false);

  const [templateFile, setTemplateFile] = useState(null);

  const [documentOptionsData, setDocumentOptionsData] = useState([]);

  const [statePreviewPdf, setStatePreviewPdf] = useState({
    isShowModal: false,
    idPreview: null,
  });

  useEffect(() => {
    if (documentTemplateId) {
      loadAllData();
    }
  }, [documentTemplateId]);

  async function loadAllData() {
    setLoadingInfo(true);
    try {
      const apiResponse = await getDocumentTemplateDetail(documentTemplateId);
      if (apiResponse) {
        setTemplateFile(apiResponse.templateId);
        formDocumentTemplate.setFieldsValue(apiResponse);
        formDocStatus.setFieldsValue(apiResponse);
      }
    } catch (error) {
      console.error("Error loading document template:", error);
    } finally {
      setLoadingInfo(false);
    }
  }



  async function onFinish(values) {
    setLoadingInfo(true);
    const apiRequest = { ...values, _id: documentTemplateId };
    const apiResponse = await editDocumentTemplate(apiRequest);
    if (apiResponse) {
      toast.success("UPDATE_SUCCESS", { replace: true });
    }
    setLoadingInfo(false);
  }

  function handleShowPreviewPdf(idPreview = null) {
    setStatePreviewPdf({ isShowModal: !!idPreview, idPreview });
  }

  async function onDropTemplateFile(files) {
    if (files[0]) {
      setLoadingTemplateFile(true);
      const apiRequest = {
        docxTemplateId: documentTemplateId,
        type: DOCUMENT_TEMPLATE_TYPE.TEMPLATE,
      };
      const apiResponse = await uploadDocumentTemplate(apiRequest, files[0]);
      if (apiResponse) {
        setTemplateFile(apiResponse.templateId);
        toast.success("UPLOAD_SUCCESS", { replace: true });
      }
      setLoadingTemplateFile(false);
    }
  }


  async function onValuesChange(changedValues) {
    if (changedValues.hasOwnProperty("isPublic")) {
      setLoadingPublic(true);
      const apiRequest = { isPublic: changedValues.isPublic, _id: documentTemplateId };
      const apiResponse = await editDocumentTemplate(apiRequest);
      if (apiResponse) {
        toast.success(
          changedValues.isPublic ? "SET_PUBLIC_TEMPLATE_SUCCESS" : "SET_PRIVATE_TEMPLATE_SUCCESS",
          { replace: true },
        );
      } else {
        formDocStatus.setFieldsValue({ isPublic: !changedValues.isPublic });
      }
      setLoadingPublic(false);
    }
  }

  return <>
    <div className="document-template-detail-container">
      <div className="document-template-detail__header">
        {t("DOCUMENT_TEMPLATE_DETAIL")}
      </div>

      {/* Basic Information Section */}
      <div className="document-template-detail__section">
        <div className="document-template-detail__header">
          {t("BASIC_INFORMATION")}
        </div>
        <Loading active={isLoadingInfo}>
          <AntForm
            form={formDocumentTemplate}
            layout="vertical"
            requiredMark={true}
            onFinish={onFinish}
            className="form-document-template-detail"
          >
            <AntForm.Item label={t("NAME")} name="name" rules={[RULE.REQUIRED]}>
              <Input placeholder={t("ENTER_NAME")} />
            </AntForm.Item>
            <AntForm.Item label={t("DESCRIPTION")} name="description">
              <Input placeholder={t("ENTER_DESCRIPTION")} />
            </AntForm.Item>

            <div className="form-document-template-detail__submit">
              <AntButton size="large" htmlType="submit" type={BUTTON.DEEP_NAVY}>
                {t("SAVE")}
              </AntButton>
            </div>
          </AntForm>
        </Loading>
      </div>

      {/* Template File Section */}
      <div className="document-template-detail__section">
        <div className="document-template-detail__header">
          {t("TEMPLATE_FILE")}
        </div>
        <Loading active={isLoadingTemplateFile}>
          <AntForm layout="vertical">
            <div className="document-template__file-name">
              {templateFile?.displayName ? (
                <div className="document-template__file-display">
                  <span className="document-template__file-icon">📄</span>
                  <span className="document-template__file-name-text">{templateFile.displayName}</span>
                </div>
              ) : (
                <div className="document-template__file-empty">{t("NO_FILE_SELECTED")}</div>
              )}

              <div className="document-template__file-action">
                {templateFile?._id && <AntButton
                  size="large"
                  type={BUTTON.GHOST_NAVY}
                  onClick={() => handleShowPreviewPdf(templateFile._id)}
                >
                  {t("PREVIEW")}
                </AntButton>}

                <ReactDropzone
                  onDrop={onDropTemplateFile}
                  accept={{
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx", ".doc"],
                    //"application/pdf": [".pdf", ".PDF"],
                  }}
                >
                  <AntButton size="large" type={BUTTON.DEEP_NAVY}>
                    {t(templateFile ? "CHANGE_FILE" : "SELECT_FILE")}
                  </AntButton>
                </ReactDropzone>
              </div>
            </div>
          </AntForm>
        </Loading>
      </div>

      {/* Document Options Section */}
      <div className="document-template-detail__section">
        <DocumentOptions
          documentOptionsData={documentOptionsData}
          setDocumentOptionsData={setDocumentOptionsData}
        />
      </div>

      {/* Option Default Section */}
      <div className="document-template-detail__section">
        <OptionDefault
          documentOptionsData={documentOptionsData}
          setDocumentOptionsData={setDocumentOptionsData}
        />
      </div>

      {/* Public/Private Setting */}
      <div className="document-template-detail__section document-template-detail__public-switch">
        <div className="document-template-detail__header">
          {t("VISIBILITY_SETTINGS")}
        </div>
        <AntForm form={formDocStatus} onValuesChange={onValuesChange}>
          <AntForm.Item
            label={t("PUBLIC")}
            className="m-0"
            valuePropName="checked"
            name="isPublic"
          >
            <Switch size="large" disabled={isLoadingPublic} />
          </AntForm.Item>
        </AntForm>
      </div>

    </div>

    <PreviewDocumentTemplate
      statePreviewPdf={statePreviewPdf}
      isOpen={statePreviewPdf.isShowModal}
      handleCancel={() => handleShowPreviewPdf(null)}
    />
  </>;
}

export default DocumentTemplateDetail;