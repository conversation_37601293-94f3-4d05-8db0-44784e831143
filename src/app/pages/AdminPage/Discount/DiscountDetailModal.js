import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { Col, DatePicker, Form, Input, InputNumber, Row, Select, Switch } from "antd";
import { useTranslation } from "react-i18next";

import AntModal from "@src/app/component/AntModal";

// import "./OutputTypeModal.scss";
import { DISCOUNT_TYPE } from "@constant";
import { AntForm } from "@src/app/component/AntForm";
import RULE from "@rule";


const DiscountDetailModal = ({ ...props }) => {
  const { open, discount, onCancel, onFinish } = props;
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);
  const [type, setType] = useState(null);

  useEffect(() => {
    if (open && discount) {
      const { startDate, endDate } = discount;
      const dayjsStartDate = startDate ? dayjs(startDate) : null;
      const dayjsEndDate = endDate ? dayjs(endDate) : null;
      setMaxDate(dayjsEndDate);
      setMinDate(dayjsStartDate);
      form.setFieldsValue({ ...discount, startDate: dayjsStartDate, endDate: dayjsEndDate });
    } else {
      form.resetFields();
    }
  }, [open, discount]);

  const handleChangeType = (value) => {
    setType(value);
    form.setFieldsValue({ discount: null });
  }

  return (
    <AntModal
      open={open}
      onCancel={() => onCancel()}
      title={discount ? t("UPDATE_DISCOUNT") : t("CREATE_DISCOUNT")}
      width={1000}
      className="knowledge-modal"
      okText={discount ? t("UPDATE") : t("CREATE")}
      formId="form-discount"
    >
      <AntForm onFinish={onFinish} layout="vertical" form={form} id="form-discount" size={"large"}>
        <Row gutter={24}>
          <Col xs={12} >
            <AntForm.Item label={t("DISCOUNT_CODE")} name="code" rules={[RULE.REQUIRED]}>
              <Input placeholder={t("DISCOUNT_CODE")} />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("TYPE")} name="type" rules={[RULE.REQUIRED]}>
              <Select
                placeholder={t("SELECT_TYPE")}
                options={Object.entries(DISCOUNT_TYPE).map(([key, value]) => ({ value: value, label: t(key) }))}
                onChange={handleChangeType}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("DISCOUNT_LEVEL")} name="discount">
              <InputNumber min={0} placeholder={t("DISCOUNT")} controls={false}
                {...type === DISCOUNT_TYPE.PERCENTAGE ? { max: 100 } : {}}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("LIMIT")} name="limit">
              <InputNumber min={0} step={1} placeholder={t("LIMIT")} />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item
              name={"startDate"}
              rules={[
                () => ({
                  validator(_, value) {
                    if (!!value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                  },
                }),
              ]}
            >
              <DatePicker
                placeholder={t("SELECT_START_DATE")}
                size="large"
                style={{ width: '100%', padding: '9px 11px' }}
                format="DD/MM/YYYY"
                maxDate={maxDate}
                onChange={setMinDate}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item
              name={"endDate"}
              rules={[
                () => ({
                  validator(_, value) {
                    if (!!value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                  },
                }),
              ]}>
              <DatePicker
                placeholder={t("SELECT_END_DATE")}
                size="large"
                style={{ width: '100%', padding: '9px 11px' }}
                format="DD/MM/YYYY"
                minDate={minDate}
                onChange={setMaxDate}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("IS_ACTIVATE")} name="isActivate" valuePropName="checked">
              <Switch />
            </AntForm.Item>
          </Col>
        </Row>
      </AntForm>
    </AntModal>
  );
};

export default DiscountDetailModal;
