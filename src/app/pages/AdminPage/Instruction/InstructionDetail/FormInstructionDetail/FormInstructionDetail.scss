.form-instruction-detail-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  .form-instruction-detail-header {
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
  }

  .ant-form-item {
    margin-bottom: 24px;
  }

  .instruction-localization-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .instruction-localization-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;


  }

  .items-baseline {
    width: 100%;
  }

  .instruction-item-action {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 5px;
  }

  .first-actions {
    align-items: center;
    margin-top: 33px;
  }

  .btn-cancel-add-tool-detail {
    box-shadow: var(--shadow-level-2);
  }

  .localization {
    width: 100%;
  }

  .add-instruction-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    button {
      box-shadow: var(--shadow-level-2);
      color: var(--primary-colours-blue) !important;
    }
  }

  .ant-slider.ant-slider-horizontal {
    padding: 0;
    height: 12px;
    margin: 0;
    background-color: var(--ant-slider-track-bg) !important;
    border-radius: var(--ant-border-radius-xs) !important;

    .ant-slider-track {
      display: flex;
      justify-content: center;
      align-items: center;

      &::after {
        content: attr(time-ranger);
        color: #FFF;
        font-size: 12px;
        font-weight: 600;
      }
    }

    .ant-slider-handle {
      inset-block-start: unset;
      width: 16px;

      &:after {
        background-color: transparent;
        border-radius: unset;
        background-repeat: no-repeat;
        width: 16px;
      }

      &:before {
        width: 16px;
      }

      &.ant-slider-handle-1 {
        transform: translateX(-100%) !important;

        &:after {
          background-image: url('/src/asset/icon/slider/slider-left-light.svg');
        }
      }

      &.ant-slider-handle-2 {
        transform: unset !important;

        &:after {
          background-image: url('/src/asset/icon/slider/slider-right-light.svg');
        }
      }
    }
  }

}