import { Button, Form, Col, Row, Select } from 'antd';

import CustomModal from '@component/CustomModal';

import './ModalCreateFineTuning.scss';
import { useTranslation } from "react-i18next";

const ModalCreateFineTuning = ({ ...props }) => {
  const {t} = useTranslation();
  const { isShowModal, dataSets, modelOptions } = props;
  const [formFineTuning] = Form.useForm();
  const onFinish = async (values) => {
    await props.handleOk(values);
    formFineTuning.resetFields();
  }

  const handleCancel = () => {
    formFineTuning.resetFields();
    props.handleCancel();
  }

  return (
    <CustomModal
      isShowModal={isShowModal}
      closeIcon
      handleCancel={handleCancel}
      className='fine-tuning-modal'
      title='Create Fine-tuning'
      form='finetuning-form'
      footerAlign='center'
      okText={t('CREATE')}
      width={800}
    >
      <div className='fine-tuning-modal__content'>
        <Form
          id='finetuning-form'
          onFinish={onFinish}
          size={"large"}
          layout="vertical"
          form={formFineTuning}
        >
          <Row gutter={20}>
            <Col xs={24}>
              <Form.Item
                label="User Feedback"
                name="datasetId"
                rules={[{ required: true, message: "User Feedback can't be blank!" }]}
              >
                <Select options={dataSets} fieldNames={{ label: 'name', value: '_id' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label="Gpt Model"
                name="gptModel"
                rules={[{ required: true, message: "Gpt Model can't be blank!" }]}
              >
                <Select options={modelOptions} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </CustomModal>
  )
}

export default ModalCreateFineTuning;