import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import React from "react";
import { PlusOutlined } from "@ant-design/icons";

import { BUTTON } from "@constant";

import { deleteFineTuning, createFineTuning } from "@services/FineTuning";

import Actions from "@component/Actions";
import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import TableAdmin from "@src/app/component/TableAdmin";

import ModalCreateFineTuning from "./ModalCreateFineTuning";

import "./FineTunings.scss";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";
import { formatTimeDate } from "@common/functionCommons";

const FineTunings = ({ ...props }) => {
  const { t } = useTranslation();
  const { id: instructionId } = useParams();
  const { isShowModalFineTuning, setShowModalFineTuning } = props;
  const { fineTuningList, handleReloadData, dataSets, modelOptions } = props;
  
  const handleDelete = fineTuningId => {
    confirm.delete({
      content: t("CONFIRM_DELETE_FINE_TUNING"),
      handleConfirm: async (e) => {
        const apiResponse = await deleteFineTuning(fineTuningId, true);
        if (apiResponse) {
          await handleReloadData();
          toast.success("DELETE_FINE_TUNING_SUCCESS");
        }
      },
    });
  };
  
  const handleCloseModal = () => {
    setShowModalFineTuning(false);
  };
  
  const handleOpenModal = () => {
    setShowModalFineTuning(true);
  };
  
  const handleSubmitOption = async (values) => {
    const dataRequest = { ...values, instructionId: instructionId };
    const dataResponse = await createFineTuning(dataRequest, true);
    if (dataResponse) {
      toast.success("CREATE_FINE_TUNING_SUCCESS");
      await handleReloadData();
    }
    handleCloseModal();
  };
  
  const columns = [
    {
      title: "Order",
      align: "center",
      width: 80,
      render: (value, row, index) => index + 1,
    },
    {
      title: "Tuned model",
      dataIndex: "fineTunedModel",
      key: "fineTunedModel",
      width: 250,
    },
    {
      title: "Dataset",
      dataIndex: ["datasetId", "name"],
      key: "datasetId",
    },
    {
      title: "Model training",
      dataIndex: "gptModel",
      key: "gptModel",
      width: 250,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 150,
    },
    {
      title: "Error",
      dataIndex: "job",
      render: (value) => value.error?.code,
      width: 150,
    },
    {
      title: "Created at",
      dataIndex: "createdAt",
      render: (value) => formatTimeDate(value),
      width: 150,
    },
    {
      title: "Action",
      key: "action",
      width: 80,
      align: "center",
      render: (_, record) => (
        <div>
          <AntButton type={BUTTON.GHOST_WHITE} size="small" className={"btn-edit-options"} icon={<DeleteIcon/>}
                     onClick={() => handleDelete(record._id)}>{t("DELETE")}</AntButton>
        </div>
      ),
    },
  ];
  
  return (
    <div className="fine-tunings">
      <div className="fine-tunings-header">
        <AntButton type={BUTTON.DEEP_NAVY} size={"large"} icon={<PlusOutlined/>} className={"btn-create-fine-tunings"}
                   onClick={handleOpenModal}>{t("CREATE_FINE_TUNING")}</AntButton>
      </div>
      <TableAdmin columns={columns} dataSource={fineTuningList} pagination={false} scroll={{ x: 1000 }} />
      <ModalCreateFineTuning
        isShowModal={isShowModalFineTuning}
        handleCancel={handleCloseModal}
        handleOk={handleSubmitOption}
        dataSets={dataSets}
        modelOptions={modelOptions}
      />
    </div>
  );
};

export default FineTunings;