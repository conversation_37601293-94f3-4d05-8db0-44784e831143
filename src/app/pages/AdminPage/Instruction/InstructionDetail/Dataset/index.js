import React, { useState } from "react";
import PropTypes from "prop-types";
import { Modal } from "antd";

import { confirm } from "@component/ConfirmProvider";
import { t } from "i18next";
import { deleteDataset } from "@src/app/services/Dataset";
import { toast } from "@component/ToastProvider";
import CreateDataset from "@src/app/pages/AdminPage/Instruction/InstructionDetail/Dataset/CreateDataset/CreateDataset";

import "./Dataset.scss";
import AntButton from "@component/AntButton";
import { BUTTON } from "@constant";
import { EditOutlined, PlusOutlined } from "@ant-design/icons";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";
import { useTranslation } from "react-i18next";
import TableAdmin from "@src/app/component/TableAdmin";

DataSet.propTypes = {
  InstructionID: PropTypes.string,
};

function DataSet({ samplesData, modalDataset, setModalDataset, handleReloadData, setDatasetList, outputTypeCode }) {
  const {t} = useTranslation();
  const handleEdit = (record) => {
    setModalDataset({
      open: true,
      recordId: record?._id,
    });
  };
  const handleCancel = () => {
    setModalDataset({
      open: false,
      recordId: null,
    });
  };
  const handleOpenModalDataset = () => {
    setModalDataset({ open: true, recordId: null });
  };
  const handleAfterDelete = (idDelete) => {
    const newData = samplesData.filter((item) => item._id !== idDelete);
    setDatasetList(newData);
  }
  const handleDelete = async(record) => {
    confirm.delete({
      content: t("CONFIRM_DELETE_SAMPLE"),
      handleConfirm: async (e) => {
        const response = await deleteDataset(record, true);
        if (response) {
          toast.success("DELETE_SAMPLE_SUCCESS");
          handleAfterDelete(response?._id);
        }
      },
    });
  };
  const columns = [
    {
      title: "Order",
      width: 80,
      align: "center",
      render: (value, row, index) => index + 1,
    },
    {
      title: "Name",
      key: "name",
      dataIndex: "name",
    },
    {
      title: "Action",
      key: "action",
      align: "center",
      width: 200,
      render: (_, record) => (
        <div className={"dataset-detail__option-table-actions"}>
          <AntButton type={BUTTON.GHOST_WHITE} size="small" className={"btn-edit-options"} icon={<EditOutlined/>}
                     onClick={() => handleEdit(record)}>{t("EDIT")}</AntButton>
          <AntButton type={BUTTON.GHOST_WHITE} size="small" className={"btn-edit-options"} icon={<DeleteIcon/>}
                     onClick={() => handleDelete(record._id)}>{t("DELETE")}</AntButton>
        </div>
      ),
    },
  ];
  return (
    <div className={"dataset-container"}>
      <div className={"dataset-header"}>
        <AntButton type={BUTTON.DEEP_NAVY} size={"large"} icon={<PlusOutlined/>} className={"btn-create-dataset"}
                   onClick={handleOpenModalDataset}>{t("CREATE_DATASET")}</AntButton>
      </div>
      <TableAdmin
        columns={columns}
        dataSource={samplesData}
        pagination={false}
        scroll={{ x: 1000 }}
      />
      <Modal open={modalDataset?.open} onCancel={handleCancel} footer={null} width={"100%"} style={{ top: 10 }}>
        <CreateDataset 
        recordId={modalDataset?.recordId} outputTypeCode={outputTypeCode}
        handleCloseModal={handleCancel} handleCreateDataset={handleEdit} handleReloadData={handleReloadData}/>
      </Modal>
    </div>
  );
}

export default DataSet;