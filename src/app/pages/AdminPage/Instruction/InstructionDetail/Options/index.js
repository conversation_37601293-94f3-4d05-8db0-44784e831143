import React from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { EditOutlined, PlusOutlined } from "@ant-design/icons";
import { connect } from "react-redux";

import { BUTTON, INPUT_TYPE, RULES } from "@constant";

import ModalOptionDetail from "./ModalOptionDetail";

import { deleteOption, createOption, updateOption } from "@services/Option";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import * as tool from "@src/ducks/tool.duck";

import "./Options.scss";
import TableAdmin from "@src/app/component/TableAdmin";


const Options = ({ ...props }) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const { modalOptionState, setModalOptionState } = props;
  const { optionsData, getOptionsData , knowledgeData} = props;
  
  const handleEdit = (option) => {
    setModalOptionState({
      isShowModal: true,
      option: option,
    });
  };
  
  const openModalCreateOptions = () => {
    setModalOptionState({ isShowModal: true, option: null });
  };
  
  const handleDelete = optionId => {
    confirm.delete({
      content: t("CONFIRM_DELETE_OPTION"),
      handleConfirm: async (e) => {
        const apiResponse = await deleteOption(optionId, true);
        if (apiResponse) {
          await getOptionsData();
          toast.success("DELETE_OPTION_SUCCESS");
        }
      },
    });
  };
  
  const handleCloseModal = () => {
    setModalOptionState({
      isShowModal: false,
      option: null,
    });
  };
  
  const handleSubmitOption = async (values) => {
    const dataRequest = { ...values, instructionId: id };
    let dataResponse;
    if (values._id) {
      dataResponse = await updateOption(dataRequest, true);
    } else {
      dataResponse = await createOption(dataRequest, true);
    }
    if (dataResponse) {
      if (values._id) {
        toast.success("UPDATE_OPTION_SUCCESS");
      } else {
        toast.success("CREATE_OPTION_SUCCESS");
      }
      await getOptionsData();
      props.getToolAvailable();
      props.getTool();
    }
    handleCloseModal();
  };
  
  const columns = [
    {
      title: "Order",
      align: "center",
      width: 80,
      render: (value, row, index) => index + 1,
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      width: 150,
    },
    {
      title: "Placeholder",
      dataIndex: "placeholder",
      width: 150,
    },
    {
      title: "Default value",
      dataIndex: "defaultValue",
      width: 150,
    },
    {
      title: "Type",
      dataIndex: "type",
      render: (value) => INPUT_TYPE.find(type => type.value === value)?.label,
      key: "type",
      width: 150,
    },
    {
      title: "Rule",
      dataIndex: "rule",
      render: (value) => RULES.find(rule => rule.value === value)?.label,
      key: "rule",
      width: 150,
    },
    {
      title: "Instruction",
      dataIndex: "instruction",
      key: "instruction",
    },
    {
      title: "Action",
      key: "action",
      width: 150,
      align: "center",
      render: (_, record) => (
        <div className={"instruction-detail__option-table-actions"}>
          <AntButton type={BUTTON.GHOST_WHITE} size="small" className={"btn-edit-options"} icon={<EditOutlined/>}
                     onClick={() => handleEdit(record)}>{t("EDIT")}</AntButton>
          <AntButton type={BUTTON.GHOST_WHITE} size="small" className={"btn-edit-options"} icon={<DeleteIcon/>}
                     onClick={() => handleDelete(record._id)}>{t("DELETE")}</AntButton>
        </div>
      ),
    },
  ];
  
  return (
    <div className="instruction-detail-options">
      <div className="instruction-detail-options-header">
        <AntButton type={BUTTON.DEEP_NAVY} size={"large"} icon={<PlusOutlined/>} onClick={openModalCreateOptions} className={"btn-create-options"}>{t("CREATE_OPTION")}</AntButton>
      </div>
      <TableAdmin columns={columns} dataSource={optionsData} pagination={false}
             className={"instruction-detail__option-table"} scroll={{ x: 1000 }}/>
      <ModalOptionDetail
        isShowModal={modalOptionState.isShowModal}
        optionData={modalOptionState.option}
        knowledgeData={knowledgeData}
        handleCancel={handleCloseModal}
        handleOk={handleSubmitOption}
      />
    </div>
  );
};

const mapDispatchToProps = {
  ...tool.actions,
};


export default connect(() => {}, mapDispatchToProps)(Options);