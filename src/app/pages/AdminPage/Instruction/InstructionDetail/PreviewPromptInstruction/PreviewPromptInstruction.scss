@import "src/app/styles/scroll";

.preview-prompt-instruction {

  @media screen and (max-height: 720px) {
    top: 24px;

    .prompt-wrapper {
      max-height: calc(100vh - 180px);
    }
  }

  @media screen and (min-height: 721px) {
    top: 50px;

    .prompt-wrapper {
      max-height: calc(100vh - 280px);
    }
  }

  .modal-body {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 16px 24px;

    .prompt-wrapper {
      @extend .scrollbar;
      @extend .scrollbar-show;
      display: flex;
      flex-direction: column;
      gap: 24px;
      overflow: auto;

      .prompt-role {
        font-weight: bold;
      }

      .prompt-content {
        white-space: pre-line;
      }
    }

    .copy-button{
      width: fit-content !important;
      border-radius: 8px !important;
      align-self: center;
    }
  }
}