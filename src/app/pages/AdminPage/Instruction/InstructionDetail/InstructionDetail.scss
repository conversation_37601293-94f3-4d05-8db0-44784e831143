.instruction-detail {
  background-color: var(--background-light-background-2);
  padding: 24px;
  gap: 24px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;

  .ant-tabs-tab-btn {
    font-size: 16px !important;
    font-weight: 700 !important;
    line-height: 20px !important;
  }

  .ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      color: var(--typo-colours-support-blue) !important;
    }
  }

  .ant-tabs-ink-bar {
    background-color: var(--primary-colours-blue) !important;
  }
  .tab-is-hidden{
    .ant-tabs-nav{
      display: none;
    }
  }
  .ant-form-item-label {
    label {
      height: unset !important;
    }
  }

  .ant-form-item-required {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    padding: 0px;
    flex-direction: row-reverse;

    &:after {
      display: none;
    }
  }
  .ant-tabs-nav{
    margin: 0;
  }
}