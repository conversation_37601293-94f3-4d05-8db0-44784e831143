import React, { useEffect, useMemo, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { usePageViewTracker } from "@src/ga";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Form, Input, Row } from "antd";

import { BUTTON, CONSTANT, INVITATION_STATUS } from "@constant";
import RULE from "@rule";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import SearchInput from "@component/SearchInput";

import { toast } from "@component/ToastProvider";
import ModalManagerUser from "@src/app/pages/Organization/OrganizationInfo/ManageUser/ModalManagerUser";
import ManageUser from "@src/app/pages/Organization/OrganizationInfo/ManageUser/ManageUser";
import OrganizationInfo from "@src/app/pages/Organization/OrganizationInfo/OrganizationDetail";
import Loading from "@src/app/component/Loading";

import ORGANIZATION_ICON from "@src/asset/icon/organization/organization-icon.svg";
import USER_ORGANIZATION_ICON from "@src/asset/icon/organization/user-organization-icon.svg";
import PlusIcon from "@component/SvgIcons/PlusIcon";

import { updateOrganization } from "@services/Organization";
import { getInvitationUser, invitationMember } from "@src/app/services/Invitation";
import { getOrganizationDetail } from "@src/app/services/Organization";

import * as workspaceRedux from "@src/ducks/workspace.duck";
import * as authRedux from "@src/ducks/auth.duck";

import "./OrganizationInfo.scss";

function OrganizationDetail({ user, ...props }) {
  usePageViewTracker("Organization");
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [formOrganization] = Form.useForm();
  const [formCreateNewUser] = Form.useForm();

  const [organizationData, setOrganizationData] = useState();
  const [allMembers, setAllMembers] = useState([]);
  const [searchText, setSearchText] = useState(null);
  const [isShowModalCreateNewUser, setShowModalCreateNewUser] = useState(false);
  const [isLoading, setLoading] = useState(true);

  const organizationId = useParams().id;


  useEffect(() => {
    getOrganizationData();
  }, []);

  useEffect(() => {
    setNameFromOrganization();
  }, [organizationData]);

  const membersDisplay = useMemo(() => {
    if (searchText) {
      return allMembers?.filter((member) => {
        return member?.fullName?.toLowerCase()?.includes(searchText?.toLowerCase());
      });
    } else {
      return allMembers;
    }
  }, [searchText, allMembers]);

  const isLocked = useMemo(() => !organizationData?.active, [organizationData]);

  const getOrganizationData = async () => {
    const allRequest = [
      getInvitationUser({ organizationId: organizationId, sort: "-updatedAt" }),
      getOrganizationDetail(organizationId),
    ];
    setLoading(true);
    const [allMembersResponse, organizationResponse] = await Promise.all(allRequest);
    if (allMembersResponse) {
      setAllMembers(allMembersResponse);
    }
    if (organizationResponse) {
      setOrganizationData(organizationResponse);
    }
    setLoading(false);
  };

  const setNameFromOrganization = () => {
    formOrganization.setFieldsValue({ "name": organizationData?.name });
  };

  const onClearSearchUser = () => {
    setSearchText(null);
  };

  const onSaveName = async (dataForm) => {
    const response = await updateOrganization({ _id: user?.organizationId?._id, ...dataForm });
    if (response) {
      toast.success("UPDATE_ORGANIZATION_SUCCESS");
      if (user?.organizationId?._id === organizationId) {
        props.setUser({ ...user, organizationId: { ...user.organizationId, name: response.name } });
        props.getAvailableWorkspaces();
      }
      setOrganizationData({ ...organizationData, name: response.name });
    }
  };

  const handleOpenModalCreateNewUser = () => {
    setShowModalCreateNewUser(true);
  };

  const handleCloseModalInvitation = () => {
    setShowModalCreateNewUser(false);
    formCreateNewUser.resetFields();
  };

  const onInvitationMember = async (dataUser) => {
    const apiResponse = await invitationMember({ ...dataUser, organizationId: organizationId });
    if (apiResponse) {
      const temp = allMembers?.filter((data) => data?._id !== apiResponse?._id);
      const updatedRows = [apiResponse, ...temp];
      setAllMembers(updatedRows);
      toast.success("INVITATION_TO_ADD_MEMBERS_SENT");
      handleCloseModalInvitation();
    }
  };

  const onChangeSearchUser = (e) => {
    setSearchText(e.target.value);
  };

  const onBack = () => {
    navigate(-1);
  }

  if (isLoading) {
    return <Loading active transparent />;
  }

  //auto add first admin to number of members
  const numberOfMembers = allMembers.filter(member => member?.status === INVITATION_STATUS.accepted.value)?.length + 1;

  return (
    <>
      <div className="organization-info-detail">
        <div className="organization-content">
          <div className="organization-content-rows">
            <img src={ORGANIZATION_ICON} className="organization-content-icon" />
            <span className="organization-content-title">{t("ORGANIZATION_INFOMATION")}</span>
            {/* <div className="organization-content-actions">
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="large"
                // icon={<ArrowLeftOutlined />}
                onClick={onBack}>
                {t("BACK")}
              </AntButton>
            </div> */}
          </div>
          <Row>
            <AntForm
              form={formOrganization}
              layout={"inline"}
              size="large"
              className="form-organization-info"
              onFinish={onSaveName}
            >
              <AntForm.Item label={t("ORGANIZATION_NAME")} name="name" rules={[RULE.REQUIRED]}>
                <Input />
              </AntForm.Item>
              <AntButton
                type={BUTTON.WHITE}
                size={"small"}
                onClick={setNameFromOrganization}
                className={"btn-cancel-edit-organization"}
              >
                {t("CANCEL")}
              </AntButton>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size={"large"}
                htmlType={"submit"}
              >
                {t("UPDATE")}
              </AntButton>
            </AntForm>
          </Row>
          <div className="organization-content-rows">
            <img src={USER_ORGANIZATION_ICON} className="organization-content-icon" />
            <span className="organization-content-title">
              {t("USER_ORGANIZATION")}:{" "}
              <span className="organization-content__total-member">
                {numberOfMembers} {t("MEMBER")}
              </span>
            </span>
          </div>
          <div className="organization-content-rows gap-16">
            <SearchInput
              placeholder={t("SEARCH_MEMBER")}
              size={"large"}
              onClear={onClearSearchUser}
              value={searchText}
              onChange={onChangeSearchUser}
            />

            <AntButton
              type={BUTTON.DEEP_NAVY}
              icon={<PlusIcon />}
              size={"large"}
              onClick={handleOpenModalCreateNewUser}
            >
              {t("ADD_MEMBER")}
            </AntButton>
          </div>
          <div className="organization-table-member">
            <ManageUser
              setAllMembers={setAllMembers}
              allMembers={allMembers}
              membersDisplay={membersDisplay}
            />
          </div>
        </div>
        <OrganizationInfo
          organizationData={organizationData}
          setOrganizationData={setOrganizationData}
          numberOfMembers={numberOfMembers}
        />

      </div>
      <ModalManagerUser
        form={formCreateNewUser}
        isShowModal={isShowModalCreateNewUser}
        handleOk={onInvitationMember}
        handleCancel={handleCloseModalInvitation}
        titleOk={t("ADD_MEMBER")}
        title={t("ADD_MEMBER")}
        formId="form-create-user-organization"
      ></ModalManagerUser>
    </>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = { ...workspaceRedux.actions, ...authRedux.actions };

export default connect(mapStateToProps, mapDispatchToProps)(OrganizationDetail);
