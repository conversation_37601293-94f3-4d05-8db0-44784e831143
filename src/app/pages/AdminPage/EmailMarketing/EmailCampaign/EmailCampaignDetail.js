import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Form, Input, Select, DatePicker, TimePicker, Row, Col, message, Checkbox, Card, Divider } from "antd";
import { SaveOutlined } from "@ant-design/icons";
import moment from "moment";

import { toast } from "@component/ToastProvider";
import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import RULE from "@rule";
import { LINK } from "@link";
import { BUTTON } from "@constant";

import { getCampaignDetail, createCampaign, updateCampaign } from "@services/EmailMarketing";
import { getPaginationGroups } from "@services/EmailMarketing";
import { getPaginationTemplates } from "@services/EmailMarketing";

import "./EmailCampaignDetail.scss";

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const EmailCampaignDetail = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [campaignDetail, setCampaignDetail] = useState(null);
  const [groups, setGroups] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [campaignType, setCampaignType] = useState("manual");
  const [frequency, setFrequency] = useState("once");
  const isCreating = !id || id === "create";

  // Định nghĩa các loại chiến dịch
  const CAMPAIGN_TYPES = [
    { value: "manual", label: t("CAMPAIGN_TYPE_MANUAL") },
    { value: "automatic", label: t("CAMPAIGN_TYPE_AUTOMATED") }
  ];

  // Định nghĩa các tần suất gửi
  const FREQUENCY_OPTIONS = [
    { value: "once", label: t("FREQUENCY_ONCE") },
    { value: "daily", label: t("FREQUENCY_DAILY") },
    { value: "weekly", label: t("FREQUENCY_WEEKLY") },
    { value: "monthly", label: t("FREQUENCY_MONTHLY") }
  ];

  // Định nghĩa các ngày trong tuần (theo thứ tự từ Chủ nhật đến Thứ bảy)
  const WEEKDAY_OPTIONS = [
    { value: "sunday", label: t("SUNDAY") },
    { value: "monday", label: t("MONDAY") },
    { value: "tuesday", label: t("TUESDAY") },
    { value: "wednesday", label: t("WEDNESDAY") },
    { value: "thursday", label: t("THURSDAY") },
    { value: "friday", label: t("FRIDAY") },
    { value: "saturday", label: t("SATURDAY") }
  ];

  useEffect(() => {
    fetchGroups();
    fetchTemplates();

    if (!isCreating) {
      fetchCampaignDetail();
    }
  }, [id]);

  const fetchCampaignDetail = async () => {
    setLoading(true);
    try {
      const response = await getCampaignDetail(id);
      if (response) {
        setCampaignDetail(response);
        setCampaignType(response.type);
        setFrequency(response.schedule?.frequency || "once");

        // Format data for form
        const formData = {
          ...response,
          targetGroups: response.targetGroups?.map(group => group._id) || [],
          templateId: response.templateId?._id,
          dateRange: response.schedule?.startDate && response.schedule?.endDate ? [
            moment(response.schedule.startDate),
            moment(response.schedule.endDate)
          ] : undefined,
          timeOfDay: response.schedule?.timeOfDay ? moment(response.schedule.timeOfDay, "HH:mm") : undefined,
          frequency: response.schedule?.frequency || "once",
          daysOfWeek: response.schedule?.daysOfWeek ? response.schedule.daysOfWeek.map(day => {
            // Chuyển đổi từ số (0-6) sang tên ngày
            const dayMap = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
            return dayMap[day];
          }) : []
        };

        form.setFieldsValue(formData);
      }
    } catch (error) {
      console.error("Error fetching campaign detail:", error);
      toast.error(t("ERROR_FETCHING_CAMPAIGN"));
    } finally {
      setLoading(false);
    }
  };

  const fetchGroups = async () => {
    try {
      const response = await getPaginationGroups({ page: 1, pageSize: 100 }, {});
      if (response && response.rows) {
        setGroups(response.rows);
      }
    } catch (error) {
      console.error("Error fetching groups:", error);
    }
  };

  const fetchTemplates = async () => {
    try {
      const response = await getPaginationTemplates({ page: 1, pageSize: 100 }, {});
      if (response && response.rows) {
        setTemplates(response.rows);
      }
    } catch (error) {
      console.error("Error fetching templates:", error);
    }
  };

  const handleTypeChange = (value) => {
    setCampaignType(value);
  };

  const handleFrequencyChange = (value) => {
    setFrequency(value);
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      // Lấy giá trị từ form
      const values = await form.validateFields();

      // Format schedule data
      const formattedValues = {
        ...values,
        schedule: {
          frequency: values.frequency,
          startDate: values.dateRange && values.timeOfDay ?
            values.dateRange[0]
              .hour(values.timeOfDay.hour())
              .minute(values.timeOfDay.minute())
              .second(0)
              .millisecond(0)
              .toISOString() : undefined,
          endDate: values.dateRange && values.timeOfDay ?
            values.dateRange[1]
              .hour(values.timeOfDay.hour())
              .minute(values.timeOfDay.minute())
              .second(0)
              .millisecond(0)
              .toISOString() : undefined,
          timeOfDay: values.timeOfDay ? values.timeOfDay.format("HH:mm") : undefined,
          daysOfWeek: values.daysOfWeek ? values.daysOfWeek.map(day => {
            // Chuyển đổi từ tên ngày sang số (0-6, Chủ nhật - Thứ bảy)
            const dayMap = { sunday: 0, monday: 1, tuesday: 2, wednesday: 3, thursday: 4, friday: 5, saturday: 6 };
            return dayMap[day.toLowerCase()];
          }) : []
        }
      };

      // Remove form-only fields
      delete formattedValues.frequency;
      delete formattedValues.dateRange;
      delete formattedValues.timeOfDay;
      delete formattedValues.daysOfWeek;

      let response;
      if (isCreating) {
        response = await createCampaign(formattedValues);
        if (response) {
          toast.success(t("CREATE_CAMPAIGN_SUCCESS"));
          navigate(LINK.ADMIN.EMAIL_CAMPAIGN);
        } else {
          toast.error(t("CREATE_CAMPAIGN_ERROR"));
        }
      } else {
        formattedValues._id = id;
        response = await updateCampaign(formattedValues);
        if (response) {
          toast.success(t("UPDATE_CAMPAIGN_SUCCESS"));
          navigate(LINK.ADMIN.EMAIL_CAMPAIGN);
        } else {
          toast.error(t("UPDATE_CAMPAIGN_ERROR"));
        }
      }
    } catch (error) {
      console.error("Error saving campaign:", error);
      toast.error(t("ERROR_SAVING_CAMPAIGN"));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.EMAIL_CAMPAIGN);
  };

  return (
    <div className="email-campaign-detail-container">
      <div className="email-campaign-detail__header">
        <div className="header-content">
          <div className="header-title">{isCreating ? t("CREATE_NEW_CAMPAIGN") : t("EDIT_CAMPAIGN")}</div>
          <div className="header-description">{t("EMAIL_CAMPAIGN_MANAGEMENT_DESCRIPTION")}</div>
        </div>
      </div>

      <Loading active={loading} transparent>
        <div className="campaign-info-container">
          <div className="info-header">
            <h3 className="section-title">{t("CAMPAIGN_INFORMATION")}</h3>
          </div>
          <div className="info-content">
            <AntForm
              form={form}
              layout="vertical"
              requiredMark={true}
              className="form-campaign-info"
            >
              <div className="form-row">
                <AntForm.Item
                  label={t("CAMPAIGN_NAME")}
                  name="name"
                  rules={[{ required: true, message: t("PLEASE_ENTER_CAMPAIGN_NAME") }]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_CAMPAIGN_NAME")} />
                </AntForm.Item>

                <AntForm.Item
                  label={t("CAMPAIGN_TYPE")}
                  name="type"
                  rules={[{ required: true, message: t("PLEASE_SELECT_CAMPAIGN_TYPE") }]}
                  initialValue="manual"
                  className="status-field"
                >
                  <Select onChange={handleTypeChange}>
                    {CAMPAIGN_TYPES.map(type => (
                      <Option key={type.value} value={type.value}>{type.label}</Option>
                    ))}
                  </Select>
                </AntForm.Item>
              </div>

              <AntForm.Item
                label={t("CAMPAIGN_DESCRIPTION")}
                name="description"
              >
                <TextArea rows={4} placeholder={t("ENTER_CAMPAIGN_DESCRIPTION")} />
              </AntForm.Item>

              <div className="form-row">
                <AntForm.Item
                  label={t("TARGET_GROUPS")}
                  name="targetGroups"
                  rules={[{ required: true, message: t("PLEASE_SELECT_TARGET_GROUPS") }]}
                  className="title-field"
                >
                  <Select
                    mode="multiple"
                    placeholder={t("SELECT_TARGET_GROUPS")}
                    optionFilterProp="children"
                  >
                    {groups.map(group => (
                      <Option key={group._id} value={group._id}>{group.name}</Option>
                    ))}
                  </Select>
                </AntForm.Item>

                <AntForm.Item
                  label={t("EMAIL_TEMPLATE")}
                  name="templateId"
                  rules={[{ required: true, message: t("PLEASE_SELECT_EMAIL_TEMPLATE") }]}
                  className="status-field"
                >
                  <Select
                    placeholder={t("SELECT_EMAIL_TEMPLATE")}
                    optionFilterProp="children"
                  >
                    {templates.map(template => (
                      <Option key={template._id} value={template._id}>{template.name}</Option>
                    ))}
                  </Select>
                </AntForm.Item>
              </div>
            </AntForm>
          </div>
        </div>

        <div className="schedule-settings-container">
          <div className="info-header">
            <h3 className="section-title">{t("SCHEDULE_SETTINGS")}</h3>
          </div>
          <div className="info-content">
            <AntForm
              form={form}
              layout="vertical"
              requiredMark={true}
              className="form-schedule-settings"
            >
              <div className="form-row">
                <AntForm.Item
                  label={t("FREQUENCY")}
                  name="frequency"
                  rules={[{ required: true, message: t("PLEASE_SELECT_FREQUENCY") }]}
                  initialValue="once"
                  className="title-field"
                >
                  <Select onChange={handleFrequencyChange}>
                    {FREQUENCY_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>{option.label}</Option>
                    ))}
                  </Select>
                </AntForm.Item>

                <AntForm.Item
                  label={t("DATE_RANGE")}
                  name="dateRange"
                  rules={[{ required: true, message: t("PLEASE_SELECT_DATE_RANGE") }]}
                  className="status-field"
                >
                  <RangePicker style={{ width: '100%', height: '46px' }} />
                </AntForm.Item>
              </div>

              <div className="form-row">
                <AntForm.Item
                  label={t("TIME_OF_DAY")}
                  name="timeOfDay"
                  rules={[{ required: true, message: t("PLEASE_SELECT_TIME") }]}
                  className="title-field"
                >
                  <TimePicker format="HH:mm" style={{ width: '100%', height: '46px' }} />
                </AntForm.Item>

                {frequency === "weekly" && (
                  <AntForm.Item
                    label={t("DAYS_OF_WEEK")}
                    name="daysOfWeek"
                    rules={[{ required: true, message: t("PLEASE_SELECT_DAYS") }]}
                    className="status-field"
                  >
                    <Checkbox.Group options={WEEKDAY_OPTIONS} className="days-checkbox-group" />
                  </AntForm.Item>
                )}
              </div>
            </AntForm>
          </div>
        </div>

        <div className="save-button-container">
          <AntButton
            type={BUTTON.LIGHT_NAVY}
            size="large"
            onClick={handleCancel}
            style={{ marginRight: '16px' }}
            className="cancel-button"
          >
            {t("CANCEL")}
          </AntButton>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            onClick={handleSave}
            className="save-button"
            icon={<SaveOutlined />}
          >
            {isCreating ? t("CREATE") : t("SAVE")}
          </AntButton>
        </div>
      </Loading>
    </div>
  );
};

export default EmailCampaignDetail;
