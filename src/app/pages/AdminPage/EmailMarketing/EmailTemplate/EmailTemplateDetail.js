import React, { useEffect, useState, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Form, Input, Select, Row, Col, Tabs, Button, Card, Modal } from "antd";
import { ArrowLeftOutlined, SaveOutlined, EyeOutlined } from "@ant-design/icons";

import { toast } from "@component/ToastProvider";
import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import RULE from "@rule";
import { LINK } from "@link";
import { BUTTON } from "@constant";

import { getTemplateDetail, createTemplate, updateTemplate, sendTestEmail } from "@services/EmailMarketing";

import "../EmailMarketing.scss";
import "./EmailTemplateDetail.scss";

const { TabPane } = Tabs;

const EmailTemplateDetail = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();

  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [htmlContent, setHtmlContent] = useState("");
  const [previewMode, setPreviewMode] = useState(false);
  const [previewData, setPreviewData] = useState({});
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [testEmail, setTestEmail] = useState("");
  const [isSendingTestEmail, setIsSendingTestEmail] = useState(false);

  // Theo dõi category được chọn
  const selectedCategory = Form.useWatch('category', form);

  const isCreating = !id || id === "create";

  // Function phân tích biến được sử dụng trong template
  const getUsedVariables = useCallback((subject, content) => {
    const combinedText = `${subject || ''} ${content || ''}`;
    const variableRegex = /\{([^}]+)\}/g;
    const usedVariables = new Set();
    let match;

    while ((match = variableRegex.exec(combinedText)) !== null) {
      usedVariables.add(match[1]);
    }

    return Array.from(usedVariables);
  }, []);

  // Tự động áp dụng template khi category thay đổi
  useEffect(() => {
    // Chỉ auto-apply sau khi dữ liệu đã được load (tránh ghi đè dữ liệu ban đầu)
    if (selectedCategory && SAMPLE_TEMPLATES[selectedCategory] && isDataLoaded) {
      const template = SAMPLE_TEMPLATES[selectedCategory];

      // Luôn auto-apply template khi category thay đổi (cho cả tạo mới và chỉnh sửa)
      form.setFieldsValue({
        subject: template.subject
      });
      setHtmlContent(template.content);

      // Cập nhật preview data cho các biến được sử dụng
      const usedVarNames = getUsedVariables(template.subject, template.content);
      const newPreviewData = {};
      usedVarNames.forEach(varName => {
        const variable = AVAILABLE_VARIABLES.find(v => v.name === `{${varName}}`);
        if (variable) {
          newPreviewData[varName] = variable.defaultValue;
        }
      });
      setPreviewData(newPreviewData);
    }
  }, [selectedCategory, form, getUsedVariables, isDataLoaded]);

  // Định nghĩa các danh mục mẫu email
  const TEMPLATE_CATEGORIES = [
    { value: "welcome", label: t("TEMPLATE_CATEGORY_WELCOME") },
    { value: "promotion", label: t("TEMPLATE_CATEGORY_PROMOTION") },
    { value: "reminder", label: t("TEMPLATE_CATEGORY_REMINDER") },
    { value: "notification", label: t("TEMPLATE_CATEGORY_NOTIFICATION") },
    { value: "other", label: t("TEMPLATE_CATEGORY_OTHER") }
  ];

  // Template mẫu theo category
  const SAMPLE_TEMPLATES = {
    welcome: {
      subject: "Chào mừng {name} đến với {company_name}!",
      content: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <h2 style="color: #1890ff;">Chào mừng bạn đến với {company_name}!</h2>
          <p>Xin chào {name},</p>
          <p>Cảm ơn bạn đã đăng ký tài khoản tại {company_name}. Chúng tôi rất vui mừng được chào đón bạn!</p>
          <p>Thông tin tài khoản của bạn:</p>
          <ul>
            <li>Email: {email}</li>
            <li>Ngày đăng ký: {registration_date}</li>
          </ul>
          <p>Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với chúng tôi qua email: {support_email}</p>
          <p>Trân trọng,<br>Đội ngũ {company_name}</p>
        </div>
      `
    },
    reminder: {
      subject: "Nhắc nhở: Gói dùng thử của {name} sẽ hết hạn vào {trial_end_date}",
      content: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <h2 style="color: #faad14;">Thông báo hết hạn dùng thử</h2>
          <p>Xin chào {name},</p>
          <p>Gói dùng thử <strong>{package_name}</strong> của bạn sẽ hết hạn vào <strong>{trial_end_date}</strong>.</p>
          <p>Để tiếp tục sử dụng dịch vụ, vui lòng gia hạn tài khoản của bạn:</p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="{personal_link}" style="background-color: #1890ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Gia hạn ngay</a>
          </div>
          <p>Nếu bạn có thắc mắc, liên hệ: {support_email} hoặc {phone}</p>
          <p>Trân trọng,<br>Đội ngũ {company_name}</p>
        </div>
      `
    },
    promotion: {
      subject: "🎉 Ưu đãi đặc biệt dành cho {name}!",
      content: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <h2 style="color: #52c41a;">🎉 Ưu đãi đặc biệt!</h2>
          <p>Xin chào {name},</p>
          <p>Chúng tôi có một ưu đãi đặc biệt dành riêng cho bạn!</p>
          <p>Nâng cấp lên gói <strong>{package_name}</strong> với mức giá ưu đãi.</p>
          <div style="background-color: #f6ffed; padding: 15px; border-left: 4px solid #52c41a; margin: 20px 0;">
            <p style="margin: 0; font-weight: bold;">Ưu đãi có hiệu lực đến: {trial_end_date}</p>
          </div>
          <div style="text-align: center; margin: 20px 0;">
            <a href="{personal_link}" style="background-color: #52c41a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Nhận ưu đãi ngay</a>
          </div>
          <p>Liên hệ: {support_email}</p>
          <p>Trân trọng,<br>Đội ngũ {company_name}</p>
        </div>
      `
    },
    notification: {
      subject: "Thông báo quan trọng từ {company_name}",
      content: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <h2 style="color: #722ed1;">Thông báo quan trọng</h2>
          <p>Xin chào {name},</p>
          <p>Chúng tôi có thông báo quan trọng gửi đến bạn.</p>
          <p>Vui lòng đăng nhập vào tài khoản để xem chi tiết:</p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="{personal_link}" style="background-color: #722ed1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Xem chi tiết</a>
          </div>
          <p>Nếu bạn không muốn nhận email này, <a href="{unsubscribe_link}">hủy đăng ký tại đây</a>.</p>
          <p>Liên hệ hỗ trợ: {support_email}</p>
          <p>Trân trọng,<br>Đội ngũ {company_name}</p>
        </div>
      `
    },
    other: {
      subject: "Email từ {company_name}",
      content: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <h2 style="color: #595959;">Email thông tin</h2>
          <p>Xin chào {name},</p>
          <p>Đây là email thông tin từ {company_name}.</p>
          <p>Nội dung email sẽ được tùy chỉnh theo nhu cầu cụ thể.</p>
          <p>Nếu bạn có thắc mắc, vui lòng liên hệ: {support_email}</p>
          <p>Trân trọng,<br>Đội ngũ {company_name}</p>
        </div>
      `
    }
  };

  // Định nghĩa các biến có sẵn
  const AVAILABLE_VARIABLES = [
    { name: "{name}", description: "Tên khách hàng", defaultValue: "Nguyễn Văn A" },
    { name: "{trial_end_date}", description: "Ngày hết hạn dùng thử", defaultValue: "31/12/2024" },
    { name: "{personal_link}", description: "Đường dẫn cá nhân hoá", defaultValue: "https://example.com/renew/abc123" },
    { name: "{email}", description: "Email khách hàng", defaultValue: "<EMAIL>" },
    { name: "{company_name}", description: "Tên công ty", defaultValue: "Công ty Cổ phần Công nghệ Clickee" },
    { name: "{support_email}", description: "Email hỗ trợ", defaultValue: "<EMAIL>" },
    { name: "{phone}", description: "Số điện thoại", defaultValue: "0123456789" },
    { name: "{package_name}", description: "Tên gói dịch vụ", defaultValue: "Gói Premium" },
    { name: "{registration_date}", description: "Ngày đăng ký", defaultValue: "01/01/2024" },
    { name: "{unsubscribe_link}", description: "Liên kết hủy đăng ký", defaultValue: "https://example.com/unsubscribe" }
  ];

  // Lấy dữ liệu mẫu email khi id thay đổi
  useEffect(() => {
    if (!isCreating) {
      getTemplateData();
    } else {
      // Đặt giá trị mặc định cho mẫu mới và áp dụng template welcome
      form.setFieldsValue({
        category: "welcome"
      });

      // Áp dụng template welcome mặc định
      const welcomeTemplate = SAMPLE_TEMPLATES.welcome;
      if (welcomeTemplate) {
        form.setFieldsValue({
          category: "welcome",
          subject: welcomeTemplate.subject
        });
        setHtmlContent(welcomeTemplate.content);

        // Cập nhật preview data
        const usedVarNames = getUsedVariables(welcomeTemplate.subject, welcomeTemplate.content);
        const newPreviewData = {};
        usedVarNames.forEach(varName => {
          const variable = AVAILABLE_VARIABLES.find(v => v.name === `{${varName}}`);
          if (variable) {
            newPreviewData[varName] = variable.defaultValue;
          }
        });
        setPreviewData(newPreviewData);
      }

      setLoading(false);
      setIsDataLoaded(true); // Đánh dấu đã load xong dữ liệu
    }
  }, [id, form, getUsedVariables]);

  const getTemplateData = async () => {
    setLoading(true);
    try {
      const data = await getTemplateDetail(id);
      if (data) {
        form.setFieldsValue(data);
        setHtmlContent(data.content || "");
        setIsDataLoaded(true); // Đánh dấu đã load xong dữ liệu
      } else {
        toast.error(t("ERROR_FETCHING_TEMPLATE"));
      }
    } catch (error) {
      console.error('Error loading template data:', error);
      toast.error(t("ERROR_FETCHING_TEMPLATE"));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    try {
      setLoading(true);
      const saveData = {
        ...values,
        content: htmlContent
      };

      let response;
      if (isCreating) {
        response = await createTemplate(saveData);
        if (response) {
          toast.success(t("CREATE_TEMPLATE_SUCCESS"));
          navigate(LINK.ADMIN.EMAIL_TEMPLATE);
        } else {
          toast.error(t("CREATE_TEMPLATE_ERROR"));
        }
      } else {
        saveData._id = id;
        response = await updateTemplate(saveData);
        if (response) {
          toast.success(t("UPDATE_TEMPLATE_SUCCESS"));
          navigate(LINK.ADMIN.EMAIL_TEMPLATE);
        } else {
          toast.error(t("UPDATE_TEMPLATE_ERROR"));
        }
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error(t("ERROR_SAVING_TEMPLATE"));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.EMAIL_TEMPLATE);
  };

  const insertVariable = (variable) => {
    // Thêm biến vào nội dung HTML tại vị trí con trỏ
    setHtmlContent((prevContent) => {
      return prevContent + " " + variable.name + " ";
    });
  };

  const togglePreview = () => {
    setPreviewMode(!previewMode);
  };

  // Khởi tạo dữ liệu mẫu mặc định
  useEffect(() => {
    const defaultData = {};
    AVAILABLE_VARIABLES.forEach(variable => {
      const key = variable.name.replace(/[{}]/g, '');
      defaultData[key] = variable.defaultValue;
    });
    setPreviewData(defaultData);
  }, []);

  // Function lấy biến có sẵn được sử dụng
  const getUsedAvailableVariables = () => {
    const currentSubject = form.getFieldValue('subject') || '';
    const currentContent = htmlContent || '';
    const usedVarNames = getUsedVariables(currentSubject, currentContent);

    return AVAILABLE_VARIABLES.filter(variable => {
      const varName = variable.name.replace(/[{}]/g, '');
      return usedVarNames.includes(varName);
    });
  };

  // Function thay thế biến trong nội dung
  const replaceVariables = (content, data) => {
    let replacedContent = content;
    Object.keys(data).forEach(key => {
      const placeholder = `{${key}}`;
      const value = data[key] || placeholder;
      replacedContent = replacedContent.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
    });
    return replacedContent;
  };

  // Function áp dụng template mẫu
  const applySampleTemplate = (category) => {
    const template = SAMPLE_TEMPLATES[category];
    if (template) {
      form.setFieldsValue({
        subject: template.subject,
        category: category
      });
      setHtmlContent(template.content);

      // Cập nhật preview data cho các biến được sử dụng
      const usedVarNames = getUsedVariables(template.subject, template.content);
      const newPreviewData = {};
      usedVarNames.forEach(varName => {
        const variable = AVAILABLE_VARIABLES.find(v => v.name === `{${varName}}`);
        if (variable) {
          newPreviewData[varName] = variable.defaultValue;
        }
      });
      setPreviewData(newPreviewData);
    }
  };

  // Hiển thị modal preview với dữ liệu mẫu
  const showPreview = () => {
    setShowPreviewModal(true);
  };

  // Cập nhật dữ liệu preview
  const handlePreviewDataChange = (key, value) => {
    setPreviewData(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Xử lý gửi email test
  const handleSendTestEmail = async () => {
    if (!testEmail) {
      toast.error(t("PLEASE_ENTER_TEST_EMAIL"));
      return;
    }

    if (!testEmail.includes('@')) {
      toast.error(t("PLEASE_ENTER_VALID_EMAIL"));
      return;
    }

    const templateData = form.getFieldsValue();
    if (!templateData.subject || !htmlContent) {
      toast.error(t("PLEASE_COMPLETE_TEMPLATE_BEFORE_TESTING"));
      return;
    }

    try {
      setIsSendingTestEmail(true);

      const testData = {
        templateId: id && !isCreating ? id : null,
        to: testEmail,
        data: {
          subject: templateData.subject,
          content: htmlContent,
          previewData: previewData
        }
      };

      const response = await sendTestEmail(testData);
      if (response) {
        toast.success(t("TEST_EMAIL_SENT_SUCCESSFULLY"));
        setTestEmail(""); // Clear input after successful send
      } else {
        toast.error(t("FAILED_TO_SEND_TEST_EMAIL"));
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      toast.error(t("ERROR_SENDING_TEST_EMAIL"));
    } finally {
      setIsSendingTestEmail(false);
    }
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="email-template-detail-container">
        <div className="email-template-detail__header">
          <div className="header-content">
            <div className="header-title">{isCreating ? t("CREATE_NEW_TEMPLATE") : t("EDIT_TEMPLATE")}</div>
            <div className="header-description">{t("EMAIL_TEMPLATE_MANAGEMENT_DESCRIPTION")}</div>
          </div>
        </div>

        <div className="template-info-container">
          <div className="info-header">
            <h3 className="section-title">{t("TEMPLATE_INFORMATION")}</h3>
          </div>
          <div className="info-content">
            <AntForm
              form={form}
              onFinish={handleSave}
              layout="vertical"
              requiredMark={true}
              className="form-template-info"
            >
              <div className="form-row">
                <AntForm.Item
                  name="name"
                  label={t("TEMPLATE_NAME")}
                  rules={[{ required: true, message: t("PLEASE_ENTER_TEMPLATE_NAME") }]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_TEMPLATE_NAME")} />
                </AntForm.Item>

                <AntForm.Item
                  name="category"
                  label={t("CATEGORY")}
                  rules={[{ required: true, message: t("PLEASE_SELECT_CATEGORY") }]}
                  className="category-field"
                >
                  <Select
                    placeholder={t("SELECT_CATEGORY")}
                    options={TEMPLATE_CATEGORIES}
                  />
                </AntForm.Item>
              </div>

              <div className="form-row">
                <AntForm.Item
                  name="subject"
                  label={t("EMAIL_SUBJECT")}
                  rules={[{ required: true, message: t("PLEASE_ENTER_EMAIL_SUBJECT") }]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_EMAIL_SUBJECT")} />
                </AntForm.Item>
              </div>
            </AntForm>
          </div>
        </div>

        <div className="template-content-container">
          <div className="info-header">
            <h3 className="section-title">{t("CONTENT")}</h3>
          </div>
          <div className="info-content">
            {/* Template mẫu info */}
            <Card className="sample-template-card" size="small">
              <div className="card-header">
                <h4 className="card-title">
                  <span className="card-icon">📋</span>
                  {t("CURRENT_TEMPLATE_INFO")}
                </h4>
                <p className="card-description">{t("TEMPLATE_AUTO_APPLIED_DESCRIPTION")}</p>
              </div>
              <div className="current-template-info">
                {!selectedCategory ? (
                  <div className="no-category-message">
                    <div className="empty-state">
                      <span className="empty-icon">📂</span>
                      <p className="empty-text">{t("PLEASE_SELECT_CATEGORY_FIRST")}</p>
                      <p className="empty-hint">{t("SELECT_CATEGORY_TO_AUTO_APPLY_TEMPLATE")}</p>
                    </div>
                  </div>
                ) : SAMPLE_TEMPLATES[selectedCategory] ? (
                  <div className="template-info-display">
                    <div className="template-category-info">
                      <span className="template-icon">
                        {selectedCategory === 'welcome' && '🎉'}
                        {selectedCategory === 'reminder' && '⏰'}
                        {selectedCategory === 'promotion' && '🎁'}
                        {selectedCategory === 'notification' && '📢'}
                        {selectedCategory === 'other' && '📄'}
                      </span>
                      <div className="template-details">
                        <h5 className="template-category-name">
                          {TEMPLATE_CATEGORIES.find(cat => cat.value === selectedCategory)?.label || selectedCategory}
                        </h5>
                        <p className="template-status">{t("TEMPLATE_WILL_AUTO_APPLY")}</p>
                      </div>
                    </div>
                    <div className="template-actions">
                      <AntButton
                        onClick={() => applySampleTemplate(selectedCategory)}
                        type={BUTTON.LIGHT_NAVY}
                        size="small"
                        className="reapply-template-button"
                      >
                        {t("REAPPLY_TEMPLATE")}
                      </AntButton>
                    </div>
                  </div>
                ) : (
                  <div className="no-templates-message">
                    <div className="empty-state">
                      <span className="empty-icon">📝</span>
                      <p className="empty-text">{t("NO_TEMPLATES_FOR_CATEGORY")}</p>
                      <p className="empty-hint">{t("TRY_DIFFERENT_CATEGORY")}</p>
                    </div>
                  </div>
                )}
              </div>
            </Card>
            <Card className="email-variables-card" size="small">
              <div className="card-header">
                <h4 className="card-title">
                  <span className="card-icon">🔧</span>
                  {t("AVAILABLE_VARIABLES")}
                </h4>
                <p className="card-description">{t("CLICK_TO_INSERT_VARIABLE")}</p>
              </div>
              <div className="email-variables-grid">
                {AVAILABLE_VARIABLES.map((variable, index) => (
                  <div key={index} className="variable-item-card">
                    <AntButton
                      onClick={() => insertVariable(variable)}
                      className="variable-button"
                      type={BUTTON.GHOST_WHITE}
                      size="small"
                    >
                      {variable.name}
                    </AntButton>
                    <span className="variable-description">{variable.description}</span>
                  </div>
                ))}
              </div>
            </Card>

            <Card className="email-editor-card" size="small">
              <div className="card-header">
                <h4 className="card-title">
                  <span className="card-icon">✏️</span>
                  {t("EMAIL_CONTENT")}
                </h4>
                <div className="card-actions">
                  <AntButton
                    onClick={togglePreview}
                    type={BUTTON.LIGHT_NAVY}
                    size="middle"
                    className="preview-toggle-button"
                    style={{ marginRight: '12px' }}
                  >
                    {previewMode ? t("EDIT_MODE") : t("PREVIEW_MODE")}
                  </AntButton>
                  <AntButton
                    onClick={showPreview}
                    type={BUTTON.DEEP_NAVY}
                    size="middle"
                    className="preview-button"
                    icon={<EyeOutlined />}
                  >
                    {t("PREVIEW_EMAIL")}
                  </AntButton>
                </div>
              </div>

              <div className="email-editor-content">
                {previewMode ? (
                  <div className="email-preview-container">
                    <div
                      className="email-preview"
                      dangerouslySetInnerHTML={{ __html: htmlContent }}
                    />
                  </div>
                ) : (
                  <Input.TextArea
                    value={htmlContent}
                    onChange={(e) => setHtmlContent(e.target.value)}
                    autoSize={{ minRows: 12, maxRows: 25 }}
                    placeholder={t("ENTER_EMAIL_CONTENT_HTML")}
                    className="email-content-editor"
                  />
                )}
              </div>
            </Card>
          </div>
        </div>

        <div className="template-content-container">
          <div className="info-header">
            <h3 className="section-title">{t("TEST_SEND")}</h3>
          </div>
          <div className="info-content">
            <div className="test-email-container">
              <div className="test-email-label">{t("TEST_EMAIL")}</div>
              <div className="test-email-input-group">
                <Input
                  placeholder={t("ENTER_TEST_EMAIL")}
                  className="test-email-input"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  onPressEnter={handleSendTestEmail}
                />
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  size="middle"
                  className="test-email-button"
                  onClick={handleSendTestEmail}
                  loading={isSendingTestEmail}
                  disabled={!testEmail || isSendingTestEmail}
                >
                  {t("SEND_TEST_EMAIL")}
                </AntButton>
              </div>
            </div>
          </div>
        </div>

        <div className="save-button-container">
          <AntButton
            type={BUTTON.LIGHT_NAVY}
            size="large"
            onClick={handleCancel}
            style={{ marginRight: '16px' }}
            className="cancel-button"
          >
            {t("CANCEL")}
          </AntButton>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            onClick={() => form.submit()}
            className="save-button"
            icon={<SaveOutlined />}
          >
            {isCreating ? t("CREATE") : t("SAVE")}
          </AntButton>
        </div>

        {/* Modal Preview Email */}
        <Modal
          title={
            <div className="modal-header">
              <span className="modal-icon">📧</span>
              <span className="modal-title">{t("PREVIEW_EMAIL")}</span>
            </div>
          }
          open={showPreviewModal}
          onCancel={() => setShowPreviewModal(false)}
          width={1000}
          footer={[
            <AntButton
              key="close"
              onClick={() => setShowPreviewModal(false)}
              type={BUTTON.LIGHT_NAVY}
              size="large"
              className="modal-close-button"
            >
              {t("CLOSE")}
            </AntButton>
          ]}
          className="email-preview-modal"
          centered
        >
          <div className="preview-modal-content">
            {/* Form nhập dữ liệu mẫu - chỉ hiển thị biến được sử dụng */}
            <Card className="preview-data-card" size="small">
              <div className="card-header">
                <h4 className="card-title">
                  <span className="card-icon">📝</span>
                  {t("SAMPLE_DATA_FOR_USED_VARIABLES")}
                </h4>
                <p className="card-description">{t("ENTER_SAMPLE_DATA_DESCRIPTION")}</p>
              </div>
              <div className="preview-form-content">
                {(() => {
                  const usedVariables = getUsedAvailableVariables();
                  if (usedVariables.length === 0) {
                    return (
                      <div className="no-variables-message">
                        <div className="empty-state">
                          <span className="empty-icon">📭</span>
                          <p className="empty-text">{t("NO_VARIABLES_USED_IN_TEMPLATE")}</p>
                          <p className="empty-hint">{t("ADD_VARIABLES_TO_TEMPLATE_HINT")}</p>
                        </div>
                      </div>
                    );
                  }
                  return (
                    <Row gutter={[16, 16]}>
                      {usedVariables.map((variable, index) => {
                        const key = variable.name.replace(/[{}]/g, '');
                        return (
                          <Col span={12} key={index}>
                            <div className="preview-input-group">
                              <label className="preview-label">
                                <span className="variable-name">{variable.name}</span>
                                <span className="variable-desc"> - {variable.description}</span>
                              </label>
                              <Input
                                value={previewData[key] || ''}
                                onChange={(e) => handlePreviewDataChange(key, e.target.value)}
                                placeholder={variable.defaultValue}
                                size="middle"
                                className="preview-input"
                              />
                            </div>
                          </Col>
                        );
                      })}
                    </Row>
                  );
                })()}
              </div>
            </Card>

            {/* Preview email */}
            <Card className="preview-email-card" size="small">
              <div className="card-header">
                <h4 className="card-title">
                  <span className="card-icon">📧</span>
                  {t("EMAIL_PREVIEW")}
                </h4>
                <p className="card-description">{t("EMAIL_PREVIEW_DESCRIPTION")}</p>
              </div>

              {/* Email mockup container */}
              <div className="email-mockup">
                {/* Email header */}
                <div className="email-header">
                  <div className="email-meta-row">
                    <div className="email-from">
                      <strong>From:</strong> {previewData.company_name || 'Công ty Cổ phần Công nghệ Clickee'} &lt;{previewData.support_email || '<EMAIL>'}&gt;
                    </div>
                    <div className="email-date">
                      <strong>Date:</strong> {new Date().toLocaleString('vi-VN')}
                    </div>
                  </div>
                  <div className="email-meta-row">
                    <div className="email-to">
                      <strong>To:</strong> {previewData.name || 'Nguyễn Văn A'} &lt;{previewData.email || 'cus <EMAIL>'}&gt;
                    </div>
                  </div>
                  <div className="email-subject-row">
                    <div className="email-subject">
                      <strong>Subject:</strong> {replaceVariables(form.getFieldValue('subject') || '', previewData)}
                    </div>
                  </div>
                </div>

                {/* Email body */}
                <div className="email-body">
                  <div
                    className="email-content-preview"
                    dangerouslySetInnerHTML={{
                      __html: replaceVariables(htmlContent, previewData)
                    }}
                  />

                  {/* Email footer */}
                  <div className="email-footer">
                    <hr style={{ margin: '20px 0', border: '1px solid #eee' }} />
                    <p style={{ fontSize: '12px', color: '#999', textAlign: 'center' }}>
                      Email này được gửi từ {previewData.company_name || 'Công ty Cổ phần Công nghệ Clickee'}
                      {previewData.unsubscribe_link && (
                        <>
                          <br />
                          <a href={previewData.unsubscribe_link} style={{ color: '#999' }}>
                            Hủy đăng ký nhận email
                          </a>
                        </>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </Modal>
      </div>
    </Loading>
  );
};

export default EmailTemplateDetail;
