.filter-form-openai-cost {
  width: 100%;

  .filter-row {
    width: 100%;
  }

  .filter-inputs-row {
    overflow-x: auto;
    padding-bottom: 8px; // For scrollbar space

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  // Form item styling
  .search-form-item {
    margin: 0;

    .ant-select-selector,
    .ant-picker {
      height: 40px !important;
      display: flex;
      align-items: center;
    }

    .ant-select-selection-search {
      display: flex;
      align-items: center;
    }

    .ant-select-selection-placeholder,
    .ant-select-selection-item {
      display: flex;
      align-items: center;
    }

    // Style for component wrapper
    .component-wrapper {
      height: 40px;
      display: flex;
      align-items: center;
    }
  }

  // Time select specific styling
  .time-select {
    .ant-select {
      height: 40px;
    }
  }

  // Date picker specific styling
  .date-picker-item {
    .ant-picker {
      width: 100%;
      height: 40px;
    }
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    white-space: nowrap;
  }

  // Responsive styles
  @media (max-width: 768px) {
    .filter-row {
      flex-direction: column;
      align-items: stretch;
    }

    .filter-inputs-row {
      margin-bottom: 16px;
    }

    .search-buttons {
      width: 100%;
      justify-content: space-between;
    }
  }
}
