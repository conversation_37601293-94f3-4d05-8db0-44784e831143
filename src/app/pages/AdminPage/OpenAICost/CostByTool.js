import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Card } from "antd";
import { useLocation, useNavigate } from "react-router-dom";

import Loading from "@component/Loading";
import TableAdmin from "@src/app/component/TableAdmin";

import { convertObjectToQuery, cloneObj } from "@common/functionCommons";

function CostByTool({ queryParams, activeTab, costByToolData, isLoading }) {
  const { i18n, t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  // Handle change sort table
  const handleChangeTable = async (pagination, filters, sorter, extra) => {
    if (extra.action === "sort") {
      const newQuerParams = cloneObj(queryParams);
      if (sorter.order) {
        newQuerParams.sort = `${sorter.order === "ascend" ? "" : "-"}${sorter?.columnKey}`;
      } else {
        delete newQuerParams.sort;
      }
      navigate(`?${convertObjectToQuery(newQuerParams)}`, { replace: true });
    }
  };

  // Get sort value for column
  const getColumnSortOrder = (column) => {
    const { sort } = queryParams;
    const isDescending = sort?.startsWith("-");
    const sortedColumn = isDescending ? sort?.replace("-", "") : sort;

    return sortedColumn === column ? (isDescending ? "descend" : "ascend") : null;
  };

  const formatCurrency = useMemo(() => {
    return (value) => {
      const US = new Intl.NumberFormat("en-US", {
        currency: "USD",
        maximumFractionDigits: 6,
      });
      return US.format(value || 0);
    };
  }, []);

  const columns = useMemo(() => [
    {
      title: t("ORDER"),
      dataIndex: "order",
      align: "center",
      render: (value, record, index) => index + 1,
      width: 80,
    },
    {
      title: t("TOOL"),
      dataIndex: "name",
      key: "name",
      width: 250,
      render: (value) => value[i18n.language] || value?.en || value,
      sorter: true,
      sortOrder: getColumnSortOrder("name"),
    },
    {
      title: t("NUMBER_OF_SUBMITS"),
      dataIndex: "numberOfSubmissions",
      key: "numberOfSubmissions",
      align: "center",
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("numberOfSubmissions"),
    },
    {
      title: t("TOTAL_TOKENS"),
      dataIndex: "totalTokens",
      key: "totalTokens",
      align: "center",
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("totalTokens"),
    },
    {
      title: t("OUTPUT_AVERAGE_TOKENS"),
      dataIndex: "averageOutputTokens",
      key: "averageOutputTokens",
      align: "center",
      width: 180,
      sorter: true,
      sortOrder: getColumnSortOrder("averageOutputTokens"),
    },
    {
      title: t("INPUT_AVERAGE_TOKENS"),
      dataIndex: "averageInputTokens",
      key: "averageInputTokens",
      align: "center",
      width: 180,
      sorter: true,
      sortOrder: getColumnSortOrder("averageInputTokens"),
    },
    {
      title: t("TOTAL_COST"),
      dataIndex: "totalCost",
      key: "totalCost",
      align: "center",
      render: (value) => <span className="cost-value">${formatCurrency(value)}</span>,
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("totalCost"),
    },
    {
      title: t("AVERAGE_COST"),
      dataIndex: "averageCost",
      key: "averageCost",
      align: "center",
      render: (value) => <span className="cost-value">${formatCurrency(value)}</span>,
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("averageCost"),
    },
  ], [t, i18n.language, formatCurrency, queryParams]);

  if (activeTab !== "2") return null;

  return (
    <Card className="cost-by-tool-table-card">
      <Loading active={isLoading} transparent>
        <TableAdmin
          columns={columns}
          dataSource={costByToolData}
          className="cost-by-tool-table"
          showSorterTooltip={false}
          scroll={{ x: 1200 }}
          rowKey={(record, index) => `tool-${index}`}
          pagination={{
            pageSizeOptions: ["10", "20", "50"],
            showSizeChanger: true,
            defaultPageSize: 10,
          }}
          onChange={handleChangeTable}
          rowClassName={() => "cost-by-tool-table-row"}
          locale={{ emptyText: t("NO_COST_DATA_FOUND") }}
        />
      </Loading>
    </Card>
  );
}

export default CostByTool;
