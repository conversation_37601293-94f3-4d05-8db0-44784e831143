import React, { useMemo } from "react";
import { Card } from "antd";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";

import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { convertObjectToQuery, cloneObj } from "@common/functionCommons";

function CostByInstructions({ activeTab, queryParams, costByInstructionsData, isLoading }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  // Handle change sort table
  const handleChangeTable = async (pagination, filters, sorter, extra) => {
    if (extra.action === "sort") {
      const newQuerParams = cloneObj(queryParams);
      if (sorter.order) {
        newQuerParams.sort = `${sorter.order === "ascend" ? "" : "-"}${sorter?.columnKey}`;
      } else {
        delete newQuerParams.sort;
      }
      navigate(`?${convertObjectToQuery(newQuerParams)}`, { replace: true });
    }
  };

  // Get sort value for column
  const getColumnSortOrder = (column) => {
    const { sort } = queryParams;
    const isDescending = sort?.startsWith("-");
    const sortedColumn = isDescending ? sort?.replace("-", "") : sort;

    return sortedColumn === column ? (isDescending ? "descend" : "ascend") : null;
  };

  const formatCurrency = useMemo(() => {
    return (value) => {
      const US = new Intl.NumberFormat("en-US", {
        currency: "USD",
        maximumFractionDigits: 6,
      });
      return US.format(value || 0);
    };
  }, []);

  const columns = useMemo(() => [
    {
      title: t("ORDER"),
      dataIndex: "order",
      align: "center",
      render: (value, record, index) => index + 1,
      width: 80,
    },
    {
      title: t("INSTRUCTION"),
      dataIndex: "shortName",
      key: "shortName",
      width: 250,
      sorter: true,
      sortOrder: getColumnSortOrder("shortName"),
    },
    {
      title: t("GPT_MODEL"),
      dataIndex: "gptModel",
      key: "gptModel",
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("gptModel"),
    },
    {
      title: t("INPUT_TOKEN"),
      dataIndex: "promptTokens",
      key: "promptTokens",
      align: "center",
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("promptTokens"),
    },
    {
      title: t("OUTPUT_TOKEN"),
      dataIndex: "completionTokens",
      key: "completionTokens",
      align: "center",
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("completionTokens"),
    },
    {
      title: t("TOTAL_TOKEN"),
      dataIndex: "totalTokens",
      key: "totalTokens",
      align: "center",
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("totalTokens"),
    },
    {
      title: t("COST"),
      dataIndex: "totalCost",
      key: "totalCost",
      align: "center",
      render: (value) => <span className="cost-value">${formatCurrency(value)}</span>,
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("totalCost"),
    },
  ], [t, formatCurrency, queryParams]);

  if (activeTab !== "3") return null;

  return (
    <Card className="cost-by-instructions-table-card">
      <Loading active={isLoading} transparent>
        <TableAdmin
          columns={columns}
          dataSource={costByInstructionsData}
          className="cost-by-instructions-table"
          showSorterTooltip={false}
          scroll={{ x: 1200 }}
          rowKey={(record, index) => `instruction-${index}`}
          pagination={{
            pageSizeOptions: ["10", "20", "50"],
            showSizeChanger: true,
            defaultPageSize: 10,
          }}
          onChange={handleChangeTable}
          rowClassName={() => "cost-by-instructions-table-row"}
          locale={{ emptyText: t("NO_COST_DATA_FOUND") }}
        />
      </Loading>
    </Card>
  );
}

export default CostByInstructions;
