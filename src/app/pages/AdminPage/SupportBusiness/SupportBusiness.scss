.support-business-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .support-business-info-card,
  .support-business-search-card,
  .support-business-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .support-business-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .support-business-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .support-business-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }
  }

  // Search form styles
  .form-filter {
    width: 100%;
  }

  .search-form-item {
    margin-bottom: 16px;
  }

  .search-buttons-row {
    margin-top: 8px;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .ant-form-item {
    margin: 0;
  }

  // Table styles
  .support-business-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .support-business-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .company-name-value {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 14px;
    }

    .support-business-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 8px;

      .support-active {
        .ant-checkbox-inner {
          background-color: var(--primary-colours-blue);
          border-color: var(--primary-colours-blue);
        }
      }

      .ant-checkbox-wrapper:hover .ant-checkbox-inner,
      .ant-checkbox:hover .ant-checkbox-inner {
        border-color: var(--primary-colours-blue);
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .support-business-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .search-buttons {
      margin-top: 16px;
      width: 100%;
      justify-content: space-between;
    }
  }
}