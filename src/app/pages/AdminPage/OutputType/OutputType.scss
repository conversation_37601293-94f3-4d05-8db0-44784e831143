@import "src/app/styles/scroll";

.output-type {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .output-type-info-card,
  .output-type-search-card,
  .output-type-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .output-type-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .output-type-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .output-type-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-output-type {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .form-filter {
    width: 100%;
  }

  .output-type-name-value {
    font-weight: 600;
    color: var(--typo-colours-primary-black);
    font-size: 14px;
  }

  // Format tag styles
  .ant-tag {
    padding: 2px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
  }

  .output-type-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .row-selected {
      .ant-table-cell {
        background-color: var(--primary-colours-blue-navy-light-2);
      }
    }

    .ant-table-cell {
      height: 53px !important;

      &:has(.output-type-content) {
        background-color: unset !important;
        padding: 0 !important;
      }

      &:not(:has(.output-type-content)) {
        cursor: pointer;
      }
    }

    .output-type-content-title {
      display: flex;
      gap: 24px;
      justify-content: space-between;

      .output-type-content-action {
        display: flex;
        gap: 8px;
        justify-content: center;
      }
    }

    .output-type-content {
      @extend .scrollbar;
      @extend .scrollbar-show;

      white-space: pre-line;
      padding: 16px 24px;
      overflow-y: auto;

      @for $i from 1 through 50 {
        &.rows-height-#{$i} {
          height: if($i > 10, #{$i*53}px, 530px);
        }
      }
    }

    &:has(.output-type-content:hover) {
      .ant-table-row {
        &:not(&.row-selected) {
          .ant-table-cell {
            background-color: unset !important;
          }
        }
      }
    }
  }

  .ant-pagination-total-text {
    height: 40px !important;
    display: flex;
    align-items: center;
    margin-inline-end: 16px !important;
  }

  .ant-pagination-options {
    .ant-select {
      height: 40px;
    }
  }

  .ant-pagination-item-link {
    background-color: var(--background-light-background-2) !important;
    border: var(--background-light-background-grey) !important;
    border-radius: 8px !important;
    color: var(--typo-colours-support-blue-light) !important;
    width: 40px !important;
    height: 40px !important;
  }

  .ant-pagination-item {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: var(--background-light-background-2) !important;
    border: var(--background-light-background-grey) !important;
    border-radius: 8px !important;
    width: 40px !important;
    height: 40px !important;

    a {
      color: var(--typo-colours-support-blue-light) !important;
    }
  }

  .ant-pagination-item-active {
    background-color: var(--primary-colours-blue) !important;

    a {
      color: var(--background-light-background-2) !important;
    }
  }

  .ant-form-item {
    margin: 0;
  }

  // Remove this to allow default border for inputs
  // .ant-input-outlined {
  //   border-width: 0 !important;
  // }

  .ant-input-outlined[disabled] {
    color: #1c1a1a !important;
    background-color: white !important;
    cursor: default !important;
  }
  // Responsive styles
  @media (max-width: 768px) {
    .output-type-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-output-type {
        width: 100%;
        justify-content: center;
      }
    }

    .form-filter {
      .search-buttons {
        margin-top: 16px;
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}