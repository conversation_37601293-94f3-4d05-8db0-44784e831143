import React, { useEffect } from "react";
import { Form, Input, Select } from "antd";
import { useTranslation } from "react-i18next";

import { stringSplit } from "@common/functionCommons";
import AntModal from "@src/app/component/AntModal";

import "./OutputTypeModal.scss";
import { parseJsonToText, parseTextToJson } from "@common/dataConverter";
import { RESPONSE_FORMAT } from "@constant";


const OutputTypeModal = ({ ...props }) => {
  const { visible, outputType, onCancel, onFinish } = props;
  const [form] = Form.useForm();
  const { t } = useTranslation();
  
  useEffect(() => {
    if (visible) {
      if (outputType) {
        const { schemaInstruction } = outputType;
        const toText = parseJsonToText(schemaInstruction);
        form.setFieldsValue({ ...outputType, schemaInstruction: toText });
      } else {
        form.resetFields();
      }
    }
  }, [visible, outputType]);
  
  return (
    <AntModal
      open={visible}
      onCancel={() => onCancel()}
      title={outputType ? t("OUTPUT_TYPE_DETAIL") : t("CREATE_OUTPUT_TYPE")}
      width={1000}
      className="knowledge-modal"
      onOk={onFinish}
      okText={outputType ? t("UPDATE") : t("CREATE")}
      formId="knowledge-form"
    >
      <Form onFinish={onFinish} layout="vertical" form={form} id="knowledge-form" size={"large"}>
        <Form.Item label="Name" name="name" rules={[{ required: true, message: "Name can't be blank!" }]}>
          <Input/>
        </Form.Item>
        <Form.Item label="Code" name="code" rules={[{ required: true, message: "Code can't be blank!" }]}>
          <Input/>
        </Form.Item>
        <Form.Item label="Response format" name="responseFormat"
                   rules={[{ required: true, message: "Name can't be blank!" }]}>
          <Select options={RESPONSE_FORMAT} allowClear placeholder={"Select response format"}/>
        </Form.Item>
        <Form.Item
          name="schemaInstruction"
          label="Schema"
          rules={[
            () => ({
              validator(_, value) {
                if (value) {
                  const toJson = parseTextToJson(value);
                  if (!toJson) {
                    return Promise.reject(new Error("Please enter valid JSON!"));
                  }
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input.TextArea autoSize={{ minRows: 1 }} placeholder={"Select schema instruction"}/>
        </Form.Item>
      </Form>
    </AntModal>
  );
};

export default OutputTypeModal;
