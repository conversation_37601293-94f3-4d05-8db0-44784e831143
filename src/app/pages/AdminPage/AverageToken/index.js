import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { Card, Col, Form, Row, Statistic, Table } from "antd";
import { useTranslation } from "react-i18next";

import { handleSearchParams } from "@common/functionCommons";
import { getTokenToolByUser } from "@services/Dashboard";
import Loading from "@component/Loading";


import "./AverageToken.scss";
import { DollarOutlined } from "@ant-design/icons";


AverageToken.propTypes = {};

function AverageToken({ user }) {
  const { i18n } = useTranslation();
  const [averageTokenData, setAverageTokenData] = useState([]);

  const [formFilter] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    getTokenDataFromAPI();
  }, [location.search]);
  
  
  const handlePaginationChange = (page) => {
    setLimit(page.pageSize);
    setPage(page.current);
  };
  
  const getTokenDataFromAPI = async () => {
    setIsLoading(true);
    const { query } = handleSearchParams(location.search);
    const apiResponse = await getTokenToolByUser(query);
    if (apiResponse) {
      setAverageTokenData(apiResponse);
    }
    formFilter.setFieldsValue({
      userId: query?.userId || user?._id,
      fullName: query?.userId || user?._id,
    });
    setIsLoading(false);
  };
  
  
  function formatCurrency(currentcy) {
    const US = new Intl.NumberFormat("en-US", {
      currency: "USD",
      maximumFractionDigits: 6,
    });
    return US.format(currentcy);
  }
  
  const columns = [
    {
      title: "Order",
      dataIndex: "order",
      align: "center",
      render: (value, record, index) => (page - 1) * limit + (index + 1),
      width: 100,
    },
    {
      title: "Tool",
      dataIndex: "name",
      width: 400,
      render: (value) => value[i18n.language] || value?.en || value,
    },
    {
      title: "Number of submit",
      dataIndex: "numberOfSubmissions",
      align: "center",
      width: 100,
    },
    {
      title: "Total token",
      dataIndex: "totalTokens",
      align: "center",
      width: 150,
    },
    {
      title: "Output average token",
      dataIndex: "averageOutputTokens",
      align: "center",
      width: 150,
    },
    {
      title: "Input average token",
      dataIndex: "averageInputTokens",
      align: "center",
      width: 150,
    },
    
    {
      title: "Total cost",
      dataIndex: "totalCost",
      align: "center",
      render: (value) => <>${formatCurrency(value)}</>,
      width: 150,
    },
    {
      title: "Average cost",
      dataIndex: "averageCost",
      align: "center",
      render: (value) => <>${formatCurrency(value)}</>,
      width: 150,
    },
  ];
  
  return (
    <div className="average-token-container">
      <span className="average-token-title">Statistics token by tool</span>
      
      {isLoading && <Loading active transparent/>}
      
      {!!averageTokenData.length && !isLoading && (
        <Table
          columns={columns}
          dataSource={averageTokenData}
          scroll={{ x: 1000 }}
          pagination={{
            current: page,
            pageSize: limit,
            pageSizeOptions: ["1", "10", "20", "50"],
            showSizeChanger: true,
          }}
          onChange={handlePaginationChange}
        />
      )}
    </div>
  );
}

function mapStateToProps(state) {
  return {
    user: state.auth.user,
  };
}

export default connect(mapStateToProps)(AverageToken);
