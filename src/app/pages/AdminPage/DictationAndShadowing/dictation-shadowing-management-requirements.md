# YÊU CẦU CHỨC NĂNG QUẢN LÝ DICTATION & SHADOWING

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

### 1.1 <PERSON><PERSON><PERSON> đích
Tài liệu này mô tả chi tiết yêu cầu cho chức năng quản lý bài tập Dictation và Shadowing trong phần Admin của ứng dụng. Chức năng này cho phép quản trị viên tạo mới, chỉnh sửa, xóa và quản lý các bài tập Dictation và Shadowing.

### 1.2 Phạm vi
Chức năng quản lý Dictation và Shadowing bao gồm:
- Hiển thị danh sách bài tập
- Lọc và tìm kiếm bài tập
- Tạo mới bài tập
- Chỉnh sửa bài tập
- <PERSON><PERSON><PERSON> bài tập
- Qu<PERSON>n lý trạng thái bài tập

## 2. <PERSON><PERSON><PERSON> c<PERSON><PERSON> chức năng

### 2.1 Trang danh sách bài tập

#### 2.1.1 <PERSON><PERSON><PERSON> thị danh sách
- <PERSON><PERSON>n thị danh sách bài tập dưới dạng bảng với các cột:
  - STT
  - Tên bài tập
  - Độ khó (A1-C2)
  - Loại bài tập (Dictation, Shadowing, hoặc Dictation & Shadowing)
  - Tag
  - Giới hạn thời gian
  - Trạng thái (Draft, Published, Hidden)
  - Người tạo
  - Thao tác (Sửa, Xóa)

#### 2.1.2 Lọc và tìm kiếm
- Cho phép lọc theo các tiêu chí:
  - Tên bài tập
  - Độ khó
  - Loại bài tập
  - Trạng thái
- Có nút "Tìm kiếm" và "Xóa bộ lọc"

#### 2.1.3 Phân trang
- Hiển thị phân trang với các tùy chọn số lượng bài tập trên mỗi trang
- Hiển thị tổng số bài tập và số trang

#### 2.1.4 Thao tác
- Nút "Tạo bài tập mới" ở đầu trang
- Nút "Sửa" và "Xóa" cho mỗi bài tập trong danh sách

### 2.2 Trang tạo/chỉnh sửa bài tập

#### 2.2.1 Thông tin cơ bản
- Tên bài tập (bắt buộc)
- Độ khó (A1-C2)
- Loại bài tập (Dictation, Shadowing, hoặc Dictation & Shadowing)
- Tag
- Giới hạn thời gian
- Trạng thái (Draft, Published, Hidden)

#### 2.2.2 Quản lý audio
- Upload file audio
- Tạo audio từ text (Text-to-Speech)
- Hiển thị audio player để nghe thử
- Hiển thị transcript của audio

#### 2.2.3 Quản lý segments
- Hiển thị danh sách segments được tạo từ transcript
- Cho phép chỉnh sửa text của từng segment
- Cho phép chọn từ để ẩn trong chế độ Dictation

#### 2.2.4 Lưu và quay lại
- Nút "Lưu" để lưu bài tập
- Nút "Quay lại" để trở về trang danh sách

## 3. Yêu cầu phi chức năng

### 3.1 Hiệu suất
- Trang danh sách phải tải trong vòng 3 giây
- Thao tác lọc và tìm kiếm phải hoàn thành trong vòng 2 giây
- Upload audio phải hỗ trợ file lên đến 20MB

### 3.2 Giao diện người dùng
- Giao diện phải nhất quán với các trang Admin khác
- Responsive trên các thiết bị desktop
- Hiển thị thông báo thành công/lỗi cho các thao tác

### 3.3 Bảo mật
- Chỉ quản trị viên mới có quyền truy cập trang quản lý
- Xác thực người dùng trước khi thực hiện các thao tác

## 4. Luồng làm việc

### 4.1 Luồng tạo bài tập mới
1. Quản trị viên nhấn nút "Tạo bài tập mới"
2. Nhập thông tin cơ bản của bài tập
3. Upload audio hoặc tạo audio từ text
4. Chỉnh sửa transcript nếu cần
5. Cập nhật segments và chọn từ để ẩn
6. Nhấn "Lưu" để hoàn tất

### 4.2 Luồng chỉnh sửa bài tập
1. Quản trị viên nhấn nút "Sửa" trên bài tập cần chỉnh sửa
2. Chỉnh sửa thông tin cần thiết
3. Nhấn "Lưu" để hoàn tất

### 4.3 Luồng xóa bài tập
1. Quản trị viên nhấn nút "Xóa" trên bài tập cần xóa
2. Hệ thống hiển thị hộp thoại xác nhận
3. Quản trị viên xác nhận xóa
4. Hệ thống xóa bài tập và hiển thị thông báo thành công

## 5. Mô hình dữ liệu

### 5.1 Bài tập (Exercise)
- _id: String (ID của bài tập)
- name: String (Tên bài tập)
- difficulty: String (Độ khó: A1, A2, B1, B2, C1, C2)
- type: String (Loại bài tập: dictation, shadowing, dictation_shadowing)
- tag: String (Tag của bài tập)
- timeLimit: Number (Giới hạn thời gian tính bằng giây)
- status: String (Trạng thái: draft, published, hidden)
- audioId: String (ID của file audio)
- transcript: String (Transcript của audio)
- segments: Array (Danh sách các segment)
  - _id: String (ID của segment)
  - text: String (Text của segment)
  - hiddenWord: String (Từ bị ẩn trong chế độ Dictation)
  - startTime: Number (Thời gian bắt đầu của segment)
  - endTime: Number (Thời gian kết thúc của segment)
- createdBy: Object (Thông tin người tạo)
  - _id: String (ID của người tạo)
  - fullName: String (Tên đầy đủ của người tạo)
- createdAt: Date (Thời gian tạo)
- updatedAt: Date (Thời gian cập nhật gần nhất)

## 6. Giao diện người dùng

### 6.1 Trang danh sách bài tập
- Header với tiêu đề "Quản lý Dictation & Shadowing"
- Nút "Tạo bài tập mới" ở góc phải
- Form lọc với các trường:
  - Tên bài tập
  - Độ khó
  - Loại bài tập
  - Trạng thái
- Bảng danh sách bài tập
- Phân trang ở dưới bảng

### 6.2 Trang tạo/chỉnh sửa bài tập
- Header với tiêu đề "Tạo bài tập mới" hoặc "Chỉnh sửa bài tập"
- Form với các trường thông tin cơ bản
- Khu vực upload audio
- Audio player
- Khu vực hiển thị và chỉnh sửa transcript
- Khu vực hiển thị và chỉnh sửa segments
- Nút "Lưu" và "Quay lại" ở cuối trang

## 7. Hạn chế và giới hạn
- Chỉ hỗ trợ file audio định dạng mp3, wav, ogg
- Kích thước file audio tối đa 20MB
- Độ dài transcript tối đa 5000 ký tự
- Số lượng segments tối đa 100

## 8. Yêu cầu tương lai
- Hỗ trợ tạo bài tập từ template
- Thêm chức năng preview bài tập
- Thêm chức năng phân tích dữ liệu về hiệu suất của học viên
- Hỗ trợ nhiều ngôn ngữ cho giao diện quản lý
