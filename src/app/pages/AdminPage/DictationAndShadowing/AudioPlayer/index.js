import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { toast } from "@component/ToastProvider";
import Loading from "@component/Loading";

import { API } from "@api";
import { convertSecondToHHMMSS } from "@common/functionCommons";
import { checkResourceExist } from "@services/Resource";

import PAUSE_WHITE from "@src/asset/icon/pause/pause-white.svg";
import PLAY_WHITE from "@src/asset/icon/play/play-white.svg";
import VOLUME_OFF from "@src/asset/icon/volume/volume-x.svg";
import VOLUME_ON from "@src/asset/icon/volume/volume-on.svg";

import "./AudioPlayer.scss";

const AudioPlayer = ({ exerciseData, audioDuration, ...props }) => {
  const { t } = useTranslation();
  
  const jsAudio = useRef(undefined);
  const jsProgressBar = useRef(undefined);
  
  const [isLoading, setLoading] = useState(false);
  const [audioSrc, setAudioSrc] = useState(undefined);
  const [isPlaying, setPlaying] = useState(false);
  const [isMute, setMute] = useState(false);
  
  useEffect(() => {
    if (!jsProgressBar.current) return;
    jsProgressBar.current.style.setProperty("--min", 0);
    jsProgressBar.current.style.setProperty("--thumb-width", audioSrc ? "1px" : "0");
    jsProgressBar.current.style.setProperty("--max", audioDuration);
    jsProgressBar.current.min = 0;
    jsProgressBar.current.max = audioDuration;
    jsProgressBar.current.value = 0;
    
  }, [audioDuration]);
  
  useLayoutEffect(() => {
    function timeUpdate(ev) {
      const currentTime = ev.target.currentTime;
      if (currentTime === 0) {
        jsProgressBar.current.value = 0;
        jsProgressBar.current.style.setProperty("--thumb-width", "1px");
      } else {
        jsProgressBar.current.style.setProperty("--thumb-width", "0");
      }
      
      jsProgressBar.current.style.setProperty("--value", currentTime);
      
      if (currentTime === ev.target.duration) {
        setPlaying(false);
      }
    }
    
    function getElement() {
      jsAudio.current = document.getElementById(`js-resource-audio-${exerciseData?.audioId}`);
      jsProgressBar.current = document.getElementById(`js-progress-bar-${exerciseData?.audioId}`);
      
      
      jsAudio.current.addEventListener("timeupdate", timeUpdate);
      return () => jsAudio.current.removeEventListener("timeupdate", timeUpdate);
    }
    
    if (document.readyState === "complete") {
      getElement();
    } else {
      window.addEventListener("load", getElement);
      return () => document.removeEventListener("load", getElement);
    }
  }, []);
  
  
  useEffect(() => {
    function checkFinished() {
      if (jsAudio.current) {
        jsAudio.current.currentTime = 0;
      }
    }
    
    const jsAudioElement = document.getElementById(`js-resource-audio-${exerciseData?.audioId}`);
    jsAudioElement.addEventListener("ended", checkFinished, false);
    return () => jsAudioElement.removeEventListener("ended", checkFinished);
  }, []);
  
  
  useEffect(() => {
    if (!jsAudio.current) return;
    if (isPlaying) {
      jsAudio.current.play();
    } else {
      jsAudio.current.pause();
    }
  }, [isPlaying]);
  
  const handlePlayOrPauseAudio = async () => {
    if (audioSrc) {
      setPlaying((prevState) => !prevState);
    } else {
      setLoading(true);
      if (exerciseData?.audioId) {
        setAudioSrc(API.STREAM_MEDIA.format(exerciseData.audioId));
        setPlaying((prevState) => !prevState);
      }
      setLoading(false);
    }
  };
  
  const toggleMute = () => {
    setMute((pre) => !pre);
  };
  
  const onChangeProgressBar = (e) => {
    jsAudio.current.currentTime = e.target.value;
  };
  
  const formatTimeDuration = (time) => {
    if (time && !isNaN(time)) {
      return convertSecondToHHMMSS(time);
    }
    return "00:00:00";
  };
  
  return <div className="resource-audio-player" onClick={e => e.stopPropagation()}>
    <div className="audio-player">
      <audio id={`js-resource-audio-${exerciseData?.audioId}`} src={audioSrc} muted={isMute}/>
      <Loading active={isLoading}>
        <div className="audio-player__controls audio-player__controls-small">
          <button type="button" className="play-button" onClick={handlePlayOrPauseAudio}>
            <img src={isPlaying ? PAUSE_WHITE : PLAY_WHITE} alt=""/>
          </button>
          <div className="progress-bar-container">
            <input
              id={`js-progress-bar-${exerciseData?.audioId}`}
              type="range"
              step="any"
              className="progress-bar"
              defaultValue="0"
              onChange={onChangeProgressBar}
              disabled={!audioSrc}
            />
            <div className="duration">{formatTimeDuration(audioDuration)}</div>
            <img
              className="volume-button"
              src={isMute ? VOLUME_OFF : VOLUME_ON}
              onClick={toggleMute}
              alt=""
            />
          </div>
        </div>
      </Loading>
    </div>
  </div>;
};

export default AudioPlayer;