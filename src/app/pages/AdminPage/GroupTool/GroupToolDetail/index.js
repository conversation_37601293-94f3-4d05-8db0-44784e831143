import React, { useEffect, useState } from "react";
import { AntForm } from "@component/AntForm";
import { Col, Divider, Form, Input, Row, Select } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import RULE from "@rule";

import "./GroupToolDetail.scss";
import AntButton from "@component/AntButton";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { BUTTON, LANG_OPTIONS } from "@constant";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import { cloneObj } from "@common/functionCommons";
import clsx from "clsx";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import { LINK } from "@link";
import { toast } from "@component/ToastProvider";
import { createGroupTool, editGroupTool, getGroupTool, getGroupToolById } from "@services/Tool";

GroupToolDetail.propTypes = {};

function GroupToolDetail(props) {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const [formToolGroup] = Form.useForm();
  const [groupNameLanguage, setGroupNameLanguage] = useState([]);
  
  useEffect(() => {
    getGroupToolDetail();
  }, [id]);
  
  const getGroupToolDetail = async () => {
    if (id) {
      const apiResponse = await getGroupToolById(id);
      if (apiResponse) {
        const localization = apiResponse?.localization ? convertObjectToArray(apiResponse.localization) : [{"lang": null}];
        formToolGroup.setFieldsValue({ ...apiResponse, localization });
      }
    }
  };
  
  const onSelect = (data, index, setData) => {
    const { value } = data;
    setData((pre) => {
      const newData = cloneObj(pre);
      newData[index] = value;
      return newData;
    });
  };
  
  const onRemove = (index, name, remove, setData) => {
    remove(index, name);
    setData((pre) => {
      const newData = cloneObj(pre);
      newData.splice(index, 1);
      return newData;
    });
  };
  //
  
  const formToolGroupSubmit = async (valuesForm) => {
    let dataRequest = cloneObj(valuesForm);
    const localization = await convertArrayToObject(dataRequest.localization);
    if (id) {
      const response = await editGroupTool({ _id: id, ...dataRequest, localization });
      if (response) {
        toast.success("UPDATE_GROUP_TOOL_SUCCESS");
      }
    } else {
      const response = await createGroupTool({ ...dataRequest, localization });
      if (response) {
        toast.success("GROUP_TOOL_CREATE_SUCCESS");
        navigate(LINK.ADMIN_PAGE + LINK.DETAIL_GROUP_TOOL.format(response?._id));
      }
    }
  };
  const getAvailabeOptions = (index, options) => {
    let optionsSelected = [];
    options?.forEach((lang, optionIndex) => {
      if (optionIndex !== index) {
        optionsSelected.push(lang);
      }
    });
    const availableOptions = LANG_OPTIONS.filter((option) => !optionsSelected.includes(option.value));
    return availableOptions;
  };
  
  const convertObjectToArray = (inputObject) => {
    let array = [];
    let languageAvalible = [];
    for (let key in inputObject?.groupName) {
      array.push({
        description: { [key]: inputObject.description[key] },
        groupName: { [key]: inputObject.groupName[key] },
        lang: key,
      });
      languageAvalible.push(key);
    }
    setGroupNameLanguage(languageAvalible);
    
    return array;
  };
  const convertArrayToObject = (inputObject) => {
    let transformedObject = {};
    inputObject.map((res) => {
      transformedObject["description"] = { ...transformedObject["description"], ...res.description };
      transformedObject["groupName"] = { ...transformedObject["groupName"], ...res.groupName };
    });
    return transformedObject;
  };
  const goBack = () => {
    navigate(LINK.ADMIN_GROUP_TOOL);
  };
  
  return (
    <div className={"group-tool-detail-container"}>
      <div className={"group-tool-detail-header"}>
        <span>{id ? t("GROUP_TOOL_DETAILS") : t("CREATE_GROUP_TOOL")}</span>
      </div>
      <AntForm
        form={formToolGroup}
        layout="vertical"
        size={"large"}
        requiredMark={true}
        onFinish={formToolGroupSubmit}
        className={"form-tool-group"}
      >
        <AntForm.Item label={t("GROUP_NAME")} name="groupName" rules={[RULE.REQUIRED]}>
          <Input placeholder={"Enter group name"}/>
        </AntForm.Item>
        <AntForm.Item label={t("DESCRIPTION")} name="description" rules={[RULE.REQUIRED]}>
          <Input placeholder={"Enter description"}/>
        </AntForm.Item>
        <AntForm.Item label={t("CODE")} name="code" rules={[RULE.REQUIRED]}>
          <Input placeholder={"Enter code"}/>
        </AntForm.Item>
        
        <AntForm.Item required>
          <Form.List name="localization" initialValue={[{ "lang": null }]}>
            {(fields, { add, remove }) => (
              <>
                <div className={"group-tool-detail"}>
                  {fields.map(({ key, name, ...restField }, index) => (
                    <div className={"group-tool-detail-item"} key={key}>
                      <Row gutter={24} className="items-baseline">
                        <Col xs={23} lg={6}>
                          <Form.Item
                            {...restField}
                            name={[name, "lang"]}
                            label={index == 0 && t("LANGUAGE")}
                            rules={[{ required: true, message: "Missing language!" }]}
                          >
                            <Select
                              options={getAvailabeOptions(index, groupNameLanguage)}
                              placeholder="Select language"
                              onSelect={(_, option) => onSelect(option, index, setGroupNameLanguage)}
                            />
                          </Form.Item>
                        </Col>
                        <Col xs={23} lg={9}>
                          <Form.Item
                            {...restField}
                            label={index == 0 && t("GROUP_NAME")}
                            name={[name, "groupName", groupNameLanguage[index]]}
                            rules={[{ required: true, message: "Missing group name!" }]}
                          >
                            <Input placeholder="Enter group name"/>
                          </Form.Item>
                        </Col>
                        <Col xs={23} lg={9}>
                          <Form.Item
                            {...restField}
                            name={[name, "description", groupNameLanguage[index]]}
                            label={index == 0 && t("DESCRIPTION")}
                            rules={[{ required: true, message: "Missing name!" }]}
                          >
                            <Input placeholder="Enter description"/>
                          </Form.Item>
                        </Col>
                      </Row>
                      <div className={"group-tool-detail-item-action"}>
                        <AntButton
                          type={BUTTON.WHITE}
                          shape={"circle"}
                          className={clsx("btn-cancel-add-group-detail", { "first-actions": !index })}
                          icon={<CancelIcon/>}
                          size={"small"}
                          onClick={() => onRemove(index, name, remove, setGroupNameLanguage)}
                        />
                      </div>
                    </div>
                  ))}
                  {fields.length < 2 && (
                    <div className={"add-group-tool-actions"}>
                      <AntButton
                        shape={"circle"}
                        size={"large"}
                        type={BUTTON.WHITE_BLUE}
                        onClick={() => add()}
                        icon={<PlusIcon/>}
                      ></AntButton>
                    </div>
                  )}
                </div>
              </>
            )}
          </Form.List>
        </AntForm.Item>
        
        {/* <AntForm.Item label="Group name" name="groupName" rules={[RULE.REQUIRED]}>
         <Input />
         </AntForm.Item> */}
        {/* <AntForm.Item label="Description" name="description" rules={[RULE.REQUIRED]}>
         <Input />
         </AntForm.Item> */}
        <div className={"form-actions-submit"}>
          <AntButton type={BUTTON.WHITE} onClick={goBack}>
            {t("BACK")}
          </AntButton>
          <AntButton type={BUTTON.DEEP_NAVY} htmlType="submit">
            {t("SAVE")}
          </AntButton>
        </div>
      </AntForm>
    </div>
  );
}

export default GroupToolDetail;
