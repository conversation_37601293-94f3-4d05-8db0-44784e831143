.group-tool-detail-container {
  background-color: var(--background-light-background-2);
  padding: 24px;
  gap: 24px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;

  .group-tool-detail-header {
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
  }

  .form-tool-group {
    .ant-form-item-required {
      height: unset !important;
    }

    .btn-cancel-add-group-detail {
      box-shadow: var(--shadow-level-2);
    }

    .group-tool-detail-item-action {
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin-top: 5px;
    }

    .first-actions {
      align-items: center;
      margin-top: 33px;
    }

    .group-tool-detail {
      display: flex;
      flex-direction: column;
    }

    .add-group-tool-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      button {
        box-shadow: var(--shadow-level-2);
        color: var(--primary-colours-blue) !important;
      }
    }

    .form-actions-submit {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 32px;
    }

    .group-tool-detail-item {
      display: flex;
      flex-direction: row;

      gap: 24px;

      .items-baseline {
        width: calc(100% - 16px);
      }
    }
  }
  .ant-form-item-required {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    padding: 0px;
    flex-direction: row-reverse;

    &:after {
      display: none;
    }
  }

}