import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import UserSubscription from "@app/pages/AdminPage/Customer/UserSubscription";
import { Card } from "antd";
import Loading from "@src/app/component/Loading";
import './AdminCustomer.scss';
AdminCustomer.propTypes = {

};

function AdminCustomer(props) {
  const {t} = useTranslation();
  const [isLoading, setIsLoading] = useState(false);

  return (
    <Loading active={isLoading} transparent>
      <div className="customer-container">
        <Card className="customer-info-card">
          <div className="customer-info-header">
            <div>
              <h1 className="customer-title">{t("CUSTOMER_MANAGEMENT")}</h1>
              <p className="customer-description">{t("CUSTOMER_MANAGEMENT_DESCRIPTION")}</p>
            </div>
          </div>
        </Card>

        <UserSubscription setIsLoading={setIsLoading}/>
      </div>
    </Loading>
  );
}

export default AdminCustomer;