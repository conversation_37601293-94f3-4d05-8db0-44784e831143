.feedback-analysis-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .feedback-analysis-info-card,
  .feedback-analysis-filter-card,
  .feedback-analysis-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .feedback-analysis-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .feedback-analysis-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .feedback-analysis-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }
  }

  // Table styles
  .feedback-analysis-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .feedback-analysis-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .feedback-analysis-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
}

.modal-feedback-detail {
  .modal-body {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .content-header {
      display: flex;
      gap: 100px;

      .content-header__item {
        display: flex;
        gap: 6px;
        align-items: center;

        .content-header__item__label{
          font-weight: 600;
        }
      }
    }
  }
}