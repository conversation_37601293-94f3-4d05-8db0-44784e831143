# YÊU CẦU CHỨC NĂNG QUẢN LÝ SPEAKING EXERCISE

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

### 1.1 <PERSON><PERSON><PERSON> đích
Tài liệu này mô tả chi tiết yêu cầu cho chức năng quản lý bài tập Speaking Exercise trong phần Admin của ứng dụng. Chức năng này cho phép quản trị viên tạo mới, chỉnh sửa, xóa và quản lý các bài tập Speaking Exercise theo định dạng IELTS.

### 1.2 Phạm vi
Chức năng quản lý Speaking Exercise bao gồm:
- Hiển thị danh sách bài tập
- Lọc và tìm kiếm bài tập
- Tạo mới bài tập
- Chỉnh sửa bài tập
- Xóa bài tập
- Quản lý trạng thái bài tập
- Tạo audio cho các câu hỏi bằng AI TTS

## 2. <PERSON><PERSON><PERSON> cầu chức năng

### 2.1 <PERSON><PERSON> danh sách bài tập

#### 2.1.1 Hi<PERSON><PERSON> thị danh sách
- Hiển thị danh sách bài tập dưới dạng bảng với các cột:
  - STT
  - Tên bài tập (Title)
  - Trạng thái (Status): Draft, Published, Hidden
  - Thao tác (Action): Sửa, Xóa

#### 2.1.2 Lọc và tìm kiếm
- Cho phép lọc theo các tiêu chí:
  - Tên bài tập (Title)
  - Trạng thái (Status)
- Có nút "Tìm kiếm" và "Xóa bộ lọc"

#### 2.1.3 Phân trang
- Hiển thị phân trang với các tùy chọn số lượng bài tập trên mỗi trang
- Hiển thị tổng số bài tập và số trang

#### 2.1.4 Thao tác
- Nút "Tạo bài tập mới" ở đầu trang
- Nút "Sửa" và "Xóa" cho mỗi bài tập trong danh sách

### 2.2 Trang tạo/chỉnh sửa bài tập

#### 2.2.1 Thông tin cơ bản
- Tên bài tập (Title) - bắt buộc
- Chủ đề (Topic) - bắt buộc
- Trạng thái (Status) - bắt buộc: Draft, Published, Hidden
- Hình ảnh đại diện (Thumbnail)

#### 2.2.2 Quản lý nội dung bài tập
- Part 1: Các câu hỏi phỏng vấn ngắn
  - Thêm câu hỏi mới
  - Tạo audio cho câu hỏi bằng AI TTS
  - Xóa câu hỏi
- Part 2: Cue card và các câu hỏi theo dõi
  - Chỉnh sửa nội dung cue card
  - Tạo audio cho cue card
  - Thêm câu hỏi theo dõi
  - Tạo audio cho câu hỏi
  - Xóa câu hỏi
- Part 3: Các câu hỏi mở rộng
  - Thêm câu hỏi mới
  - Tạo audio cho câu hỏi
  - Xóa câu hỏi

#### 2.2.3 Tạo audio cho câu hỏi
- Chọn giọng đọc (Voice): Alloy, Echo, Fable, Onyx, Nova, Shimmer
- Điều chỉnh tốc độ đọc (Speed): 0.25x - 4.0x
- Nghe thử audio trước khi lưu

#### 2.2.4 Lưu bài tập
- Nút "Save Data" để lưu bài tập

## 3. Yêu cầu phi chức năng

### 3.1 Hiệu suất
- Trang danh sách phải tải trong vòng 3 giây
- Thao tác lọc và tìm kiếm phải hoàn thành trong vòng 2 giây
- Tạo audio bằng AI TTS phải hoàn thành trong vòng 5 giây

### 3.2 Giao diện người dùng
- Giao diện phải nhất quán với các trang Admin khác
- Bố cục rõ ràng, chuyên nghiệp và dễ sử dụng
- Hiển thị thông báo thành công/lỗi cho các thao tác

### 3.3 Bảo mật
- Chỉ quản trị viên mới có quyền truy cập trang quản lý
- Xác thực người dùng trước khi thực hiện các thao tác

## 4. Luồng làm việc

### 4.1 Luồng tạo bài tập mới
1. Quản trị viên nhấn nút "Create Exercise"
2. Nhập thông tin cơ bản của bài tập (tiêu đề, chủ đề, trạng thái)
3. Tải lên hình ảnh đại diện (thumbnail) nếu cần
4. Thêm các câu hỏi cho Part 1
5. Nhập nội dung cue card và thêm các câu hỏi theo dõi cho Part 2
6. Thêm các câu hỏi mở rộng cho Part 3
7. Tạo audio cho các câu hỏi bằng AI TTS nếu cần
8. Nhấn "Save Data" để hoàn tất

### 4.2 Luồng chỉnh sửa bài tập
1. Quản trị viên nhấn nút chỉnh sửa (biểu tượng bút chì) trên bài tập cần chỉnh sửa
2. Chỉnh sửa thông tin cơ bản, hình ảnh đại diện hoặc các câu hỏi
3. Tạo lại audio cho các câu hỏi nếu cần
4. Nhấn "Save Data" để hoàn tất

### 4.3 Luồng xóa bài tập
1. Quản trị viên nhấn nút xóa (biểu tượng thùng rác) trên bài tập cần xóa
2. Hệ thống hiển thị hộp thoại xác nhận
3. Quản trị viên xác nhận xóa
4. Hệ thống xóa bài tập và hiển thị thông báo thành công

### 4.4 Luồng tạo audio cho câu hỏi
1. Quản trị viên nhấn nút tạo audio (biểu tượng loa) bên cạnh câu hỏi
2. Hệ thống hiển thị hộp thoại chọn giọng và tốc độ
3. Quản trị viên chọn giọng và tốc độ mong muốn
4. Nhấn "OK" để tạo audio
5. Hệ thống tạo audio và hiển thị trình phát âm thanh bên cạnh câu hỏi

## 5. Cấu trúc dữ liệu

### 5.1 Bài tập (Exercise)
- _id: String (ID của bài tập)
- title: String (Tên bài tập)
- topic: String (Chủ đề của bài tập)
- status: String (Trạng thái: draft, published, hidden)
- avatarId: String (ID của hình ảnh đại diện)
- parts: Array (Danh sách các phần của bài tập)
  - part: String (Tên phần: part1, part2, part3)
  - questions: Array (Danh sách câu hỏi)
    - _id: String (ID của câu hỏi)
    - text: String (Nội dung câu hỏi)
    - audioId: String (ID của file audio cho câu hỏi)
  - cueCard: Object (Chỉ có ở part2)
    - text: String (Nội dung cue card)
    - audioId: String (ID của file audio cho cue card)
- createdBy: Object (Thông tin người tạo)
  - _id: String (ID của người tạo)
  - fullName: String (Tên đầy đủ của người tạo)
- createdAt: Date (Thời gian tạo)
- updatedAt: Date (Thời gian cập nhật gần nhất)

## 6. Giao diện người dùng

### 6.1 Trang danh sách bài tập
- Header với tiêu đề "Quản lý Speaking Exercise"
- Nút "Tạo bài tập mới" ở góc phải
- Form lọc với các trường:
  - Tên bài tập
  - Độ khó
  - Loại bài tập
  - Trạng thái
- Bảng danh sách bài tập
- Phân trang ở dưới bảng

### 6.2 Trang tạo/chỉnh sửa bài tập
- Header với tiêu đề "Tạo bài tập mới" hoặc "Chỉnh sửa bài tập"
- Phần thông tin cơ bản (Basic Info) với bố cục:
  - Dòng 1: Tiêu đề (Title), Chủ đề (Topic), Trạng thái (Status) theo thứ tự này
  - Dòng 2: Hình ảnh đại diện (Exercise Thumbnail)
- Phần quản lý nội dung bài tập:
  - Speaking Part 1: Các câu hỏi phỏng vấn ngắn
  - Speaking Part 2: Cue card và các câu hỏi theo dõi
  - Speaking Part 3: Các câu hỏi mở rộng
- Mỗi câu hỏi có thể tạo audio bằng AI TTS với các tùy chọn giọng nói và tốc độ
- Nút "Save Data" ở cuối trang

## 7. Hạn chế và giới hạn
- Chỉ hỗ trợ file hình ảnh định dạng jpg, jpeg, png, gif
- Kích thước file hình ảnh tối đa 5MB
- Độ dài text của câu hỏi tối đa 1000 ký tự
- Độ dài text của cue card tối đa 2000 ký tự
- Số lượng câu hỏi tối đa cho mỗi phần: 20

## 8. Yêu cầu tương lai
- Hỗ trợ tạo bài tập từ template
- Thêm chức năng preview bài tập
- Thêm chức năng phân tích dữ liệu về hiệu suất của học viên
- Hỗ trợ nhiều ngôn ngữ cho giao diện quản lý
- Thêm chức năng đánh giá tự động cho bài nói của học viên
- Hỗ trợ nhiều giọng đọc AI khác nhau cho các câu hỏi
- Thêm chức năng tạo bài tập theo chủ đề tự động
