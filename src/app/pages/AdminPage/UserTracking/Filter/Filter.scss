.filter-form-user-tracking {
  width: 100%;

  .grow {
    flex-grow: 1;
  }

  .search-form-item {
    margin-bottom: 16px;
  }

  .filter-form__date-picker {
    width: 100%;
    padding: 9px 11px !important;
  }

  .search-buttons-row {
    margin-top: 8px;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  // Tooltip styles
  .filter-tooltip-icon {
    margin-left: 4px;
    cursor: help;

    &.disabled {
      color: #ff4d4f;
    }

    &.enabled {
      color: rgba(0, 0, 0, 0.45);
    }
  }

  // Switch styles
  .user-status-switch {
    min-width: 70px;

    &.ant-switch-checked {
      //background-color: #1890ff;
    }

    .ant-switch-inner-checked,
    .ant-switch-inner-unchecked {
      font-weight: 500;
    }
  }

  .ant-form-item-label > label {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  // Align switch with other form controls
  .ant-form-item-control-input {
    min-height: 40px;
    display: flex;
    align-items: center;
  }

  // Responsive styles
  @media (max-width: 768px) {
    .search-buttons {
      margin-top: 16px;
      width: 100%;
      justify-content: space-between;
    }
  }
}
