import {useEffect, useState} from "react";
import {Tooltip, Checkbox, Card} from "antd";
import {useTranslation} from "react-i18next";
import {EyeOutlined, DownloadOutlined} from "@ant-design/icons";
import {useNavigate} from "react-router-dom";
import dayjs from "dayjs";

import Loading from "@src/app/component/Loading";
import AntButton from "@src/app/component/AntButton";
import ModalDetail from "./ModalDetail";
import TableAdmin from "@src/app/component/TableAdmin";

import {toast} from "@src/app/component/ToastProvider";
import {downloadTracking, getTracking} from "@services/Tracking";
import {updateDeveloperForUser} from "@services/User";
import {
  formatTimeDate,
  convertObjectToQuery,
  paginationConfig,
  cloneObj,
} from "@common/functionCommons";
import {handlePagingData} from "@src/common/dataConverter";

import {BUTTON} from "@constant";

const AccessFrequency = ({trackingData, setTrackingData, queryParams, isLoading, setIsLoading}) => {
  const {t, i18n} = useTranslation();
  const navigate = useNavigate();

  const [stateModalDetail, setStateModalDetail] = useState({
    open: false,
    data: null,
  });

  //handle change sort table
  const handleChangeTable = async (pagination, filters, sorter, extra) => {
    if (extra.action === "sort") {
      const newQuerParams = cloneObj(queryParams);
      if (sorter.order) {
        newQuerParams.sort = `${sorter.order === "ascend" ? "" : "-"}${sorter?.columnKey}`;
      } else {
        delete newQuerParams.sort;
      }
      navigate(`?${convertObjectToQuery(newQuerParams)}`, {replace: true});
    }
  };

  //get sort value for column
  const getColumnSortOrder = (column) => {
    const {sort} = queryParams;
    const isDescending = sort?.startsWith("-");
    const sortedColumn = isDescending ? sort?.replace("-", "") : sort;

    return sortedColumn === column ? (isDescending ? "descend" : "ascend") : null;
  };

  const onChangeCheckbox = async (record) => {
    setIsLoading(true);
    const updateResponse = await updateDeveloperForUser(record._id);
    if (updateResponse) {
      if (queryParams.isDeveloper) {
        // Gọi lại API để lấy dữ liệu mới từ component cha
        const response = await getTracking(queryParams);
        if (response) setTrackingData(handlePagingData(response.users));
      } else {
        const rows = trackingData.rows.map(item => item._id === record._id ? {
          ...item,
          isDeveloper: !record.isDeveloper,
        } : item);
        setTrackingData({...trackingData, rows});
      }
      toast.success(t("USER_INFORMATION_UPDATED"));
    }
    setIsLoading(false);
  };

  const onToggleModalDetail = (data) => {
    setStateModalDetail((pre) => ({open: !pre.open, data}));
  };

  //handle get pagination value and handle change pagination
  const pagination = paginationConfig(trackingData.paging, queryParams, i18n.language);

  const handleDownloadExcel = async () => {
    try {
      setIsLoading(true);
      const response = await downloadTracking(queryParams);
      if (response) {
        // Convert the response to a blob and download it
        const blob = new Blob([response], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `user-tracking-${dayjs().format('YYYY-MM-DD')}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success(t("DOWNLOAD_COMPLETED"));
      }
      setIsLoading(false);
    } catch (error) {
      console.error(error);
      toast.error(t("DOWNLOAD_FAILED"));
      setIsLoading(false);
    }
  };

  const columns = [
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      key: "fullName",
      width: 350,
      sorter: true,
      sortOrder: getColumnSortOrder("fullName"),
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      key: "email",
      width: 400,
      sorter: true,
      sortOrder: getColumnSortOrder("email"),
    },
    {
      title: t("CREATE_DATE"),
      dataIndex: "createdAt",
      key: "createdAt",
      align: "center",
      render: formatTimeDate,
      width: 250,
      sorter: true,
      sortOrder: getColumnSortOrder("createdAt"),
    },
    {
      title: t("LAST_VISIT"),
      dataIndex: "lastVisit",
      key: "lastVisit",
      align: "center",
      render: formatTimeDate,
      width: 250,
      sorter: true,
      sortOrder: getColumnSortOrder("lastVisit"),
    },
    {
      title: t("NUMBER_OF_VISITS"),
      dataIndex: "visitNumber",
      key: "visitNumber",
      align: "center",
      width: 200,
      sorter: true,
      sortOrder: getColumnSortOrder("visitNumber"),
    },
    {
      title: t("NUMBER_OF_DAYS_VISITED"),
      dataIndex: "visitDaysNumber",
      key: "visitDaysNumber",
      align: "center",
      width: 200,
      sorter: true,
      sortOrder: getColumnSortOrder("visitDaysNumber"),
    },
    {
      title: t("NUMBER_OF_PROJECTS"),
      dataIndex: "numberOfProjects",
      key: "numberOfProjects",
      align: "center",
      width: 200,
      sorter: true,
      sortOrder: getColumnSortOrder("numberOfProjects"),
    },
    {
      title: t("NUMBER_OF_SUBMITS"),
      dataIndex: "numberOfSubmits",
      key: "numberOfSubmits",
      align: "center",
      width: 200,
      sorter: true,
      sortOrder: getColumnSortOrder("numberOfSubmits"),
    },
    {
      title: t("TOTAL_COST"),
      dataIndex: "totalCost",
      render: value => Math.round(value * 1000) / 1000,
      key: "totalCost",
      align: "center",
      width: 200,
      sorter: true,
      sortOrder: getColumnSortOrder("totalCost"),
    },
    {
      title: t("IS_A_DEVELOPER"),
      key: "isDeveloper",
      dataIndex: "isDeveloper",
      align: "center",
      width: 100,
      render: (_, record) => <Checkbox onChange={() => onChangeCheckbox(record)} checked={record.isDeveloper}/>,
    },
    {
      title: t("DETAIL"),
      key: "DETAIL",
      align: "center",
      width: 80,
      render: (_, record) => <Tooltip title={t("VIEW")}>
        <AntButton
          type={BUTTON.LIGHT_NAVY}
          size="small"
          icon={<EyeOutlined/>}
          onClick={() => onToggleModalDetail(record)}
        />
      </Tooltip>,
    },
  ];

  return (<>
      <Card className="user-tracking-table-card">
        <TableAdmin
          columns={columns}
          dataSource={trackingData.rows}
          className={"table-user-subscription"}
          showSorterTooltip={false}
          scroll={{x: 1000}}
          rowKey="_id"
          pagination={pagination}
          onChange={handleChangeTable}
          locale={{ emptyText: t("NO_USER_DATA_FOUND") }}
        />
      </Card>
      <div className="export-button-container">
        <AntButton
          type={BUTTON.DEEP_NAVY}
          icon={<DownloadOutlined/>}
          onClick={handleDownloadExcel}
        >
          {t("EXPORT_EXCEL")}
        </AntButton>
      </div>
      <ModalDetail
        dataSource={stateModalDetail.data}
        open={stateModalDetail.open}
        onCancel={() => onToggleModalDetail(null)}
      />
    </>
  );
};

export default AccessFrequency;
