import { useEffect } from "react";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";

import { stringSplit } from "@common/functionCommons";
import AntModal from "@src/app/component/AntModal";

import "./KnowledgeModal.scss";


const KnowledgeModal = ({ ...props }) => {
  const { visible, knowledge, onCancel, onFinish } = props;
  const [form] = Form.useForm();
  const { t } = useTranslation();

  useEffect(() => {
    if (visible) {
      if(knowledge) {
        form.setFieldsValue(knowledge);
      } else {
        form.resetFields();
      }
    }
  }, [visible, knowledge]);

  return (
    <AntModal
      open={visible}
      onCancel={() => onCancel()}
      title={knowledge ? t("KNOWLEDGE_DETAIL") : t("CREATE_KNOWLEDGE")}
      width={1000}
      className="knowledge-modal"
      onOk={onFinish}
      okText={knowledge ? t("UPDATE") : t("CREATE")}
      formId="knowledge-form"
    >
      <Form onFinish={onFinish} layout="vertical" form={form} id="knowledge-form" size={"large"}>
        <Form.Item label="Name" name="name" rules={[{ required: true, message: "Name can't be blank!" }]}>
          <Input />
        </Form.Item>
        <Form.Item
          label="Content"
          name="content"
          rules={[{ required: true, message: "Content type can't be blank!" }]}
        >
          <Input.TextArea
            count={{ show: true, max: 1000, strategy: txt => stringSplit(txt).length }}
            autoSize={{ minRows: 1 }}
          />
        </Form.Item>
      </Form>
    </AntModal>
  );
};

export default KnowledgeModal;
