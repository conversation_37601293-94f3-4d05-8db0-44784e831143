import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Card, Form, Input, Row, Col, Select, Tag, Tooltip } from "antd";
import { SearchOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, orderColumn, paginationConfig, handleReplaceUrlSearch, formatTimeDate } from "@common/functionCommons";

import { getPaginationExplain, deleteExplain } from "@services/Explain";
import { getGptModel } from "@services/GPTModelPrice";
import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import "./Explain.scss";

// Define explain types and task types based on the documentation
const EXPLAIN_TYPES = [
  { value: "idea", label: "Generate Ideas" },
  { value: "find_vocabulary", label: "Find Vocabulary" },
  { value: "help_me_understand", label: "Help Me Understand" },
  { value: "help_me_write", label: "Help Me Write" },
];

const TASK_TYPES = [
  { value: "task1", label: "Task 1" },
  { value: "task2", label: "Task 2" },
  { value: "general", label: "General" },
];

const Explain = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [explainData, setExplainData] = useState(PAGINATION_INIT);
  const [gptModelData, setGPTModelData] = useState([]);
  const [isLoading, setLoading] = useState(false);

  const [formFilter] = Form.useForm();

  useEffect(() => {
    getGptModelData();
  }, []);

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getExplainData(paging, query);
  }, [location.search]);

  async function getGptModelData() {
    const apiResponse = await getGptModel();
    if (apiResponse) {
      setGPTModelData(apiResponse);
    }
  }

  const getExplainData = async (paging = explainData.paging, query = explainData.query) => {
    setLoading(true);
    const dataResponse = await getPaginationExplain(paging, query);
    if (dataResponse) {
      setExplainData(handlePagingData(dataResponse, query));
    }
    setLoading(false);
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, explainData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, explainData.paging.pageSize, {});
  };

  const handleDelete = (explainId, explainName) => {
    confirm.delete({
      title: t("DELETE_EXPLAIN"),
      content: t("DELETE_EXPLAIN_CONFIRM", { name: explainName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setLoading(true);
        const apiResponse = await deleteExplain(explainId, true);
        if (apiResponse) {
          toast.success(t("DELETE_EXPLAIN_SUCCESS"));
          await getExplainData();
        } else {
          toast.error(t("DELETE_EXPLAIN_ERROR"));
          setLoading(false);
        }
      },
    });
  };



  // Helper function to get explain type label
  const getExplainTypeLabel = (type) => {
    const explainType = EXPLAIN_TYPES.find(item => item.value === type);
    return explainType ? explainType.label : type;
  };

  // Helper function to get task type label
  const getTaskTypeLabel = (type) => {
    const taskType = TASK_TYPES.find(item => item.value === type);
    return taskType ? taskType.label : type;
  };

  // Define table columns
  const columns = [
    {
      ...orderColumn(explainData.paging),
      width: 120,
    },
    {
      title: t("NAME"),
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (text) => <span className="explain-name-value">{text}</span>,
    },
    {
      title: t("GPT_MODEL"),
      dataIndex: "gptModel",
      key: "gptModel",
      width: 150,
      render: (text) => text || "gpt-4o-mini",
    },
    {
      title: t("EXPLAIN_TYPE"),
      dataIndex: "explainType",
      key: "explainType",
      width: 150,
      render: (text) => {
        let color;
        switch (text) {
          case "idea":
            color = "blue";
            break;
          case "find_vocabulary":
            color = "purple";
            break;
          case "help_me_understand":
            color = "cyan";
            break;
          case "help_me_write":
            color = "magenta";
            break;
          default:
            color = "default";
        }
        return (
          <Tag color={color}>{getExplainTypeLabel(text)}</Tag>
        );
      },
    },
    {
      title: t("TASK_TYPE"),
      dataIndex: "taskType",
      key: "taskType",
      width: 150,
      render: (text) => {
        let color;
        switch (text) {
          case "task1":
            color = "green";
            break;
          case "task2":
            color = "orange";
            break;
          case "general":
            color = "geekblue";
            break;
          default:
            color = "default";
        }
        return (
          <Tag color={color}>{getTaskTypeLabel(text)}</Tag>
        );
      },
    },
    {
      title: t("RESPONSE_FORMAT"),
      dataIndex: "responseFormat",
      key: "responseFormat",
      width: 150,
      render: (text) => {
        let color;
        switch (text) {
          case "json":
            color = "blue";
            break;
          case "text":
            color = "green";
            break;
          case "html":
            color = "purple";
            break;
          default:
            color = "default";
        }
        return text ? <Tag color={color}>{text}</Tag> : "-";
      },
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (text) => formatTimeDate(text),
    },
    {
      title: t("ACTION"),
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="explain-actions">
          <Tooltip title={t("EDIT_EXPLAIN")}>
            <Link to={LINK.ADMIN_EXPLAIN_DETAIL.format(record._id)}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className={"btn-edit-explain"}
                icon={<EditOutlined/>}
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_EXPLAIN")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-explain"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(record?._id, record?.name)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(explainData.paging, explainData.query, i18n.language);
  const gptModelOptions = gptModelData.map((item) => ({ label: item.gptModel, value: item.gptModel }));

  return (
    <Loading active={isLoading} transparent>
      <div className="explain-container">
        <Card className="explain-info-card">
          <div className="explain-info-header">
            <div>
              <h1 className="explain-title">{t("EXPLAIN_MANAGEMENT")}</h1>
              <p className="explain-description">{t("EXPLAIN_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN_EXPLAIN_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create-explain"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_EXPLAIN")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="explain-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_EXPLAIN_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item name="gptModel" className="search-form-item">
                      <Select
                        options={gptModelOptions}
                        allowClear
                        placeholder={t("FILTER_BY_GPT_MODEL")}
                        showSearch
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item name="explainType" className="search-form-item">
                      <Select
                        options={EXPLAIN_TYPES}
                        placeholder={t("FILTER_BY_EXPLAIN_TYPE")}
                        allowClear
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={6}>
                    <AntForm.Item name="taskType" className="search-form-item">
                      <Select
                        options={TASK_TYPES}
                        placeholder={t("FILTER_BY_TASK_TYPE")}
                        allowClear
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="explain-table-card">
          <TableAdmin
            columns={columns}
            dataSource={explainData.rows}
            pagination={{ ...pagination }}
            scroll={{ x: 1000 }}
            className={"explain-table"}
            rowClassName={() => "explain-table-row"}
            locale={{ emptyText: t("NO_EXPLAINS_FOUND") }}
            rowKey="_id"
          />
        </Card>
      </div>
    </Loading>
  );
};

export default Explain;
