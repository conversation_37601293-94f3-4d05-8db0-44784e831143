# Explain Service - Hướng dẫn quản lý

## Tổng quan

Explain Service là một service con của Tools Service, cung cấp các giải thích và hỗ trợ cho người dùng trong việc học tiếng Anh và chuẩn bị cho kỳ thi IELTS. Service này sử dụng các mô hình ngôn ngữ lớn (LLM) để tạo ra các giải thích, ý tưởng, từ vựng và hướng dẫn viết cho các chủ đề khác nhau.

## Cấu trúc dữ liệu

Mỗi bản ghi Explain có cấu trúc như sau:

```json
{
    "_id": "ObjectId",
    "name": "Generate Ideas",
    "instruction": "Generate creative ideas for the following topic...",
    "explainType": "idea",
    "taskType": "general",
    "gptModel": "gpt-4o-mini",
    "responseFormat": "text",
    "temperature": 0.7,
    "maxTokens": 1000,
    "schemaInstruction": null,
    "isDeleted": false,
    "createdAt": "2023-06-15T10:30:00.000Z",
    "updatedAt": "2023-06-15T10:30:00.000Z"
}
```

### Các trường dữ liệu chính

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| name | String | Tên của explain |
| instruction | String | Hướng dẫn chi tiết gửi đến LLM |
| explainType | String | Loại explain: "idea", "find_vocabulary", "help_me_understand", "help_me_write" |
| taskType | String | Loại task: "task1", "task2", "general" |
| gptModel | String | Mô hình GPT sử dụng, mặc định là "gpt-4o-mini" |
| responseFormat | String | Định dạng phản hồi: "text", "markdown", "json_object" |
| temperature | Number | Nhiệt độ cho LLM (0.0 - 1.0) |
| maxTokens | Number | Số token tối đa cho phản hồi |
| schemaInstruction | Object | Schema cho định dạng JSON (nếu có) |
| isDeleted | Boolean | Trạng thái xóa |

## API Endpoints cho quản lý

### 1. Lấy danh sách Explains

```
GET /api/explain
```

**Tham số query**:
- `page`: Số trang (mặc định: 1)
- `pageSize`: Số bản ghi mỗi trang (mặc định: 10)
- `sort`: Trường sắp xếp (mặc định: "createdAt")
- `order`: Thứ tự sắp xếp: "asc" hoặc "desc" (mặc định: "desc")
- `search`: Tìm kiếm theo tên
- `explainType`: Lọc theo explainType
- `taskType`: Lọc theo taskType

**Phản hồi**:
```json
{
  "rows": [...],  // Mảng các bản ghi Explain
  "total": 42,    // Tổng số bản ghi
  "page": 1,      // Trang hiện tại
  "pageSize": 10, // Số bản ghi mỗi trang
  "totalPages": 5 // Tổng số trang
}
```

### 2. Lấy chi tiết Explain

```
GET /api/explain/:id
```

**Phản hồi**: Bản ghi Explain chi tiết

### 3. Tạo Explain mới

```
POST /api/explain
```

**Body**:
```json
{
  "name": "Generate Ideas",
  "instruction": "Generate creative ideas for the following topic...",
  "explainType": "idea",
  "taskType": "general",
  "gptModel": "gpt-4o-mini",
  "responseFormat": "text",
  "temperature": 0.7,
  "maxTokens": 1000
}
```

**Phản hồi**: Bản ghi Explain đã tạo

### 4. Cập nhật Explain

```
PUT /api/explain/:id
```

**Body**: Các trường cần cập nhật

**Phản hồi**: Bản ghi Explain đã cập nhật

### 5. Xóa Explain

```
DELETE /api/explain/:id
```

**Phản hồi**: Xác nhận xóa thành công

## Hướng dẫn phát triển giao diện quản lý

### 1. Trang danh sách Explains

- Hiển thị danh sách dưới dạng bảng với các cột: ID, Tên, Loại explain, Loại task, Ngày tạo
- Cung cấp bộ lọc cho explainType và taskType
- Cung cấp ô tìm kiếm theo tên
- Cung cấp nút "Thêm mới", "Sửa", "Xóa"
- Phân trang

### 2. Form thêm/sửa Explain

- Các trường bắt buộc: name, instruction, explainType, taskType
- Các trường tùy chọn: gptModel, responseFormat, temperature, maxTokens, schemaInstruction
- Trường instruction nên là textarea lớn
- Trường schemaInstruction nên là JSON editor
- Trường temperature nên là slider từ 0.0 đến 1.0

### 3. Xem trước kết quả

- Cung cấp chức năng "Xem trước" để kiểm tra kết quả của Explain
- Cho phép nhập chủ đề mẫu và xem kết quả trả về
- Đối với Task 1, cho phép tải lên hình ảnh mẫu

## Các loại Explain

### 1. Generate Ideas (idea)
Tạo ý tưởng sáng tạo cho một chủ đề.

### 2. Find Vocabulary (find_vocabulary)
Tìm và giải thích từ vựng liên quan đến chủ đề.

### 3. Help Me Understand (help_me_understand)
Giải thích cách tiếp cận một chủ đề IELTS.

### 4. Help Me Write (help_me_write)
Cung cấp hướng dẫn và mẫu viết cho IELTS.

## Các loại Task

### 1. Task 1
Liên quan đến mô tả biểu đồ, bảng, sơ đồ hoặc quy trình.

### 2. Task 2
Liên quan đến viết bài luận về một chủ đề.

### 3. General
Các chủ đề chung không thuộc Task 1 hoặc Task 2.

## Lưu ý quan trọng

1. Đảm bảo rằng instruction rõ ràng và chi tiết để LLM có thể tạo ra kết quả tốt.
2. Đối với Task 1, cần hỗ trợ tải lên và hiển thị hình ảnh.
3. Nhiệt độ (temperature) ảnh hưởng đến tính sáng tạo của kết quả. Giá trị cao hơn (gần 1.0) sẽ tạo ra kết quả đa dạng hơn, giá trị thấp hơn (gần 0.0) sẽ tạo ra kết quả nhất quán hơn.
4. Số token tối đa (maxTokens) ảnh hưởng đến độ dài của kết quả. Giá trị cao hơn sẽ cho phép kết quả dài hơn.
5. Đối với responseFormat là "json_object", cần cung cấp schemaInstruction để định dạng kết quả.
