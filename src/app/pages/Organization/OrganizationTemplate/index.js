import React, { useContext, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Popconfirm, Segmented, Tooltip } from "antd";
import axios from "axios";

import { toast } from "@component/ToastProvider";
import { useOrganization } from "@app/pages/Organization";

import NoData from "@component/NoData";
import Loading from "@component/Loading";
import AntButton from "@component/AntButton";
import PreviewPdf from "@component/PreviewPdf";
import CreateOrgDocTemplate from "./CreateOrgDocTemplate";
import RenameOrgDocTemplate from "./RenameOrgDocTemplate";
import DocTemplateOptions from "@app/pages/Organization/OrganizationTemplate/DocTemplateOptions";
import OrgDocTemplateList from "@app/pages/Organization/OrganizationTemplate/OrgDocTemplateList";


import { BUTTON, CONSTANT } from "@constant";
import { cloneObj } from "@common/functionCommons";

import {
  createFileClickeeTemplate,
  createFileOrgTemplate,
  deleteDocumentTemplate,
  editDocumentTemplate,
  getAllOrgDocumentTemplate,
  previewDocTemplateFile,
  publishDocumentTemplate,
} from "@services/DocumentTemplate";
import { downloadFileById } from "@services/File";

import Download from "@component/SvgIcons/Download";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";
import Lock from "@component/SvgIcons/Lock";
import Globe from "@component/SvgIcons/Globe";
import Edit24 from "@component/SvgIcons/Edit/Edit24";

import "./OrganizationTemplate.scss";

const PREVIEW_FILE_INIT = { fileName: "", fileBlob: null, status: CONSTANT.NOT_STARTED };

export const DocTemplateContext = React.createContext();

function OrganizationTemplate() {
  const { t } = useTranslation();
  const { orgId } = useOrganization();
  
  const requestGetPreview = useRef(null);
  
  const [orgDocumentTemplateList, setOrgDocumentTemplateList] = useState([]);
  const [orgDocTemplateSelected, setOrgDocTemplateSelected] = useState(null);
  
  const [previewData, setPreviewData] = useState(PREVIEW_FILE_INIT);
  const [docTemplateState, setDocTemplateState] = useState({ isShowModal: false });
  
  
  const [updateState, setUpdateState] = useState(false);
  const [isLoadingPublish, setLoadingPublish] = useState(false);
  
  useEffect(() => {
    getOrgDocumentTemplateData();
  }, []);
  
  useEffect(() => {
    renderPreview();
  }, [orgDocTemplateSelected?._id]);
  
  
  function renderPreview() {
    setPreviewData(PREVIEW_FILE_INIT);
    if (requestGetPreview.current) {
      requestGetPreview.current.cancel();
      requestGetPreview.current = null;
    }
    
    if (!orgDocTemplateSelected?._id) return;
    
    setPreviewData(prevState => Object.assign({}, prevState, { status: CONSTANT.IN_PROGRESS }));
    
    if (!!orgDocTemplateSelected?.organizationId) {
      handlePreviewOrgTemplate();
    } else {
      handlePreviewClickeeTemplate();
    }
  }
  
  async function getOrgDocumentTemplateData() {
    const apiRequest = { sort: "-updatedAt" };
    const apiResponse = await getAllOrgDocumentTemplate(orgId, apiRequest);
    if (apiResponse) {
      setOrgDocumentTemplateList(apiResponse);
    }
  }
  
  async function handlePreviewClickeeTemplate() {
    const source = axios.CancelToken.source();
    requestGetPreview.current = source;
    
    const config = { cancelToken: source.token };
    
    const apiRequest = {
      docxTemplateId: orgDocTemplateSelected._id,
      organizationId: orgId,
    };
    const apiResponse = await createFileClickeeTemplate(apiRequest, config);
    if (apiResponse?.fileName) {
      getPreviewData(apiResponse.fileName);
    } else if (apiResponse?.code !== CONSTANT.ERR_CANCELED) {
      setPreviewData(prevState => Object.assign({}, prevState, { status: CONSTANT.ERROR }));
    }
  }
  
  async function handlePreviewOrgTemplate() {
    if (!orgDocTemplateSelected.templateId?._id) return;
    
    const source = axios.CancelToken.source();
    requestGetPreview.current = source;
    
    const config = { cancelToken: source.token };
    
    const apiRequest = { templateId: orgDocTemplateSelected.templateId._id };
    const apiResponse = await createFileOrgTemplate(apiRequest, config);
    if (apiResponse?.fileName) {
      getPreviewData(apiResponse.fileName);
    } else if (apiResponse?.code !== CONSTANT.ERR_CANCELED) {
      setPreviewData(prevState => Object.assign({}, prevState, { status: CONSTANT.ERROR }));
    }
  }
  
  async function getPreviewData(fileName) {
    const source = axios.CancelToken.source();
    requestGetPreview.current = source;
    const config = { cancelToken: source.token };
    
    const previewResponse = await previewDocTemplateFile(fileName, config);
    if (previewResponse) {
      setPreviewData({ fileName, fileBlob: previewResponse, status: CONSTANT.DONE });
    } else {
      setPreviewData(prevState => Object.assign({}, prevState, { status: CONSTANT.ERROR }));
    }
  }
  
  function onFinishCreateDoc(doc) {
    setOrgDocumentTemplateList(prevState => [...prevState, doc]);
    setDocTemplateState({ isShowModal: false });
    toast.success("UPLOAD_SUCCESS");
  }
  
  async function handleDeleteOrgDocTemplate() {
    if (!orgDocTemplateSelected?._id) return;
    const apiResponse = await deleteDocumentTemplate(orgDocTemplateSelected._id);
    if (apiResponse) {
      setOrgDocTemplateSelected(null);
      setOrgDocumentTemplateList(prevState => cloneObj(prevState).filter(state => state._id !== apiResponse._id));
      toast.success("DELETE_TEMPLATE_SUCCESS");
    }
  }
  
  async function handleChangeOrgDocTemplateName(values) {
    if (!orgDocTemplateSelected?._id) return;
    const apiRequest = { ...values, _id: orgDocTemplateSelected._id };
    const apiResponse = await editDocumentTemplate(apiRequest);
    if (apiResponse) {
      setOrgDocumentTemplateList(prevState => {
        return cloneObj(prevState).map(state => {
          if (state._id === apiResponse._id) state.name = apiResponse.name;
          return state;
        });
      });
      
      setOrgDocTemplateSelected(apiResponse);
      setUpdateState(false);
      toast.success("UPDATE_TEMPLATE_SUCCESS");
    }
  }
  
  function handleDownloadDocTemplate() {
    if (!orgDocTemplateSelected?.templateId) {
      return toast.warning("FILE_NOT_FOUND");
    }
    downloadFileById(orgDocTemplateSelected.templateId._id, orgDocTemplateSelected.templateId.displayName);
  }
  
  async function handleChangePublishDocTemplate(publishValue) {
    if (!orgDocTemplateSelected?._id) return;
    setLoadingPublish(true);
    const apiRequest = {
      docxTemplateId: orgDocTemplateSelected._id,
      organizationId: orgId,
      isOrgPublic: publishValue,
    };
    const apiResponse = await publishDocumentTemplate(apiRequest);
    if (apiResponse) {
      setOrgDocumentTemplateList(prevState => {
        return cloneObj(prevState).map(state => {
          if (state._id === orgDocTemplateSelected._id) {
            state.isOrgPublic = publishValue;
          }
          return state;
        });
      });
      setOrgDocTemplateSelected(prevState => Object.assign({}, prevState, { isOrgPublic: publishValue }));
      
      toast.success(publishValue ? "SET_PUBLIC_TEMPLATE_SUCCESS" : "SET_PRIVATE_TEMPLATE_SUCCESS");
    }
    setLoadingPublish(false);
  }
  
  return <DocTemplateContext.Provider value={{
    orgDocTemplateSelected, docTemplateState,
    orgDocumentTemplateList, setOrgDocumentTemplateList,
    
    //
    getOrgDocumentTemplateData,
    renderPreview,
  }}>
    <div className="organization-template-container">
      
      
      <OrgDocTemplateList
        publicField="isOrgPublic"
        dataSource={orgDocumentTemplateList?.filter(x => !!x)}
        onUpload={() => setDocTemplateState({ isShowModal: true })}
        itemSelected={orgDocTemplateSelected}
        onSelectItem={setOrgDocTemplateSelected}
      />
      
      {!!orgDocTemplateSelected && <>
        <div className="org-doc-template__title">
          <div className="org-doc-template__title-text">
            <label>
              {`${t("TEMPLATE")}: `}
              <Tooltip title={orgDocTemplateSelected.name} placement="topRight">
                {orgDocTemplateSelected.name}
              </Tooltip>
            </label>
            {orgDocTemplateSelected.organizationId && <AntButton
              size="small"
              type={BUTTON.WHITE_NAVY}
              icon={<Edit24 />}
              className="org-doc-template__title-edit"
              onClick={() => setUpdateState(true)}
            />}
          </div>
          
          <div className="org-doc-template__action">
            {orgDocTemplateSelected.organizationId &&
              <Popconfirm
                placement="bottomRight"
                icon={null}
                title={`${t("DELETE_TEMPLATE")}?`}
                onConfirm={handleDeleteOrgDocTemplate}
                onCancel={e => e.stopPropagation()}
                okText={t("DELETE")}
                cancelText={t("CANCEL")}
                cancelButtonProps={{ className: "ant-btn-light-blue ant-btn-xsmall" }}
                okButtonProps={{ className: "ant-btn-deep-red ant-btn-xsmall" }}
              >
                <AntButton
                  size="small"
                  type={BUTTON.GHOST_NAVY}
                  icon={<DeleteIcon />}
                >
                  {t("DELETE")}
                </AntButton>
              </Popconfirm>}
            
            <AntButton
              size="small"
              type={BUTTON.GHOST_NAVY}
              icon={<Download />}
              onClick={handleDownloadDocTemplate}
            >
              {t("DOWNLOAD")}
            </AntButton>
            
            <Loading active={isLoadingPublish}>
              <Segmented
                value={orgDocTemplateSelected.isOrgPublic}
                options={[
                  { label: t("PRIVATE"), value: false, icon: <Lock /> },
                  { label: t("PUBLIC"), value: true, icon: <Globe /> },
                ]}
                onChange={handleChangePublishDocTemplate}
              />
            </Loading>
          </div>
        </div>
        
        <div className="org-doc-template__detail">
          <div className="org-doc-template__detail-preview">
            {previewData.status === CONSTANT.IN_PROGRESS && <Loading active />}
            {previewData.status === CONSTANT.DONE && <PreviewPdf file={previewData.fileBlob} />}
            {previewData.status === CONSTANT.ERROR && <NoData />}
          </div>
          
          <DocTemplateOptions />
        </div>
      </>}
    </div>
    
    <CreateOrgDocTemplate
      isOpen={docTemplateState.isShowModal}
      handleCancel={() => setDocTemplateState({ isShowModal: false })}
      onFinishCreateDoc={onFinishCreateDoc}
    />
    
    <RenameOrgDocTemplate
      isOpen={updateState}
      handleCancel={() => setUpdateState(false)}
      orgDocTemplateSelected={orgDocTemplateSelected}
      handleOk={handleChangeOrgDocTemplateName}
    />
  
  </DocTemplateContext.Provider>;
}


const useDocTemplate = () => useContext(DocTemplateContext);

export { OrganizationTemplate, useDocTemplate };