import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { toast } from "@component/ToastProvider";

import { useOrganization } from "@app/pages/Organization";
import { useDocTemplate } from "@app/pages/Organization/OrganizationTemplate";

import Loading from "@component/Loading";
import DocTempHeader from "./DocTempHeader";

import { createDocumentHeaderOrg, getDocDefaultHeader } from "@services/DocumentHeader";
import { getAllDocumentOption } from "@services/DocumentOption";

import "./DocTemplateOptions.scss";

function DocTemplateOptions() {
  const { t } = useTranslation();
  
  const { orgId } = useOrganization();
  const { orgDocTemplateSelected, getOrgDocumentTemplateData, renderPreview } = useDocTemplate();
  
  const [isLoadingDocHeader, setLoadingDocHeader] = useState(false);
  
  const [isLoadingSave, setLoadingSave] = useState(false);
  
  
  const [documentHeaderData, setDocumentHeaderData] = useState(null);
  const [documentOptionsData, setDocumentOptionsData] = useState(null);
  
  
  useEffect(() => {
    getDocumentOptionData();
  }, [orgDocTemplateSelected?._id]);
  
  
  async function getDocumentOptionData() {
    if (!!orgDocTemplateSelected && !orgDocTemplateSelected.organizationId) {
      setLoadingDocHeader(true);
      const allRequest = [
        getAllDocumentOption({ docxTemplateId: orgDocTemplateSelected._id }),
        getDocDefaultHeader(orgDocTemplateSelected._id, orgId),
      ];
      
      const [
        docOptionResponse,
        docHeaderResponse,
      ] = await Promise.all(allRequest);
      
      setDocumentOptionsData(docOptionResponse || null);
      setDocumentHeaderData(docHeaderResponse || null);
      
      setLoadingDocHeader(false);
    }
  }
  
  async function handleSaveHeader(values) {
    setLoadingSave(true);
    const apiRequest = {
      docxTemplateId: orgDocTemplateSelected._id,
      customHeader: values,
      organizationId: orgId,
    };
    
    const apiResponse = await createDocumentHeaderOrg(apiRequest);
    if (apiResponse) {
      setDocumentHeaderData(apiResponse);
      toast.success("SAVE_DATA_SUCCESS");
      renderPreview();
      
      getOrgDocumentTemplateData();
    }
    setLoadingSave(false);
  }
  
  if (!!orgDocTemplateSelected?.organizationId || !documentOptionsData?.length) return null;
  
  if (isLoadingDocHeader) return <div className="doc-template-options">
    <Loading active className="doc-template-header-content" />
  </div>;
  
  return <>
    <div className="doc-template-options">
      <Loading active={isLoadingSave}>
        <DocTempHeader
          docOptionData={documentOptionsData}
          docHeaderData={documentHeaderData}
          onSaveHeader={handleSaveHeader}
        />
      </Loading>
    </div>
  </>;
}

export default DocTemplateOptions;