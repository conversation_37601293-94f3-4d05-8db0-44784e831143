import React, { useEffect, useRef } from "react";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";

import { AntForm } from "@component/AntForm";
import AntModal from "@component/AntModal";

import RULE from "@rule";
import { CONSTANT } from "@constant";


function RenameOrgDocTemplate({ isOpen, handleCancel, handleOk, orgDocTemplateSelected }) {
  const { t } = useTranslation();
  const [renameOrgDocTemplateForm] = Form.useForm();
  const renameInput = useRef(null);
  
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        renameInput.current.focus();
      }, 100);
      renameOrgDocTemplateForm.resetFields();
      renameOrgDocTemplateForm.setFieldsValue({ name: orgDocTemplateSelected?.name });
    }
  }, [isOpen]);
  
  return <AntModal
    onCancel={handleCancel}
    open={isOpen}
    formId="form-rename-org-doc-template"
    closeIcon={null}
    align={CONSTANT.CENTER}
  >
    <AntForm
      id="form-rename-org-doc-template"
      layout="vertical"
      form={renameOrgDocTemplateForm}
      onFinish={handleOk}
    >
      <AntForm.Item label={t("RENAME")} name="name" rules={[RULE.REQUIRED]}>
        <Input size="large" allowClear ref={renameInput}></Input>
      </AntForm.Item>
    </AntForm>
  </AntModal>;
}

export default RenameOrgDocTemplate;