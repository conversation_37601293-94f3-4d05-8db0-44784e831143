@import "src/app/styles/scroll";

.organization-template-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .org-doc-template__title {
    display: flex;
    flex-wrap: wrap;
    row-gap: 8px;
    column-gap: 16px;

    .org-doc-template__title-text {
      font-size: 20px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      label {
        max-width: 480px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
      }

      .org-doc-template__title-edit {

        .ant-btn-icon svg {
          width: 24px;
          height: 24px;
        }
      }
    }

    .org-doc-template__action {
      margin-left: auto;
      display: flex;
      flex-direction: row;
      gap: 8px;

      .ant-segmented {
        margin-left: 8px;
        padding: 0;
        border-radius: 8px;

        .ant-segmented-thumb {
          border-radius: 8px;
        }

        .ant-segmented-item {
          border-radius: 8px;

          .ant-segmented-item-label {
            font-size: 16px;
            line-height: 1.25;
            padding: 5px 28px;
            display: flex;
            justify-content: center;
            align-items: center;

            .ant-segmented-item-icon {
              height: 20px;
              display: inline-flex;
              align-items: center;
            }
          }
        }
      }
    }
  }

  .org-doc-template__detail {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 24px;

    .org-doc-template__detail-preview {
      aspect-ratio: 796 / 732;
      overflow-y: scroll;

      .loading-component {
        height: 100%;

        .loading-backdrop .loading-spin {
          top: 200px;
        }
      }

    }
  }
}