.organization-container {
  background-color: var(--background-light-background-2);
  display: flex;
  flex-direction: column;

  gap: 24px;
  padding: 24px;
  border-radius: 8px;
  min-height: 100%;

  .organization-tabs {
    .ant-tabs-nav {
      margin-bottom: 24px;

      &::before {
        display: none;
      }

      .ant-tabs-tab {
        padding: 0 0 5px 0;
      }

      .ant-tabs-tab-btn {
        font-weight: 700;
      }

      /*.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          color: var(--typo-colours-support-blue);
        }
      }*/

      .ant-tabs-ink-bar {
        //background-color: var(--primary-colours-blue) !important;
        height: 1px;
      }
    }
  }
}