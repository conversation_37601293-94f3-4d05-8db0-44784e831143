import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { Col, DatePicker, Row, Select, Table, Form } from "antd"
import dayjs from "dayjs";

import { AntForm } from "@src/app/component/AntForm";
import AntButton from "@src/app/component/AntButton";
import Loading from "@src/app/component/Loading";

import { getStatisticOrganization } from "@services/Organization";
import { formatTimeDate } from "@src/common/functionCommons";

import { BUTTON } from "@constant";

import "./TrackingMember.scss";

const TrackingMember = ({ user, ...props }) => {
  const { t } = useTranslation();
  const [formFilter] = Form.useForm();

  const organizationId = user?.organizationId?._id;
  const [memberOptions, setMemberOptions] = useState([]);

  const [statisticData, setStatisticData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearchable, setSearchable] = useState(false);
  const [isShowSelectDate, setShowSelectDate] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);

  useEffect(() => {
    (async () => {
      const allMembers = await getStatisticData({ time: 'month' });
      setMemberOptions(allMembers.map((data) => ({ fullName: data?.fullName, _id: data?._id, key: data?._id })));
    })();
    formFilter.setFieldsValue({ time: 'month' });
  }, []);

  const getStatisticData = async (dataSearch) => {
    setIsLoading(true);
    const response = await getStatisticOrganization({ ...dataSearch, organizationId });
    if (response) {
      setStatisticData(response.map((data) => ({ ...data, key: data?._id })));
      setSearchable(false);
    }
    setIsLoading(false);
    return response || [];
  }

  const onFilterSubmit = async (values) => {
    let dataRequest = {};
    Object.entries(values).forEach(([key, value]) => {
      if (value) {
        if (key === 'fromDate') {
          dataRequest.fromDate = dayjs(value)?.startOf("day")?.unix();
        } else if (key === 'toDate') {
          dataRequest.toDate = dayjs(value)?.endOf("day")?.unix();
        } else {
          dataRequest[key] = value;
        }
      }
    })
    await getStatisticData(dataRequest);
  }

  const onFormChange = (values) => {
    setSearchable(true);
  }

  const clearFormFilter = () => {
    formFilter.resetFields();
    setShowSelectDate(false);
    setSearchable(true);
  }

  const hanldeChangeSelectTime = (value) => {
    if (value === "custom") {
      setShowSelectDate(true);
    } else {
      setShowSelectDate(false);
      formFilter.setFieldsValue({ fromDate: null, toDate: null });
    }
  };

  const columns = [
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      key: "fullName",
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      key: "email",
    },
    {
      title: t('LAST_ACCESS'),
      dataIndex: 'lastVisit',
      key: 'lastAccess',
      render: (text) => formatTimeDate(text)
    },
    {
      title: <div className="title-with-unit"><span>{t('STORAGE')}</span> <span>(Mb)</span></div>,
      dataIndex: 'capacityUsed',
      key: 'storage',
      render: (value) => Math.round(value * 100) / 100,
      align: 'center',
      sorter: (a, b) => a.capacityUsed - b.capacityUsed
    },
    {
      title: <div className="title-with-unit"><span>{t('USE_TEXT_TOOL')}</span> <span>{`(${t('SUBMIT')})`}</span></div>,
      dataIndex: 'textSubmit',
      key: 'textSubmit',
      align: 'center',
    },
    {
      title: <div className="title-with-unit"><span>{t('USE_MEDIA_TOOL')}</span> <span>{`(${t('SUBMIT')})`}</span></div>,
      dataIndex: 'mediaSubmit',
      key: 'mediaSubmit',
      align: 'center',
    },
  ]
  
  return (
    <div className="tracking-member">
      <AntForm
        form={formFilter}
        size={"large"}
        className={"tracking-member__filter-form"}
        onFinish={onFilterSubmit}
        onValuesChange={onFormChange}
      >
        <Row gutter={[24, 24]} className="grow">
          <Col xs={24} md={12} lg={8} xl={6}>
            <AntForm.Item name={"userId"}>
              <Select
                placeholder={t("MEMBER")}
                options={memberOptions}
                fieldNames={{ label: "fullName", value: "_id" }}
                allowClear
                showSearch
              />
            </AntForm.Item>
          </Col>
          <Col xs={24} md={12} lg={8} xl={6}>
            <AntForm.Item name={"time"}>
              <Select placeholder={t("SELECT_TIME")} onChange={hanldeChangeSelectTime} allowClear>
                <Select.Option value={"week"}>{t("THIS_WEEK")}</Select.Option>
                <Select.Option value={"month"}>{t("THIS_MONTH")}</Select.Option>
                <Select.Option value={"custom"}>{t("CUSTOM")}</Select.Option>
              </Select>
            </AntForm.Item>
          </Col>
          {isShowSelectDate && <>
            <Col xs={24} md={12} lg={8} xl={6}>
              <AntForm.Item
                name={"fromDate"}
              >
                <DatePicker
                  placeholder={t("SELECT_FROM_DATE")}
                  size="large"
                  className="filter-form__date-picker"
                  format="DD/MM/YYYY"
                  maxDate={maxDate}
                  onChange={setMinDate}
                />
              </AntForm.Item>
            </Col>
            <Col xs={24} md={12} lg={8} xl={6}>
              <AntForm.Item
                name={"toDate"}
              >
                <DatePicker
                  placeholder={t("SELECT_TO_DATE")}
                  size="large"
                  className="filter-form__date-picker"
                  format="DD/MM/YYYY"
                  minDate={minDate}
                  onChange={setMaxDate}
                />
              </AntForm.Item>
            </Col>
          </>}
        </Row>
        <div className={"filter-form__actions"}>
          <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>{t("CLEAR")}</AntButton>
          <AntButton type={BUTTON.DEEP_NAVY} htmlType={"submit"} disabled={!isSearchable}>{t("SEARCH")}</AntButton>
        </div>
      </AntForm>

      <Loading active={isLoading} transparent >
        <Table
          dataSource={statisticData}
          columns={columns}
          className="tracking-member__table"
        />
      </Loading>
    </div>
  )
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(TrackingMember);