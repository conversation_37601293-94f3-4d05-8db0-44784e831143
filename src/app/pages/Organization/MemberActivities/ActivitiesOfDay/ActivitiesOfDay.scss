.activities-of-day {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .activities-of-day__title {
    padding: 10px 16px 10px 16px;
    font-size: 16px;
    font-weight: 700;
    background-color: var(--background-light-background-1);
  }

  .activities-of-day__content {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .activities-of-day__activity-item {
      display: flex;
      flex-direction: row;
      gap: 8px;
      position: relative;

      &:not(:last-child) {
        &::after {
          position: absolute;
          bottom: -8px;
          content: '';
          width: 100%;
          height: 1px;
          background-color: var(--background-light-background-grey);
        }
      }

      .activity-item__item-content {
        font-size: 16px;

        .item-content__activity-info {
          .activity-info__item {
            &::after {
              content: " "
            }

            &.blue-dark {
              color: #2196F3;
            }
          }
        }

        .item-content__email {
          color: var(--typo-colours-support-blue-light);
        }
      }
    }
  }
}