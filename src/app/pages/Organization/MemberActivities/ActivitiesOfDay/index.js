import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { snakeCase } from "lodash";

import AntAvatar from "@src/app/component/AntAvatar";
import { formatTimeDate } from "@src/common/functionCommons";

import { PERMISSION } from "@constant";

import './ActivitiesOfDay.scss';

const ActivitiesOfDay = ({ user, ...props }) => {
  const { title, activities } = props;
  const { t } = useTranslation();

  const renderBaseContent = (actionData, isProject = false) => {
    const projectName = actionData?.projectId?.projectName;
    const folderName = actionData?.folderId?.folderName;
    return (isProject
      ? `${t("PROJECT").toLowerCase()} "${projectName}"`
      : `${t("FOLDER").toLowerCase()} "${folderName}"`);
  }

  const renderActionContent = (actionData) => {
    const isProject = !!actionData?.projectId;
    const inFolderName = isProject ? actionData?.folderId?.folderName : '';
    const action = actionData?.action;
    switch (true) {
      case action === "upload":
        return `${t("FILE").toLowerCase()} "${actionData?.metadata?.name}"`;

      case (['create', 'update', 'remove'].includes(action) && !!inFolderName): {
        return `${renderBaseContent(actionData, isProject)} ${t("IN_FOLDER")} "${inFolderName}"`;
      }

      case action === "copy": {
        if (!isProject) {
          const fromFolder = actionData?.metadata?.copyFolderId?.folderName;
          return `${renderBaseContent(actionData)} ${t("FROM_FOLDER")} "${fromFolder}"`;
        }
        const fromProject = actionData?.metadata?.copyProjectId?.projectName;
        if (inFolderName) {
          return `${renderBaseContent(actionData, isProject)} ${t("IN_FOLDER")} "${inFolderName}" ${t("FROM_PROJECT")} "${fromProject}"`;
        }
        return `${renderBaseContent(actionData, isProject)} ${t("FROM_PROJECT")} "${fromProject}"`
      }

      case action === "move": {
        let contentText = `${renderBaseContent(actionData, isProject)} ${t("TO_FOLDER")} "${inFolderName}"`;
        const oldFolder = actionData?.metadata?.oldFolderId?.folderName;
        if (oldFolder) contentText += ` ${t("FROM_FOLDER")} "${oldFolder}"`;
        return contentText;
      }
      
      case ['share', 'unshare'].includes(action): {
        const email = actionData?.metadata?.email || actionData?.metadata?.shareUserId?.email;
        const permission = actionData?.metadata?.permission;
        const type = actionData?.metadata?.type;
        const generalAccessInfo = !!type && (type === 'organisational' ? user?.organizationId?.name : t(snakeCase(type).toUpperCase()));
        let contentText = `${renderBaseContent(actionData, isProject)}`;
        if (inFolderName) contentText += ` ${t("IN_FOLDER")} "${inFolderName}"`;
        contentText += type ? ` ${t("GENERAL_ACCESS").toLowerCase()} "${generalAccessInfo}"` : ` ${t("FOR_EMAIL")} "${email}"`;
        if (permission) contentText += ` ${t('WITH_PERMISSION')}: "${t(PERMISSION[permission])}"`;
        return contentText;
      }

      default:
        return renderBaseContent(actionData, isProject);
    }
  }
  return (
    <div className="activities-of-day">
      <div className="activities-of-day__title">{title}</div>
      <div className="activities-of-day__content">
        {activities.map((element, index) => {
          return (
            <div className="activities-of-day__activity-item" key={index}>
              <AntAvatar name={element?.userId?.fullName} shape="circle" size="default" />
              <div className="activity-item__item-content">
                <div className="item-content__activity-info">
                  <span className="activity-info__item">{`${formatTimeDate(element?.createdAt)}:`}</span>
                  <span className="activity-info__item blue-dark">{element?.userId?.fullName}</span>
                  <span className="activity-info__item blue-dark">{`[${t(element?.action.toUpperCase())}]`}</span>
                  <span className="activity-info__item">{renderActionContent(element)}</span>
                </div>
                <div className="item-content__email">{element?.userId?.email}</div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}
export default connect(mapStateToProps)(ActivitiesOfDay);