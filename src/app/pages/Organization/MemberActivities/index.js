import React, { useEffect, useMemo, useState } from "react";
import { Col, Form, Row, Select, DatePicker } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import dayjs from "dayjs";

import { useOrganization } from "@app/pages/Organization";
import Loading from "@src/app/component/Loading";
import { AntForm } from "@src/app/component/AntForm";
import AntButton from "@src/app/component/AntButton";
import NoData from "@src/app/component/NoData";
import ActivitiesOfDay from "./ActivitiesOfDay";

import { BUTTON, getActivitiesActionOptions } from "@constant";
import { groupBy } from "@src/common/dataConverter";
import { getAllActivities } from "@services/Activities";

import "./MemberActivities.scss";

function MemberActivities({ user }) {
  const { t } = useTranslation();
  const { allMembers } = useOrganization();
  
  const [formFilter] = Form.useForm();
  
  const [isSearchable, setSearchable] = useState(false);
  const [activitiesData, setActivitiesData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const organizationId = user?.organizationId?._id;
  
  useEffect(() => {
    if (organizationId) {
      getActivitiesData();
    }
  }, [organizationId]);
  
  const getActivitiesData = async () => {
    setLoading(true);
    const apiResponse = await getAllActivities({ organizationId: organizationId, sort: "-updatedAt" });
    if (apiResponse) {
      setActivitiesData(apiResponse);
      setFilteredData(apiResponse);
    }
    setLoading(false);
  };
  
  const groupByDate = useMemo(() => {
    const toDay = dayjs().format("DD/MM/YYYY");
    const addDateData = filteredData.map(element => {
      const date = dayjs(element.createdAt).format("DD/MM/YYYY");
      return {
        ...element,
        date: date === toDay ? "TODAY" : date,
      };
    });
    return groupBy(addDateData, "date");
  }, [filteredData]);
  
  const onFilterSubmit = (values) => {
    const filterDate = values?.date && dayjs(values?.date).format("DD/MM/YYYY");
    let newFilteredData = activitiesData.filter(element => {
      let isMatchDate = true, isMatchAction = true, isMatchUser = true;
      if (filterDate) isMatchDate = filterDate === dayjs(element.createdAt).format("DD/MM/YYYY");
      if (values?.action?.length) isMatchAction = values?.action.includes(element.action);
      if (values?.member) isMatchUser = values?.member === element.userId?._id;
      return isMatchDate && isMatchAction && isMatchUser;
    });
    setFilteredData(newFilteredData);
    setSearchable(false);
  };
  
  const onFormChange = () => {
    setSearchable(true);
  };
  
  const clearFormFilter = () => {
    formFilter.resetFields();
    setSearchable(true);
  };
  
  
  if (isLoading) return <Loading active transparent />;
  return (
    <div className="member-activities">
      <AntForm
        form={formFilter}
        size={"large"}
        className={"member-activities__filter-form"}
        onFinish={onFilterSubmit}
        onValuesChange={onFormChange}
      >
        <Row gutter={24} className="grow">
          <Col xs={24} md={12} lg={8} xl={6}>
            <AntForm.Item name={"date"}>
              <DatePicker
                placeholder={t("DATE")}
                size="large"
                className="filter-form__date-picker"
                format="DD/MM/YYYY"
              />
            </AntForm.Item>
          </Col>
          <Col xs={24} md={12} lg={8} xl={6}>
            <AntForm.Item name={"member"}>
              <Select
                placeholder={t("MEMBER")}
                options={allMembers}
                fieldNames={{ label: "fullName", value: "_id" }}
                allowClear
              />
            </AntForm.Item>
          </Col>
          <Col xs={24} md={12} lg={8} xl={6}>
            <AntForm.Item name={"action"}>
              <Select
                placeholder={t("ACTION")}
                options={getActivitiesActionOptions()}
                mode="multiple"
                allowClear
              />
            </AntForm.Item>
          </Col>
        </Row>
        <div className="filter-form__actions">
          <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>{t("CLEAR")}</AntButton>
          <AntButton type={BUTTON.DEEP_NAVY} htmlType={"submit"} disabled={!isSearchable}>{t("SEARCH")}</AntButton>
        </div>
      </AntForm>
      {!filteredData.length
        ? <NoData />
        : (Object.entries(groupByDate).map(([key, value]) => {
          return (
            <ActivitiesOfDay key={key} activities={value} title={t(key)} />
          );
        }))
      }
    </div>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps, {})(MemberActivities);