.assign-list-container {
  position: relative;
  max-width: 815px;
  flex: 1;
  padding-left: 32px;
  display: flex;
  flex-direction: column;
  gap: 16px;


  &:before {
    content: '';
    width: 8px;
    height: 100%;
    //gap: 0px;
    border-radius: 8px 0 0 8px;

    background-color: var(--cobalt);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
  }

  .assign-list-header {
    display: flex;
    flex-direction: row;

    .assign-list-header__title {
      font-size: 24px;
      font-weight: 700;
      color: var(--navy);
    }

    .assign-list-header__count {
      font-size: 24px;
      padding-left: 6px;
      color: var(--navy);
    }

    .assign-list-header__filter {
      margin-left: auto;
      height: 30px;

      .ant-btn {
        margin-top: -1px;

        .ant-btn-icon svg {
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  .assign-list-body {

  }

  .assign-list-footer {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;

    .ant-pagination {
      display: flex;
      flex-direction: row;
      gap: 4px;

      .ant-pagination-prev, .ant-pagination-next {
        .ant-btn {
          width: 24px;
          height: 24px;
          border-radius: 8px;

          .ant-btn-icon svg {
            width: 12px;
            height: 12px;
          }
        }

        &.ant-pagination-disabled .ant-btn {
          cursor: not-allowed;
        }
      }

      .ant-pagination-item {
        border-radius: 8px;
        font-size: 12px;
        font-weight: 500;

        a {
          color: var(--cobalt);
        }

        &.ant-pagination-item-active {
          font-weight: 700;
          background-color: #E7E5FF;
          border-color: #E7E5FF;
        }
      }
    }
  }

}