# YÊU CẦU PHẦN MỀM (FRONTEND) - DICTATION & SHADOWING

## 1. G<PERSON><PERSON><PERSON> thiệu
### 1.1 <PERSON><PERSON><PERSON> đích
Tài liệu này mô tả yêu cầu cho việc phát triển giao diện người dùng (frontend) của hai tính năng Dictation và Shadowing trong Clickee.

### 1.2 Phạm vi
Tính năng Dictation giúp người dùng luyện tập kỹ năng nghe và viết, trong khi tính năng Shadowing hỗ trợ việc luyện nói và phát âm.

## 2. <PERSON><PERSON>u cầu giao diện người dùng
### 2.1 Dictation
#### 2.1.1 Giao diện bài tập Dictation
- **Chọn topic**: Hiển thị danh sách topic và cột sort theo trình độ (A1 - C2).
- **Trình phát audio**:
  - <PERSON><PERSON>t Play/Pause/Seek.
  - <PERSON><PERSON><PERSON> thị thời gian audio.
  - <PERSON><PERSON> chọn tốc độ nghe (0.5x, 1x, 1.5x, 2x).
- **Chế độ Dictation**:
  - Dropdown chọn Word Mode, Sentence Mode, Mixed Mode.
  - Hiển thị câu hỏi dưới dạng cloze test.
  - Trường input để nhập đáp án.
- **Phản hồi**:
  - Màu xanh cho đúng, màu đỏ cho sai.
  - Hiển thị đáp án khi sai (nếu bật tùy chọn).
- **Transcript**:
  - Checkbox bật/tắt transcript khi hoàn thành một câu.
- **Điều hướng**:
  - Nút qua lại câu.
  - Bộ đếm tiến trình bài tập.

#### 2.1.2 Ràng buộc kỹ thuật
- Trường nhập giới hạn 100 từ.
- Chấp nhận viết hoa/thường, bỏ qua dấu câu.
- Hệ thống lưu tiến trình khi thoát giữa chừng.

### 2.2 Shadowing
#### 2.2.1 Giao diện bài tập Shadowing
- **Trình phát audio**:
  - Nút Play/Pause/Seek.
  - Hiển thị thời gian audio.
  - Tuỳ chọn tốc độ nghe và tải xuống audio.
- **Hiển thị nội dung**:
  - Câu mẫu hiển thị rõ ràng.
  - Nút ẩn/hiện câu mẫu.
- **Ghi âm**:
  - Nút ghi âm màu đỏ nổi bật.
  - Trình điều khiển ghi âm (Play, Pause, Seek).
  - Hiển thị text từ bản ghi âm và so sánh với bài mẫu.
- **Điểm Accuracy Score**:
  - Hiển thị điểm độ chính xác.
  - Có thể nghe lại bản ghi.
- **Phản hồi**:
  - Màu xanh cho điểm cao, đỏ cho điểm thấp.
- **Điều hướng**:
  - Nút qua lại câu.
  - Bộ đếm tiến trình.

#### 2.2.2 Ràng buộc kỹ thuật
- Ghi âm tối đa 2 phút.
- Tự động dừng sau 5 giây nếu không phát hiện âm thanh.
- Tự động cắt khoảng im lặng.
- Lưu lại điểm Accuracy Score trong lịch sử.

## 3. UI/ Màn hình Prototype
- Màn hình topic tổng.
- Màn hình bài tập Dictation và Shadowing.
- Màn hình kết quả sau khi luyện tập.

## 4. Cơ chế lưu dữ liệu
- Tiến trình bài tập lưu lại khi thoát.
- Điểm Accuracy Score lưu vào lịch sử.
- Nếu luyện tập lại, điểm số cũ sẽ bị thay thế.

