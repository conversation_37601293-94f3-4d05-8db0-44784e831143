.total-topic-screen {
  padding: 32px 16px;
  border-radius: 24px;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #f4f3ff 0.01%, #ffffff 100%);
  box-shadow: 0 4px 20px 0 #0000001a;
  display: flex;
  justify-content: center;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;

  &__container {
    min-height: 100vh;
    max-width: 928px;
    flex: 1;
  }

  &__title {
    margin-bottom: 24px;

    h1 {
      font-family: Inter;
      font-size: 28px;
      font-weight: 700;
      color: #09196b;
      margin: 0;
    }
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
  }

  &__search-container {
    display: flex;
    gap: 16px;
  }

  &__search-input {
    border-radius: 16px;
    border: 1px solid #dbdbdb;
    padding: 12px 16px;
    width: 456px;

    @media screen and (max-width: 1024px) {
      width: 298px;
    }

    .ant-input {
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      color: #09196b;

      &::placeholder {
        color: #09196b;
      }
    }
  }

  // Style for the custom search icon used as a suffix
  &__search-icon {
    width: 16px; // Adjust size as needed
    height: 16px; // Adjust size as needed
    // Add any other styles like color if the SVG doesn't have inherent fill/stroke
  }

  &__level-select {
    width: 220px;
    height: 48px;

    .ant-select-selector {
      padding: 12px 16px;
      border-radius: 16px;
      border: 1px solid #dbdbdb !important;
      color: #09196b !important;

      .ant-select-selection-item {
        font-size: 16px;
        font-weight: 500;
        color: #09196b !important;
        line-height: 20px;
      }

      .ant-select-selection-placeholder {
        color: #09196b;
      }
    }

    .ant-select-arrow {
      // Hide the default Ant Design icon
      .anticon-down {
        display: none;
      }

      // Add custom icon using ::after pseudo-element
      &::after {
        content: '';
        display: block;
        width: 24px; // Adjust size as needed
        height: 24px; // Adjust size as needed
        background-image: url('~@src/assets/icons/selector-arrow.svg');
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
      }
    }
  }

  &__sort-button {
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;
    background: none;
    border-radius: 16px;
    padding: 12px 16px;
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
    color: #09196b;
  }

  &__sort-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
    vertical-align: middle;
  }

  &__topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }

  &__topic-card {
    border-radius: 24px;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;

    .ant-card-body {
      width: 100%;
      padding: 16px;
      // height: calc(100% * 318 / 220);
      // aspect-ratio: 220 / 318;
    }

    .topic-image {
      object-fit: cover;
      width: 100%;
      height: calc(100% * 106 / 188);
      aspect-ratio: 188 / 106;
      border-radius: 16px;
    }

    .topic-title {
      font-family: Inter;
      font-size: 18px;
      font-weight: 600;
      color: #09196b;
      margin: 16px 0 8px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      height: calc(2 * 1.2em); /* Cố định chiều cao là 2 dòng text */
    }

    .topic-level {
      font-family: Inter;
      font-size: 14px;
      color: #000000;
      margin-bottom: 16px;
    }
  }

  &__action-button {
    width: 100%;
    height: auto;
    padding: 8px;
    border-radius: 12px;
    font-family: Inter;
    font-size: 16px;
    font-weight: 600;

    &--dictation {
      background: #09196b;
      color: white;

      &:hover,
      &:focus {
        background: #0c2285;
        color: white;
      }
      margin-bottom: 8px;
    }

    &--shadowing {
      background: #26d06d;
      color: white;

      &:hover,
      &:focus {
        background: #1fb85f;
        color: white;
      }
    }
  }

  &__load-more {
    margin-top: 32px;
    display: flex;
    justify-content: center;
  }

  &__load-more-button {
    min-width: 180px;
    height: 48px;
    padding: 12px 24px;
    border-radius: 16px;
    background: #09196b;
    color: white;
    font-family: Inter;
    font-size: 16px;
    font-weight: 600;

    &:hover,
    &:focus {
      background: #0c2285;
      color: white;
    }
  }
}

.ant-select-dropdown {
  .ant-select-item {
    // color: #09196B !important; // Remove this general one

    .ant-select-item-option-content {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0px;
      color: #09196b; // Keep this one, remove !important
    }

    &.ant-select-item-option-active {
      background-color: #e7e5ff;

      .ant-select-item-option-content {
        color: #09196b !important;
      }
    }

    &.ant-select-item-option-selected {
      background-color: var(--primary-colours-blue-navy);

      .ant-select-item-option-content {
        color: #ffffff !important; // Keep important for selected override
      }
    }
  }
}
