import React from 'react';
import PropTypes from 'prop-types';

import './TranscriptDisplay.scss';

const TranscriptDisplay = ({ questions, activeTranscriptIndex, transcriptContainerRef, activeItemRef }) => {
  return (
    <div className="transcript-container" ref={transcriptContainerRef}>
      {questions.map((question, index) => (
        <div key={index}>
          <div
            className={`transcript-item ${index === activeTranscriptIndex ? 'active' : 'inactive'}`}
            ref={index === activeTranscriptIndex ? activeItemRef : null}
          >
            <p>{index + 1}</p>
            <p>{question.transcript}</p>
          </div>
          {index !== questions.length - 1 && <div className="transcript-divider"></div>}
        </div>
      ))}
    </div>
  );
};

TranscriptDisplay.propTypes = {
  questions: PropTypes.arrayOf(
    PropTypes.shape({
      transcript: PropTypes.string.isRequired,
    })
  ).isRequired,
  activeTranscriptIndex: PropTypes.any,
  transcriptContainerRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({ current: PropTypes.instanceOf(Element) })]),
  activeItemRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({ current: PropTypes.instanceOf(Element) })]),
};

export default TranscriptDisplay;
