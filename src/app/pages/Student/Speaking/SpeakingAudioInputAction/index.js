import { useSpeaking } from "@app/pages/Student/Speaking";
import { BUTTON, CONSTANT, RECOGNIZE_STATUS } from "@constant";
import AntButton from "@component/AntButton";
import Voice20 from "@component/SvgIcons/Voice/Voice20";
import React from "react";
import { useTranslation } from "react-i18next";
import SpeakingUpload from "@component/SvgIcons/Upload/SpeakingUpload";
import { actions, PARAM_CATEGORIES, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";
import { useDispatch } from "react-redux";


function SpeakingAudioInputAction() {
  const { t } = useTranslation();
  const { speakingForm, recognizeStatus } = useSpeaking();
  
  const { audioInputType, setAudioInputType } = useSpeaking();
  const { setShowUpload, disabledEdit } = useSpeaking();
  const dispatch = useDispatch();
  
  
  function handleSwitchToRecord() {
    setAudioInputType(CONSTANT.RECORD);
  }
  
  async function handleShowUploadFile() {
    dispatch(actions.trackCustomClickType(TRACKING_ACTIONS.CLICK_UPLOAD, undefined, PARAM_CATEGORIES.UPLOAD_ESSAY));
    try {
      const { errorFields } = await speakingForm.validateFields();
      if (!!errorFields?.length) return;
      setShowUpload(true);
    } catch (e) {
      //console.log(e)
    }
  }
  
  return <>
    {audioInputType === CONSTANT.RECORD &&
      <AntButton
        size="xsmall"
        icon={<SpeakingUpload />}
        type={BUTTON.DEEP_BLUE}
        disabled={recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING || disabledEdit}
        onClick={handleShowUploadFile}
      >
        {t("UPLOAD_FILE")}
      </AntButton>}
    
    
    {audioInputType === CONSTANT.FILE &&
      <AntButton
        size="xsmall"
        icon={<Voice20 />}
        type={BUTTON.DEEP_RED}
        disabled={recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING || disabledEdit}
        onClick={handleSwitchToRecord}
      >
        {t("RECORD")}
      </AntButton>}
  </>;
}

export default SpeakingAudioInputAction;