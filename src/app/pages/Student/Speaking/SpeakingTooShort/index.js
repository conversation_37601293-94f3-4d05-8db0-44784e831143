import { useSpeaking } from "@app/pages/Student/Speaking";
import {useTranslation} from "react-i18next";

import SHAPES_DESIGN from "@src/asset/icon/shapes-design.svg";

import "./SpeakingTooShort.scss";
import { RECOGNIZE_STATUS } from "@constant";

function SpeakingTooShort() {
  const {t} = useTranslation();
  
  const { isAudioTooShort, isContentTooShort, recognizeStatus } = useSpeaking();
  
  
  if ((!isAudioTooShort && !isContentTooShort) || recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING) return null;
  
  return <div className="speaking-too-short">
    <div className="speaking-too-short__icon">
      <img src={SHAPES_DESIGN} alt="" />
    </div>
    <div className="speaking-too-short__content">
      <div className="speaking-too-short__content-1">
        {t("SPEAKING_TO_SHORT_CONTENT_1")}
      </div>
      <div className="speaking-too-short__content-2">
      {t("SPEAKING_TO_SHORT_CONTENT_2")}
      </div>
    </div>
  </div>;
}

export default SpeakingTooShort;