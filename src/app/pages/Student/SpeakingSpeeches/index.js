import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";
import Loading from "@component/Loading";
import StudentBreadcrumb from "@app/layout/StudentLayout/StudentBreadcrumb";
import Share from "@component/Share";
import MyAssign from "@app/pages/Student/MyAssign";

import { CONSTANT, TYPE_OF_TOOL } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { calPagingAfterDelete, cloneObj } from "@common/functionCommons";
import { deleteProject, getProjectByStudent, getProjectDetail } from "@services/Project";

import "./SpeakingSpeeches.scss";

function SpeakingSpeeches() {
  const { t } = useTranslation();
  
  const [isLoading, setLoading] = useState(false);
  const [speechesData, setSpeechesData] = useState({
    rows: [],
    paging: {
      page: 1,
      pageSize: 5,
      total: 0,
      totalPages: 0,
    },
    query: { sortType: CONSTANT.LATEST, documentType: "" },
  });
  
  const [shareState, setShareState] = useState({
    isShowModal: false,
    projectData: null,
  });
  
  function handleShowModal(isShowModal = false, projectData = null) {
    if (isShowModal) {
      setShareState({ isShowModal, projectData });
    } else {
      setShareState(prevState => Object.assign({}, prevState, { isShowModal }));
    }
  }
  
  async function getSpeakingSpeechesData(
    paging = speechesData.paging,
    query = speechesData.query,
  ) {
    setLoading(true);
    const queryObj = {
      inputType: `${TYPE_OF_TOOL.AUDIO_STREAM}, ${TYPE_OF_TOOL.STUDENT_SPEAKING}`,
      sort: query.sortType === CONSTANT.LATEST ? "-createdAt" : "createdAt",
    };
    if (query.documentType) queryObj.documentType = query.documentType;
    if (query.tag) queryObj.tag = query.tag;
    
    const apiResponse = await getProjectByStudent(paging, queryObj);
    if (apiResponse) {
      setSpeechesData(handlePagingData(apiResponse, query));
    }
    setLoading(false);
  }
  
  function onChangePage(page) {
    getSpeakingSpeechesData(Object.assign({}, speechesData.paging, { page }));
  }
  
  function onChangeSort(type) {
    getSpeakingSpeechesData(speechesData.paging, { sortType: type });
  }
  
  function onChangeFilter(dataChange) {
    if (!dataChange) return;
    const query = cloneObj(speechesData.query);
    if (dataChange.hasOwnProperty("documentType")) {
      if (dataChange.documentType?.length === 1) {
        query.documentType = dataChange.documentType[0];
      } else {
        delete query.documentType;
      }
    }
    if(dataChange.tags?.length) {
      query.tag = dataChange.tags.join(",");
    } else {
      delete query.tag;
    }
    getSpeakingSpeechesData({ ...speechesData.paging, page: 1 }, query);
  }
  
  function onDeleteAssign(projectId) {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const apiResponse = await deleteProject(projectId);
        if (apiResponse) {
          await getSpeakingSpeechesData(calPagingAfterDelete(speechesData));
          toast.success("DELETE_PROJECT_SUCCESS");
        }
      },
    });
  }
  
  async function onShareAssign(projectId) {
    const apiResponse = await getProjectDetail(projectId);
    if (apiResponse.code === 200) {
      handleShowModal(true, apiResponse.data);
    }
    
  }
  
  return <>
    <Loading active={isLoading} className="speaking-speeches-container">
      <StudentBreadcrumb />
      
      <MyAssign
        title={t("ALL_SPEECHES")}
        studentProjectType={CONSTANT.SPEAKING}
        total={speechesData.paging.total}
        currentPage={speechesData.paging.page}
        dataSource={speechesData.rows}
        sortType={speechesData.query.sortType}
        onChangePage={onChangePage}
        onChangeSort={onChangeSort}
        onDeleteAssign={onDeleteAssign}
        onShareAssign={onShareAssign}
        onChangeFilter={onChangeFilter}
      />
    </Loading>
    
    
    <Share
      isShowModal={shareState.isShowModal}
      handleCancel={handleShowModal}
      queryAccess={{ projectId: shareState.projectData?._id }}
      name={shareState.projectData?.projectName}
      owner={shareState.projectData?.ownerId}
      workspaceId={shareState.projectData?.workspaceId}
      disabledEdit
    />
  </>;
}

export default SpeakingSpeeches;