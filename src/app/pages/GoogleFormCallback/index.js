import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import queryString from "query-string";

import Loading from "@component/Loading";

import { CONSTANT } from "@constant";
import { LINK } from "@link";

import { oauthGoogleForm } from "@services/Google";


function GoogleFormCallback() {
  const location = useLocation();
  const navigate = useNavigate();
  
  useEffect(() => {
    handleGoogle();
  }, []);
  
  async function handleGoogle() {
    localStorage.removeItem(CONSTANT.GOOGLE_FORM);
    const queryObj = queryString.parse(location.search);
    if (queryObj.code) {
      const apiResponse = await oauthGoogleForm({ code: queryObj.code });
      localStorage.setItem(CONSTANT.GOOGLE_FORM, apiResponse ? CONSTANT.SUCCESS : CONSTANT.ERROR);
    } else {
      localStorage.setItem(CONSTANT.GOOGLE_FORM, CONSTANT.ERROR);
    }
    window.close();
    navigate(LINK.HOMEPAGE, { replace: true });
  }
  
  return <Loading active />;
}

export default GoogleFormCallback;