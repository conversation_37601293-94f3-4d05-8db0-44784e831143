.grid-container {
  left: -24px;
  top: -24px;

  .ReactVirtualized__Grid,
  .ReactVirtualized__Grid__innerScrollContainer {
    overflow: visible !important;
  }

  .grid-item {
    .grid-item-wraper {
      border-radius: 16px;
      position: relative;
      background: var(--background-light-background-2);
      box-shadow: var(--shadow-level-3);

      &:hover {
        .grid-item__starred .star-container {
          opacity: 1;
        }
      }

      &:hover,
      &:has(.ant-dropdown-open) {
        .grid-item__content {
          background: var(--primary-colours-blue-navy-light-1);
        }
      }

      .grid-item__info__grid-item-description {
        display: flex;
        justify-content: space-between;

        .grid-item__starred {
          display: flex;
          justify-content: center;
          width: 24px;
        }
      }

      .grid-item__actions {
        position: absolute;
        top: 24px;
        right: 24px;
        cursor: pointer;
        display: flex;
      }

      .grid-item__content {
        border-radius: 16px;

        display: flex;
        align-items: flex-start;
        gap: 8px;
        padding: 24px;
        height: 100%;

        &:active {
          &:not(:has(.grid-item__starred:active)) {
            background: var(--primary-colours-blue-navy-light-2);
          }
        }

        .grid-item__info {
          display: flex;
          flex-direction: column;
          gap: 8px;
          color: #6486A1;
          font-size: 14px;
          font-weight: 400;
          height: 100%;
          width: 100%;

          .grid-item__info__grid-item-name {
            color: var(--typo-colours-support-blue-dark);
            font-size: 16px;
            word-break: break-word;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            height: 1lh;
            padding-right: 32px;
          }
        }
      }
    }
  }
}