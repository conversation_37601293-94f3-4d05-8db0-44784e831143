
import { Dropdown } from "antd";
import { useTranslation } from "react-i18next";
import { useWorkspace } from './'

import { BUTTON } from "@constant";

import AntButton from "@src/app/component/AntButton";

import MoreVerticalIcon from "@component/SvgIcons/MoreVertical";
import Copy from "@component/SvgIcons/Copy";
import ShareIcon from "@component/SvgIcons/ShareIcon";
import Edit from "@component/SvgIcons/Edit";
import Trash from "@component/SvgIcons/Trash";
import DownloadRotate from "@component/SvgIcons/DownloadRotate";

const ActionDropdown = ({ dataItem }) => {
  const {
    handleCopy,
    toggleMove,
    toggleShare,
    toggleRename,
    handleDelete,
  } = useWorkspace();
  const { t } = useTranslation();

  const isFolder = dataItem?.folderName;

  let items = [
    { key: "COPY", label: t("COPY"), icon: <Copy />, onClick: () => handleCopy(dataItem) },

    { key: "SHARE", label: t("SHARE"), icon: <ShareIcon />, onClick: () => toggleShare(dataItem) },
    { key: "RENAME", label: t("RENAME"), icon: <Edit />, onClick: () => toggleRename(dataItem) },
    { key: "DELETE", label: t("DELETE"), icon: <Trash />, onClick: () => handleDelete(dataItem) },
  ];

  if (!isFolder) items = [
    items[0],
    { key: "MOVE", label: t("MOVE"), icon: <DownloadRotate />, onClick: () => toggleMove(dataItem?._id) },
    ...items.slice(1)
  ];
  return (<div className="grid-item__actions">
    <Dropdown
      menu={{
        items: items,
        className: 'action-dropdown-menu'
      }}
      trigger={["click"]}
    >
      <AntButton
        size='xsmall'
        fullIcon
        type={BUTTON.GHOST_WHITE}
        icon={<MoreVerticalIcon />}
      />
    </Dropdown >
  </div>)
}
export default ActionDropdown;