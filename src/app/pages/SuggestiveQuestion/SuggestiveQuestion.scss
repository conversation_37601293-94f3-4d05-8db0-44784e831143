.suggest-question-container {
  background-color: var(--background-light-background-1);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .suggest-question-content {
    background-color: var(--background-light-background-2);
    width: 466px;
    padding: 24px;
    display: flex;
    flex-direction: column;

    gap: 24px;
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);


    .suggest-question__logo {
      display: flex;
      justify-content: center;

      img {
        height: 80px;
      }
    }

    .suggest-question__question {
      font-size: 20px;
      font-weight: 600;
    }

    .suggest-question__answer {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .suggest-question__submit {
      display: flex;
      justify-content: center;
    }
  }

}