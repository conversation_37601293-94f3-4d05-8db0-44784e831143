import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { useResource } from "@app/pages/Resource";

import Loading from "@component/Loading";
import ResourceVideo from "./ResourceVideo";
import ResourceImage from "./ResourceImage";
import ResourceAudio from "./ResourceAudio";
import ResourceDocument from "./ResourceDocument";
import ResourceCapacity from "@app/pages/Resource/ResourceContent/ResourceCapacity";

import { RESOURCE_LEGEND } from "@app/pages/Resource/ResourceContent/ResoureceConstant";

import "./ResourceContent.scss";

function ResourceContent() {
  const { t } = useTranslation();
  const { isFirst, resourceTypeCapacity, capacityLimit, getCapacityData } = useResource();
  const { calCapacity } = useResource();
  
  const [usedCapacity, percentUsedCapacity] = useMemo(() => {
    const totalCap = calCapacity(resourceTypeCapacity);
    const percentUsed = capacityLimit ? (totalCap / capacityLimit * 100).fixedFloat(2) : 0;
    return [totalCap, percentUsed];
  }, [resourceTypeCapacity]);
  
  if (isFirst) return <Loading active transparent />;
  return <div className="resource-content">
    <div className="resource-content__list">
      <ResourceVideo />
      <ResourceImage />
      <ResourceAudio />
      <ResourceDocument />
    
    </div>
    <div className="resource-content__info">
      <div className="resource-content__info-inner">
        <div className="resource-content__info-header">
          <div className="resource-content__info-title">
            {t("DATA_CAPACITY")}
          </div>
        </div>
        
        <ResourceCapacity
          video={resourceTypeCapacity?.videoSizes || 0}
          audio={resourceTypeCapacity?.audioSizes || 0}
          image={resourceTypeCapacity?.imageSizes || 0}
          document={resourceTypeCapacity?.documentSizes || 0}
          quota={capacityLimit}
        />
        
        
        <div className="resource-content__info-header">
          <div className="resource-content__info-title">
            {usedCapacity.fixedFloat(2)} / {capacityLimit} MB
          </div>
          <div>
            {t("OCCUPIES_THE_CAPACITY_PROVIDED_IN_THE_USAGE_SERVICE_PACKAGE").format(percentUsedCapacity)}
          </div>
        </div>
        
        
        <div className="resource-capacity-statistics">
          <div className="font-semibold">
            {t("CAPACITY_STATISTICS")}
          </div>
          <div className="resource-capacity__detail">
            {RESOURCE_LEGEND.map((resource) => {
              if (resource.icon)
                return <div
                  key={resource.key}
                  className={`resource-capacity__item resource-capacity__item-${resource.key.toLowerCase()}`}
                >
                  <div className="resource-capacity__item-icon">
                    <img src={resource.icon} alt="" />
                  </div>
                  <div className="resource-capacity__item-text">
                    {t(resource.lang)}
                  </div>
                  <div className="resource-capacity__item-capacity">
                    {resourceTypeCapacity?.[resource.key.toLowerCase() + "Sizes"]} MB
                  </div>
                </div>;
            })}
          </div>
        </div>
      </div>
    </div>
  </div>;
}

export default ResourceContent;