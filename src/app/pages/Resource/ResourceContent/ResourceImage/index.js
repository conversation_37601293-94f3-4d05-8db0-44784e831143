import React, { useEffect, useMemo, useState } from "react";
import { Dropdown, Pagination, Popover } from "antd";
import { useTranslation } from "react-i18next";

import { useResource } from "@app/pages/Resource";

import NoData from "@component/NoData";
import AntButton from "@component/AntButton";
import ImagePreviewModal from "@component/ImagePreviewModal";

import { BUTTON, CONSTANT } from "@constant";
import { API } from "@api";

import MoreVertical from "@component/SvgIcons/MoreVertical";
import Trash from "@component/SvgIcons/Trash";

import { formatTimeDate } from "@src/common/functionCommons";

import "./ResourceImage.scss";

function ResourceImage() {
  const { t } = useTranslation();
  
  const { resourceData, resourceTypeActive, resourceSearchValue, handleDeleteResource } = useResource();
  
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 8;
  
  const [statePreviewImage, setStatePreviewImage] = useState({
    isShowModal: false,
    imageFileId: null,
  });
  
  useEffect(() => {
    if (resourceTypeActive === CONSTANT.IMAGE && currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [resourceSearchValue]);
  
  const dataSource = useMemo(() => {
    if (!resourceData?.[CONSTANT.IMAGE]) return [];
    if (!resourceSearchValue) return resourceData[CONSTANT.IMAGE];
    return resourceData[CONSTANT.IMAGE]?.filter(x => x?.name?.toLowerCase()?.includes(resourceSearchValue?.toLowerCase()));
  }, [resourceData, resourceSearchValue]);

  useEffect(() => {
    if (dataSource?.length && (dataSource.length / pageSize) === (currentPage - 1)) {
      setCurrentPage(currentPage - 1);
    }
  }, [dataSource]);
  
  const resourceImagePagination = useMemo(() => {
    if (!Array.isArray(dataSource)) return [];
    return dataSource.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  }, [dataSource, currentPage, pageSize]);
  
  function handleShowPreviewImage(isShowModal, imageFileId = null) {
    if (isShowModal) {
      setStatePreviewImage({ isShowModal, imageFileId });
    } else {
      setStatePreviewImage(prevState => Object.assign({}, prevState, { isShowModal }));
    }
  }
  
  if (resourceTypeActive !== CONSTANT.IMAGE) return null;
  if (!dataSource?.length) return <NoData />;
  return <>
    <div className="resource-image-container">
      {resourceImagePagination.map(item => {
        return <div key={item._id} className="resource-image-item">
          <div
            className="resource-image__preview"
            onClick={() => handleShowPreviewImage(true, item?.imageId?.imageFileId._id || item?.imageId?.imageFileId)}
          >
            <img
              className=""
              src={API.STREAM_ID.format(item?.imageId?.thumbnailFileId?._id || item?.imageId?.thumbnailFileId)}
              alt=""
            />
          </div>
          <div className="resource-image__footer">
            <div className="resource-video__info">
              <div className="resource-image__title">
                <Popover className="line-clamp-2" placement="topLeft" content={item.name} trigger="hover">
                  {item.name}
                </Popover>
              </div>
              <div className="resource-image__update-at">
                {`${t("UPDATE_AT")}: ${formatTimeDate(item?.updatedAt)}`}
              </div>
            </div>
            <div className="resource-image__action">
              <Dropdown
                menu={{
                  items: [{
                    key: "DELETE",
                    label: t("DELETE"),
                    icon: <Trash />,
                    onClick: () => handleDeleteResource(item),
                  }],
                  className: "action-dropdown-menu",
                }}
                trigger={["click"]}
                placement="bottomRight"
              >
                <AntButton
                  size="tiny"
                  type={BUTTON.GHOST_WHITE}
                  icon={<MoreVertical />}
                  onClick={(e) => e.stopPropagation()}
                />
              </Dropdown>
            </div>
          </div>
        
        </div>;
      })}
    </div>
    
    {dataSource.length > pageSize && <div className="resource-content__pagination">
      <Pagination
        current={currentPage}
        total={dataSource.length}
        pageSize={pageSize}
        onChange={setCurrentPage}
      />
    </div>}
    
    
    <ImagePreviewModal
      isOpen={statePreviewImage.isShowModal}
      imageFileId={statePreviewImage.imageFileId}
      handleCancel={() => handleShowPreviewImage(false)}
    />

  </>;
}

export default ResourceImage;