import React from "react";
import { Column } from "@ant-design/charts";
import { useTranslation } from "react-i18next";

ChartTotalSubmit.propTypes = {};
ChartTotalSubmit.defaultProps = {
  xField: "type",
  yField: "amount",
};

function ChartTotalSubmit({ data, xField, yField }) {
  const { t } = useTranslation();
  const dataTranslate = data?.map((item) => {
    return {
      ...item,
      type: t(item[xField]),
      name: t(item?.name),
    };
  });
  const config = {
    data: dataTranslate,
    isGroup: true,
    xField: 'type',
    height: 400,
    dodgePadding: 2,
    xAxis: {
      label: {
        autoRotate: true,
      },
    },
    minColumnWidth: 40,
    maxColumnWidth: 40,
    yField: yField,
    yAxis: {
      label: {
        formatter: (value) => `${value}`,
      },
    },
    seriesField: "name",
    color: ["#6294FA", "#63DAAB"],
    
    
  };
  return <Column {...config}  />;
}

export default ChartTotalSubmit;