import React, { useMemo } from "react";
import { Column } from "@ant-design/charts";
import { useTranslation } from "react-i18next";

ChartStaticUsage.propTypes = {};
ChartStaticUsage.defaultProps = {
  xField: "type",
  yField: "value",
};

function ChartStaticUsage({ data, xField, yField }) {
  const { t } = useTranslation();
  const dataTranslate = data?.map((item) => {
    return {
      ...item,
      type: t(item?.type),
    };
  });
  
  const config = {
    data: dataTranslate,
    xField: xField,
    yField: yField,
    xAxis: {
      nice: true,
      label: {
        rotate: "",
        offset: 30,
        style: {
          fill: "#aaa",
          fontSize: 12,
        },
      },
    },
    minColumnWidth: 40,
    maxColumnWidth: 40,
    seriesField: "type",
    colorField: "color",
    color: ["#2196F3", "#3FDB90"],
    animation: false,
  };
  return <Column {...config}  />;
}

export default ChartStaticUsage;