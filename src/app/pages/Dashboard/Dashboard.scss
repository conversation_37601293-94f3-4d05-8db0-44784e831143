.dashboard-page-container {
  width: 100%;

  .dashboard-page-chart-remaning-container {
    display: grid;
    padding: 24px 0;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    width: 100%;
    gap: 24px;
    font-family: Segoe UI;

    .dashboard-page-chart-remaning-item {
      display: flex;
      flex-direction: column;
      padding: 24px;
      box-shadow: var(--shadow-level-2);
      border-radius: 8px;
      gap: 32px;
      background-color: var(--background-light-background-2);
    }

    .dashboard-page-chart-remaning-item-column {
      display: flex;
      flex-direction: column;
      padding: 24px;
      box-shadow: var(--shadow-level-2);
      border-radius: 8px;
      gap: 16px;
      background-color: var(--background-light-background-2);
    }

    .dashboard-page-chart-remaning-item-column-small {
      display: flex;
      flex-direction: column;

      box-shadow: var(--shadow-level-2);
      border-radius: 8px;
      gap: 16px;
      background-color: var(--background-light-background-2);
    }

    .dashboard-page-chart-remaning-info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    .dashboard-page-chart-remaning-info__left {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .dashboard-page-chart-remaning-info__right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
    }

    .dashboard-page-chart-remaning-info__title {
      font-size: 16px;
      line-height: 20px;
      font-weight: 600;
    }

    .dashboard-page-chart-remaning-info__description {
      font-weight: 400;
      font-size: 14px;
      line-height: 17.5px;
      color: var(--typo-colours-support-blue-light)
    }

    .dashboard-page-chart-remaning-info__avalibled {
      font-size: 16px;
      font-weight: 400;
      line-height: 20px;
      text-align: center;
    }

    .dashboard-page-chart-remaning-info__exp {
      display: flex;
      flex-direction: row;
    }

    .dashboard-page-chart-remaning-info__exp {
      font-weight: 400;
      font-size: 14px;
      line-height: 17.5px;
      color: var(--typo-colours-support-blue-light)
    }

    .dashboard-page-chart-remaning-info__expTitle {
      color: var(--support-colours-red)
    }

    .dashboard-page-chart-remaning-info__exp-link {
      color: var(--typo-colours-support-blue);
      cursor: pointer;
    }
  }

  .dashboard-page-filter__btnSubmit {
    display: flex;
    flex-direction: row;
    right: 0;
    position: absolute;
    justify-content: flex-end;
    float: right;

    .ant-btn-ghost-white {
      margin-right: 18px;
    }
  }

  .chart-project-dashboard__totalItem {
    display: flex;
    flex-direction: column;
    font-family: Segoe UI;
  }

  .form-filter {
    position: relative;
  }

  .chart-project-dashboard__totalNumber {
    font-weight: 400;
  }

  .dashboard-page-chart-top-tool-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .dashboard-page-chart-top-tool {
    font-family: Segoe UI;
    display: flex;
    flex-direction: column;
    gap: 8px;
    background-color: var(--background-light-background-2);
    padding: 24px;
    border-radius: 8px;
    box-shadow: var(--shadow-level-2);

    .dashboard-page-chart-top-tool__title {
      font-size: 16px;
      line-height: 20px;
      font-weight: 600;
    }
  }

  .dashboard-page-filter__tabs {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 18px;
    margin-bottom: 24px;

    .ant-btn-white {
      span {
        color: var(--typo-colours-support-blue-light);
      }
    }
  }
}