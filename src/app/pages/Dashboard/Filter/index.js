import React, { useEffect, useState } from "react";
import { Col, Form, Row, Select, DatePicker } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import dayjs from "dayjs";

import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import { handleSearchParams } from "@src/common/functionCommons";
import { getUsersOrg } from "@services/User";

import "./Filter.scss";

const Filter = ({ isOrgDashboard, user }) => {
  const [formFilter] = Form.useForm();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [isShowSelectDate, setShowSelectDate] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);
  const [listUser, setListUser] = useState([]);

  const [isSearchable, setSearchable] = useState(false);

  useEffect(() => {
    getListUserData();
  }, []);

  useEffect(() => {
    const { query } = handleSearchParams(location.search);
    handleQueryFromUrl(query);
  }, [location.search]);

  const handleQueryFromUrl = (query) => {
    const { time, fromDate, toDate } = query;

    const newQuery = {
      ...query,
      ...time ? { time } : { time: 'month' }, //set default time select
      ...fromDate ? { fromDate: dayjs(fromDate * 1000) } : {},
      ...toDate ? { toDate: dayjs(toDate * 1000) } : {}
    };

    setShowSelectDate(time === 'custom');
    setMinDate(newQuery?.fromDate);
    setMaxDate(newQuery?.toDate);
    formFilter.setFieldsValue(newQuery);
  }

  const getListUserData = async () => {
    if (isOrgDashboard) {
      const apiResponse = await getUsersOrg();
      if (apiResponse) setListUser(apiResponse);
    }
  };

  const onFilterSubmit = (values) => {
    const { fromDate, toDate } = values;
    const repareValues = {
      ...values,
      ...fromDate ? { fromDate: dayjs(fromDate)?.startOf('day')?.unix() } : {},
      ...toDate ? { toDate: dayjs(toDate)?.endOf('day')?.unix() } : {},
    }
    updateUrlQuery(repareValues);
  }

  const updateUrlQuery = (dataSearch = {}) => {
    let searchParams = new URLSearchParams();
    Object.entries(dataSearch).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    })
    navigate(`?${searchParams.toString()}`, { replace: true });
    setSearchable(false);
  }

  const clearFormFilter = () => {
    setShowSelectDate(false);
    formFilter.setFieldsValue(
      {
        time: 'month',
        fromDate: null,
        toDate: null,
        userId: null
      }
    );

    const { query } = handleSearchParams(location.search);
    const timeQuery = query?.time;
    delete query.time;
    if (timeQuery !== 'month' || Object.keys(query).length) {
      setSearchable(true);
    }
  }

  const onFormChange = () => {
    setSearchable(true);
  }

  const hanldeChangeSelectTime = (value) => {
    if (value === "custom") {
      setShowSelectDate(true);
    } else {
      setShowSelectDate(false);
      formFilter.setFieldsValue({ fromDate: null, toDate: null });
    }
  }

  const filterUserByFullName = (input, option) =>
    (option?.fullName ?? "").toLowerCase().includes(input.toLowerCase());

  return <AntForm
    form={formFilter}
    size={"large"}
    className={"filter-form"}
    onFinish={onFilterSubmit}
    onValuesChange={onFormChange}
  >
    <Row gutter={24} className="grow">
      <Col xs={24} md={12} lg={6}>
        <AntForm.Item name={"time"}>
          <Select placeholder={t("SELECT_TIME")} onChange={hanldeChangeSelectTime}>
            <Select.Option value={"week"}>{t("THIS_WEEK")}</Select.Option>
            <Select.Option value={"month"}>{t("THIS_MONTH")}</Select.Option>
            <Select.Option value={"custom"}>{t("CUSTOM")}</Select.Option>
          </Select>
        </AntForm.Item>
      </Col>
      {isShowSelectDate && <>
        <Col xs={24} md={12} lg={6}>
          <AntForm.Item
            name={"fromDate"}
            rules={[
              () => ({
                validator(_, value) {
                  if (!!value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                },
              }),
            ]}
          >
            <DatePicker
              placeholder={t("SELECT_FROM_DATE")}
              size="large"
              className="filter-form__date-picker"
              format='DD/MM/YYYY'
              maxDate={maxDate}
              onChange={setMinDate}
            />
          </AntForm.Item>
        </Col>
        <Col xs={24} md={12} lg={6}>
          <AntForm.Item
            name={"toDate"}
            rules={[
              () => ({
                validator(_, value) {
                  if (!!value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                },
              }),
            ]}>
            <DatePicker
              placeholder={t("SELECT_TO_DATE")}
              size="large"
              className="filter-form__date-picker"
              format='DD/MM/YYYY'
              minDate={minDate}
              onChange={setMaxDate}
            />
          </AntForm.Item>
        </Col>
      </>}
      {isOrgDashboard && (
        <Col xs={24} md={12} lg={6}>
          <AntForm.Item name="userId">
            <Select
              filterOption={filterUserByFullName}
              showSearch
              options={listUser}
              fieldNames={{
                label: "fullName",
                value: "_id",
              }}
              placeholder={t("MEMBER")}
              size={"large"}
              allowClear
            ></Select>
          </AntForm.Item>
        </Col>
      )}
    </Row>
    <div className={"filter-form__actions"}>
      <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>{t("CLEAR")}</AntButton>
      <AntButton type={BUTTON.DEEP_NAVY} htmlType={"submit"} disabled={!isSearchable}>{t("SEARCH")}</AntButton>
    </div>
  </AntForm>
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(Filter);