import React from "react";
import { Column } from "@ant-design/charts";
import { useTranslation } from "react-i18next";
import { Pie } from "@ant-design/plots";

ChartProjectDashboard.propTypes = {};
ChartProjectDashboard.defaultProps = {
  xField: "type",
  yField: "amount",
};

function ChartProjectDashboard({ data, xField, yField }) {
  const total = data[0]?.amount + data[1]?.amount || 0;
  const { t } = useTranslation();
  const dataTranslate = data?.map((item) => {
    return {
      ...item,
      type: t(item[xField]),
    };
  });
  
  const config = {
    appendPadding: 10,
    data: dataTranslate,
    xField: "type",
    yField,
    angleField: "amount",
    color: ["#2196F3", "#FD9F12"],
    colorField: "type",
    radius: 1,
    innerRadius: 0.6,
    
    interactions: [
      {
        type: "element-active",
      },
    
    ],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: "pre-wrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        },
        content: `<span class="chart-project-dashboard__totalItem">${t("TOTAL")} <span class="chart-project-dashboard__totalNumber">${total || 0}</span></span>`,
      },
    },
    label: false,
  };
  return <Pie {...config} />;
  
}

export default ChartProjectDashboard;