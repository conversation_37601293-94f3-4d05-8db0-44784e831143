import React, { useMemo } from "react";
import PropTypes from "prop-types";
import { Bar } from "@ant-design/charts";
import { useTranslation } from "react-i18next";

ChartTopToolUsage.propTypes = {};

function ChartTopToolUsage({ data }) {
  const { t, i18n } = useTranslation();
  const dataTranslate = data?.map((item) => {
    return {
      ...item,
      type: t(item?.type),
      name: item?.name,
    };
  });
  const config = {
    data: dataTranslate,
    xField: "amount",
    yField: "name",
    seriesField: "type",
    label: {
      position: "middle",
    },
    legend: {
      position: "top-left",
    },
  };
  return <Bar {...config} />;
  
}

export default ChartTopToolUsage;