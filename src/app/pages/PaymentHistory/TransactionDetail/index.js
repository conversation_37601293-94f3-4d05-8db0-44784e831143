import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { CONSTANT, DISCOUNT_TYPE, PACKAGE_TERM } from "@constant";

import { convertDateToText, renderMoney } from "@src/common/functionCommons";

const TransactionDetail = ({ ...props }) => {
  const { t } = useTranslation();
  const { transactionActive, renderTransactionStatus } = props;
  const unitPrice = transactionActive?.subscriptionId?.unitPrice;
  const applyDiscounts = transactionActive?.discountIds;

  const priceData = useMemo(() => {
    if (transactionActive) {
      return transactionActive?.subscriptionId?.packageId?.prices?.find(price => price?.unitName === unitPrice?.toLowerCase());
    }
    return null;
  }, [transactionActive]);

  const { total, discountValues } = useMemo(() => {
    if (priceData) {
      let money = priceData.unitAmount;
      const discounts = applyDiscounts.map(discountData => {
        if (discountData?.type === DISCOUNT_TYPE.PERCENTAGE) {
          const discountValue = (money * discountData.discount) / 100;
          money = money - discountValue;
          return { code: discountData?.code, discount: discountValue }
        } else {
          money = money - discountData?.discount;
          return { code: discountData?.code, discount: discountData?.discount }
        }
      })
      return {
        total: money,
        discountValues: discounts
      }
    }
    return { total: 0, discountValues: [] }
  }, [priceData, applyDiscounts]);

  function renderDescription() {
    return (<>
      {t('SERVICE_PACKAGE_START_FROM')}
      <span className="font-semibold">{convertDateToText(transactionActive?.subscriptionId?.startDate)}</span>
      {t('TO_DATE')}
      <span className="font-semibold">{convertDateToText(transactionActive?.subscriptionId?.endDate)}</span>
      {` (${PACKAGE_TERM[unitPrice?.toUpperCase()]} ${t("USING_DATE")})`}
    </>)
  }

  return <div className="transaction-detail">
    <div className="transaction-detail__title">{t('TRANSACTION_INFO')}</div>
    <div className="transaction-detail__content">
      <div className="transaction-detail__content__item">
        <div className="content__item__title">{t('PAYMENT_PACKAGE')}</div>
        <div className="package-info">
          <div className="package-info__name">
            {transactionActive?.subscriptionId?.packageId?.name}
            <span className="package-info__unit-price">{unitPrice?.toUpperCase() === CONSTANT.MONTH ? t('MONTHLY') : t('YEARLY')}</span>
          </div>
          <div className="package-info__term">
            {renderDescription()}
          </div>
        </div>
      </div>
      <div className="transaction-detail__content__item">
        <div className="content__item__title ">{t('PAYMENT_INFO')}</div>
        <div className="payment-info">
          <div className="payment-info__item">
            <div className="payment-info__item__label">{t('PACKAGE')}</div>
            {renderMoney(priceData?.unitAmount)}
          </div>
          <div className="payment-info__item">
            <div className="payment-info__item__label">{t('DISCOUNT_APPLIED')}</div>
            {renderMoney(priceData?.unitAmount - total)}
          </div>
          {discountValues.map(discountValue => {
            return <div className="payment-info__item payment-info__discount" key={discountValue?.code}>
              <div className="payment-info__item__label">{discountValue?.code}</div>
              {renderMoney(discountValue?.discount)}
            </div>
          })}
          <div className="payment-info__item payment-info__total">
            <div className="payment-info__item__label">{t('TOTAL_AMOUNT')}</div>
            {renderMoney(total)}
          </div>
        </div>
      </div>
      <div className="transaction-detail__content__item">
        <div className="content__item__title">{t("FORM_OF_PAYMENT")}</div>
        <div>{t('PAYMENT_VIA_VNPAY')}</div>
      </div>
      <div className="transaction-detail__content__item">
        <div className="content__item__title">{t("TRANSACTION_ID")}</div>
        <div>{transactionActive?._id}</div>
      </div>
      <div className="transaction-detail__content__item">
        <div className="content__item__title">{t("TRANSACTION_STATUS")}</div>
        {renderTransactionStatus(transactionActive?.state, transactionActive?.responseCode)}
      </div>
      <div className="transaction-detail__content__item">
        <div className="content__item__title">{t("CONTACT_SUPPORT_CLICKEE")}:</div>
        <div className="hotline-support">{t('HOT_LINE_CLICKEE')}</div>
      </div>
    </div>
  </div>
};

export default TransactionDetail;