.template-container {
  gap: 24px;
  display: flex;
  flex-direction: column;

  .template-container__header {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .template-project-type {
      display: flex;
      gap: 16px;

      button {
        padding: 15px;
        height: unset;
        line-height: 20px;

        svg {
          width: 20px;
          height: 20px;
        }

        &.project-type-active {
          svg {
            path {
              stroke: unset !important;
              fill: white;
            }
          }
        }

        &:not(.project-type-active):not(:hover) {
          background: white !important;
        }

        &:not(.project-type-active) {
          border-color: var(--background-light-background-grey) !important;
          color: unset !important;
        }
      }

    }

    .template-category {
      display: flex;
      gap: 32px;

      .template-category__item {
        color: var(--typo-colours-support-blue-dark);
        cursor: pointer;

        &.category-active {
          color: var(--primary-colours-blue-navy);
          font-weight: 600;

          &::after {
            content: '';
            width: 100%;
            height: 1px;
            background: var(--primary-colours-blue-navy);
            display: block;
            margin-top: 4px;
          }
        }
      }
    }

    .ant-form-item {
      margin-bottom: 0 !important;

      &.form-item-hidden {
        display: none;
      }
    }
  }

  .btn-search-icon {
    width: 100%;
    height: 100%;
    border: 0 !important;
    box-shadow: none;
    margin: 0 !important;
    padding: 0 !important;
  }
}