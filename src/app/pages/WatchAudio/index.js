import React, { useMemo, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import queryString from "query-string";
import ReactPlayer from "react-player";

import { API } from "@api";

import "./WatchAudio.scss";

function WatchAudio() {
  const { audioId } = useParams();
  const { search } = useLocation();
  
  const audioRef = React.useRef(null);
  
  const [playing, setPlaying] = useState(false);
  
  const { start, end } = useMemo(() => {
    return queryString.parse(search);
  }, [search]);
  
  function onProgress(state) {
    const { playedSeconds } = state;
    if (end < Math.floor(playedSeconds)) {
      audioRef.current.seekTo(start);
      setPlaying(false);
    }
  }
  
  function onDuration() {
    if (audioRef.current && start) {
      audioRef.current.seekTo(start);
    }
  }
  
  
  return <>
    <div className="watch-audio-container">
      <div className="watch-audio__player">
        <ReactPlayer
          ref={audioRef}
          url={API.STREAM_MEDIA.format(audioId)}
          controls
          playing={playing}
          width="100%"
          height="100%"
          progressInterval={0}
          onDuration={onDuration}
          onProgress={onProgress}
          onPlay={() => setPlaying(true)}
          onPause={() => setPlaying(false)}
        />
      </div>
    </div>
  </>;
}

export default WatchAudio;